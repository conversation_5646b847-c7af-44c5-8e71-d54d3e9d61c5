package com.aspentech.pme.plot.build;

import java.io.File;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.util.Hashtable;
import java.util.Enumeration;

public class ConfigGwtOutput {

	public ConfigGwtOutput() { }
	
	public void go(String folder, String jsFile, String altFileName)
	{
		try
		{
			VersionInfo versionInfo = VersionInfo.createInstance();
			Hashtable<String, String> mapName = new Hashtable<String, String>();
			
			// rename files
			File file = new File(folder);
			File [] files = file.listFiles();
			int count = 0;
			for(File htmlFile : files)
			{
				String fileName = htmlFile.getCanonicalPath();
				if(htmlFile.isDirectory() || fileName.indexOf(".cache.html") < 0) continue;
				int idxSep = fileName.lastIndexOf(File.separator);
				if(idxSep < 0) idxSep = 0;
				else
					idxSep++;
				int idxDot = fileName.indexOf(".cache.html", idxSep);
				String namePart = fileName.substring(idxSep, idxDot);
				String pathPart = fileName.substring(0, idxSep);
				mapName.put(namePart, altFileName + count);
				String newFileName = altFileName + count + ".cache.html"; 
				htmlFile.renameTo(new File(pathPart + newFileName));
				count++;
			}
			
			// update the js file
			String jsFileName = folder + File.separator + jsFile;
			BufferedReader br = new BufferedReader(new FileReader(jsFileName));
			BufferedWriter bw = new BufferedWriter(new FileWriter("tmp.js"));
			
			String line = "";
			while((line = br.readLine()) != null)
			{
				Enumeration<String> keys = mapName.keys();
				while(keys.hasMoreElements())
				{
					String oldName = keys.nextElement();
					line = line.replaceAll(oldName, mapName.get(oldName));
				}
				bw.write(line);
				bw.newLine();
			}
			br.close();
			bw.close();
			
			File oldFile = new File(jsFileName);
			File newFile = new File("tmp.js");
			oldFile.delete();
			newFile.renameTo(oldFile);
			newFile.delete();
		}
		catch(Exception e)
		{
			e.printStackTrace(System.err);
		}
	}
	
	static public void printUsage()
	{
		System.out.println("ConfigGwtOutput usage:");
		System.out.println("ConfigGwtOutput sourceFolder, jsFileName, newFileName");
	}
	
	/**
	 * @param args
	 */
	public static void main(String[] args) {
		// TODO Auto-generated method stub
		if(args.length != 3)
		{
			ConfigGwtOutput.printUsage();
			return;
		}
		ConfigGwtOutput cgo = new ConfigGwtOutput();
		cgo.go(args[0], args[1], args[2]);
	}

}
