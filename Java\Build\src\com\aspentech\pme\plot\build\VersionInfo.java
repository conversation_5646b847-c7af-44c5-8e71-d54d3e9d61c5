package com.aspentech.pme.plot.build;

public class VersionInfo {
	// the singleton instance
	private static VersionInfo versionInfo;
	
	//Version info strings
	private static final String build = "1";
	private static final String major = "7";
	private static final String minor = "3";
	private static final String patch = "";
	
	/**
	 * Constructor, use createInstance to create a VersionInfo object
	 */
	private VersionInfo() {}
	
	/**
	 * get a VersionInfo instance
	 * @return the singleton instance
	 */
	public static VersionInfo createInstance()
	{
		if(versionInfo == null)
			versionInfo = new VersionInfo();
		return versionInfo;
	}
	
	/**
	 * get the version string with major.minor.patch.build
	 * @return the version string
	 */
	public String getVersionString()
	{
		String versionString = major + "." + minor;
		if(patch != "")
			versionString += "." + patch;
		return versionString + "." + build;
	}
	
	/**
	 * Specially for renaming GWT generated files
	 * @return the string used by renaming the GWT output files
	 */
	public String getGWTFileSuffix()
	{
		return major + minor + patch;
	}
}
