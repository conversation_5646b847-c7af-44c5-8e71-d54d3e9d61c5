<!DOCTYPE html>
<html>
  <link rel="icon" href="data:;base64,iVBORw0KGgo=" />
  <link rel="shortcut icon" href="data:image/x-icon;," type="image/x-icon" />
  <head>
    <meta charset="UTF-8">
    <title>aspenONE</title>
    <meta http-equiv="X-UA-Compatible" content="chrome=1,IE=11" />
    <!-- iOS: We want the page to be scalable using regular iOS pinch gesture.
         (what is commented out below is the meta tag that *disables* the pinch gesture) -->
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1" /> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0" />
    <!-- <meta name="apple-mobile-web-app-capable" content="yes" /> -->
    <!-- feng -->

    <!-- <iframe id="idd" class="classss" src="WebControls/AtPBMPlots/AtPBMPlots.nocache.js"></iframe> -->

    <script src="aspenONECore/scripts/jquery-3.7.0.js"></script>
    <!--<script src="aspenONECore/scripts/jquery-ui.min.js"></script>-->
    <script type="text/javascript" src="aspenONECore/scripts/jquery-ui-1.13.2.js"></script>

    <link type="text/css" href="aspenONECore/styles/overcast/jquery-ui-1.8.22.custom.css" rel="stylesheet" />

    <link rel="stylesheet" href="aspenONECore/styles/Admin.css" />
    <link rel="stylesheet" href="aspenONECore/styles/ANY.css" />
    <link rel="stylesheet" href="aspenONECore/styles/ANYSearchApp.css" />
    <!--    <link rel="stylesheet" href="aspenONECore/styles/ANYPFDHubApp.css" />-->
    <link rel="stylesheet" href="aspenONECore/styles/jquery.mobile.structure-1.1.0.css" />
    <!-- feng react -->
    <link rel="stylesheet" href="aspenONECore/styles/overcast/bootstrap.min.css" />
    <link rel="stylesheet" href="static/index.css" />

    <!-- New Authentication System -->
    <script src="msal/msal-browser-4.12.0.js"></script>
    <script src="msal/authConfig.js"></script>
    <script src="msal/auth.js"></script>
    <script src="msal/shared8ScenarioAuth.js"></script>

    <script>
      // Global flags and configuration
      window.isReact = true;
      window.UserProfile = window.UserProfile || {};
      window.i18n = window.i18n || null;
      window.condition = window.condition || null;
      window.USE_JWT_AUTH = true;  // Global flag to control JWT authentication
      window.showMsalLoginBtn = false; // Global flag to auto-click login button
      
      // Context-specific overlay control flags
      window.showOverlayOnInitialLoad = false;      // Control overlay on page load (respects user preference)
      window.showOverlayOnNoToken = true;           // Show overlay when no token exists (first-time users need to login)
      window.showOverlayOnTokenExpiration = true;   // Always show on token expiry (fixes critical issue)
      window.showOverlayOn401Error = true;          // Always show on 401 Unauthorized 
      window.showOverlayOn511Error = true;          // Always show on 511 Network Auth Required      
      
      console.log('aspenONE: Authentication system loading...');
      
      // Legacy compatibility - ensure key functions are available
      // These will be properly initialized by ProcessExplorerAuth.initialize()
      window.showAuthOverlay = window.showAuthOverlay || function() { 
        console.log('showAuthOverlay placeholder - waiting for ProcessExplorerAuth initialization'); 
      };
      window.hideAuthOverlay = window.hideAuthOverlay || function() { 
        console.log('hideAuthOverlay placeholder - waiting for ProcessExplorerAuth initialization'); 
      };
      
      // Additional safeguard for page refresh scenarios
      window.addEventListener('beforeunload', function() {
        // Clear the routing flag so it can handle routing again on page reload
        window.urlRoutingHandled = false;
      });
      
      // Handle browser back/forward navigation
      window.addEventListener('popstate', function(event) {
        // Reset flag to allow URL handling on navigation
        window.urlRoutingHandled = false;
        
        // Check if we need to restore the default route
        setTimeout(function() {
          if (location.href.endsWith('/aspenONE.html') && !location.hash) {
            if (history.replaceState) {
              history.replaceState(null, null, location.href + '#/landing');
            }
          }
        }, 100);
      });

      $(document).ready(function () {
        /**
         * about click handler
         */
        function about(n) {
          var prefix = "";
          if (window.location.href.toLowerCase().includes("aspentech")) {
            prefix = "/Aspentech";
          }
          //[Scheme]://address/[Virtual directory path/]ProcessData/AtProcessDataREST.dll/[command]
          var url = window.location.origin + prefix + "/ProcessData/AtProcessDataREST.dll/Admin/ossnotes";
          console.log("url", url);
          window.open(url, "_blank");
        }

        /**
         * override function l() in ANY.min.js
         */
        window.l = function () {
          console.log("override function l()");
          var n = $("<div>");
          return (
            ANYPlatform.IPSCore.isSuite() &&
              n.append(
                $("<p>")
                  .attr("class", "title")
                  .html($.i18n("License " + (ANYPlatform.LicenseStatus || "Unauthorized")))
              ),
            ANYPlatform.UserProfile.account &&
              (n.append($("<p>").attr("class", "title").html($.i18n("Account"))),
              n.append($("<p>").attr("class", "editable").append($("<input>").attr("type", "text").attr("value", ANYPlatform.UserProfile.account)))),
            ANYPlatform.UserProfile.userName &&
              (n.append($("<p>").attr("class", "title").html($.i18n("Name"))),
              n.append($("<p>").attr("class", "editable").append($("<input>").attr("type", "text").attr("value", ANYPlatform.UserProfile.userName)))),
            ANYPlatform.UserProfile.email &&
              (n.append($("<p>").attr("class", "title").html($.i18n("Email Address"))),
              n.append($("<p>").attr("class", "editable").append($("<input>").attr("type", "text").attr("value", ANYPlatform.UserProfile.email)))),
            ANYPlatform.isAdmin == !0 &&
              ANYPlatform.LicenseStatus == "Authenticated" &&
              (n.append($("<br>")).append($("<input>").attr("type", "button").attr("value", $.i18n("Administration")).attr("id", "adminapp")),
              loadAdminJavascriptFileFlg == !0 &&
                ((loadAdminJavascriptFileFlg = !1),
                $.getScript("aspenONECore/scripts/Admin.js", function () {}),
                $.getScript("aspenONECore/scripts/aspenONEScheduleManager.js", function () {}))),
            // create about btn
            n.append($("<br>")).append($("<input>").attr("id", "about_btn").attr("type", "button").attr("value", $.i18n("About"))),
            // about button is before logout button
            n.append($("<br><br>")).append($("<input>").attr("id", "logout_btn").attr("type", "button").attr("value", $.i18n("Log Out"))),
            n.append($("<br><br>")).append(
              $("<input>")
                .attr("type", "text")
                .attr("value", $.i18n("Version") + " " + ANYPlatform.aspenONEVersion)
            ),
            n.html()
          );
        };

        /**
         * override function c() in ANY.min.js
         */
        window.c = function () {
          console.log("override function c");
          var i;
          ANYPlatform.postMessageToIntegratedClient(
            JSON.stringify({
              APIID: aspenONE.APIID.Platform_Transient_Open,
              sendAll: !0,
            })
          );
          var n = $(this),
            r = {
              left: window.innerWidth,
              top: window.innerHeight,
            },
            t = r.left - 245,
            u = n.position().left + n.width() / 2 - t;
          $(".ubar_right_tips").length > 0 && $(".ubar_right_tips").remove();
          i = {
            id: "userInfoTip",
            offset: u,
            content: window.l(),
          };
          $(Common.genTooltip(i, "top", "160")).appendTo("body");
          $("#adminapp").on("click touchstart", function () {
            ANYPlatform.setActive(adminApp);
          });
          $("#userInfoTip input[type=text]").change(function () {
            $("#userInfoTip input[value=Save]").css("display", "inline");
          });
          $("#userInfoTip").delegate('input[value="' + $.i18n("Log Out") + '"]', "click touchstart", e);
          //add about button click handler
          $("#userInfoTip").delegate('input[value="' + $.i18n("About") + '"]', "click touchstart", about);
          $("#userInfoTip #ubar_fixed_chbox").click(function () {
            $(this).is(":checked");
          });
          $("#ubar").hasClass("fixed") && $("#userInfoTip").addClass("fixed");
          $("#userInfoTip").addClass("ubar_right_tips").css("top", 50).css("left", t).fadeIn(300);
          $("#userInfoTip").click(function () {
            $("#userInfoTip").remove();
          });
          $("#userInfoTip").on("click touchstart", function (n) {
            n.stopPropagation();
          });
          return !1;
        };

        /**
         * override function e() in ANY.min.js
         */
        window.e = function (n) {
          console.log("override function e");
          if (ANYPlatform.activeApp.sdkURL) {
            ANYPlatform.activeApp.AppLogout();
            return;
          }
          ANYPlatform.Logoff(n);
        };
      });

      // URL handling with loop prevention and proper routing
      (function handleUrlRouting() {
        // Prevent multiple executions
        if (window.urlRoutingHandled) {
          return;
        }
        window.urlRoutingHandled = true;
        
        let currentUrl = location.href;
        let hasChanges = false;
        
        // Handle query parameters - convert ? to # for client-side routing
        if (currentUrl.toLowerCase().indexOf("html?") > -1) {
          currentUrl = currentUrl.replace(/\.html\?/, ".html#");
          hasChanges = true;
        }
        
        // Handle missing hash route - ensure we have #/landing for default routing
        if (currentUrl.endsWith('/aspenONE.html') || 
            (currentUrl.includes('/aspenONE.html') && !currentUrl.includes('#'))) {
          // Only add #/landing if there's no hash fragment at all
          if (!currentUrl.includes('#')) {
            currentUrl = currentUrl + '#/landing';
            hasChanges = true;
          }
        }
        
        // Apply changes using replaceState to avoid history pollution
        if (hasChanges && currentUrl !== location.href) {
          try {
            // Use history.replaceState to avoid creating new history entries
            if (history.replaceState) {
              history.replaceState(null, null, currentUrl);
            } else {
              // Fallback for older browsers
              location.replace(currentUrl);
            }
            console.log('URL routing: Updated URL from', location.href, 'to', currentUrl);
          } catch (e) {
            console.warn('URL routing: Failed to update URL', e);
            // Last resort fallback
            location.href = currentUrl;
          }
        }
      })();

      // added for Graphics Authoring, which needs to reload the initial page with no messages
      function clearBeforeUnload() {
        $(window).off("beforeunload");
      }

      function logout() {
        var sendoject = {
          IPSID: aspenONE.IPSID.Platform_Timeout,
          isIPS: true,
        };
        document.getElementById("aspenONEips").contentWindow.postMessage(sendoject, "*");
      }

      function showLicenseErrorDialog(alert, content) {
        var height = $(window).height();
        var width = $(window).width();
        content = content.split("<br/>").join("\n");

        $("#popupPanelGlass").width(width);
        $("#popupPanelGlass").height(height);
        $("#popupPanelGlass").show();
        $("#errorAlert").text(alert);
        $("#errorContent").text(content);

        $("#errorDialog").dialog({
          dialogClass: "errorDialogClass",
          title: $.i18n("Unable to check out license"),
          resizable: false,
          height: height - 50,
          width: width - 50,
          draggable: false,
          modal: false,
          closeOnEscape: false,
          closeText: "",
          close: function () {
            logout();
          },
        });
        $(".ui-dialog-titlebar-close").hide();
      }

      function hideLicenseErrorDialog() {
        $("#errorAlert").html("");
        $("#errorContent").html("");
        $("#popupPanelGlass").hide();
        $("#errorDialog").closest(".ui-dialog-content").dialog("destroy");
      }

      $(window).resize(function () {
        var show = $("#errorDialog").closest(".ui-dialog-content").dialog("isOpen");
        if (show) {
          var height = $(window).height();
          var width = $(window).width();
          $("#popupPanelGlass").width(width);
          $("#popupPanelGlass").height(height);
          $("#errorDialog")
            .closest(".ui-dialog-content")
            .dialog("option", "width", width - 50);
          $("#errorDialog")
            .closest(".ui-dialog-content")
            .dialog("option", "height", height - 50);
        }
      });

      // Authentication functions are now handled by ProcessExplorerAuth in msal/auth.js
      // All authentication overlay functions, event handlers, and initialization 
      // have been moved to dedicated classes for better maintainability.
    </script>
    <style type="text/css">
      .errorDialogClass {
        z-index: 15000 !important;
        border: rgb(182, 182, 180) solid 5px !important;
        -moz-border-radius-topright: 0px;
        -webkit-border-top-right-radius: 0px;
        -khtml-border-top-right-radius: 0px;
        border-top-right-radius: 0px;

        -moz-border-radius-topleft: 0px;
        -webkit-border-top-left-radius: 0px;
        -khtml-border-top-left-radius: 0px;
        border-top-left-radius: 0px;

        -moz-border-radius-bottomleft: 0px;
        -webkit-border-bottom-left-radius: 0px;
        -khtml-border-bottom-left-radius: 0px;
        border-bottom-left-radius: 0px;

        padding: 0px;
        display: table;
      }
      .errorDialogClass #errorMessage {
        text-align: center;
        clear: both;
        position: relative;
      }

      .errorDialogClass #errorAlert {
        text-align: left;
        white-space: pre-wrap;
        word-wrap: break-word;
        font-family: arial, verdana;
        color: red;
        font-weight: bold;
        font-size: 13px;
      }

      .errorDialogClass #errorContent {
        text-align: left;
        white-space: pre-wrap;
        word-wrap: break-word;
        color: black;
        font-family: Arimo;
        font-size: 13px;
        margin-top: 5px;
      }

      #floater {
        float: left;
        height: 50%;
        margin-bottom: -120px;
      }

      #popupPanelGlass {
        position: absolute;
        left: 0px;
        top: 0px;
        display: none;
        z-index: 14999;
        background-color: #eeeeee;
        opacity: 0.8;
        filter: alpha(opacity=20);
      }

      .errorDialogClass .ui-dialog-content {
        position: relative;
        border: 0;
        background: white;
      }

      .errorDialogClass .ui-dialog-titlebar-close span {
        margin-top: -8px;
        margin-left: -8px;
      }
      .errorDialogClass .ui-dialog-titlebar,
      .errorDialogClass .ui-widget-header,
      .errorDialogClass .ui-corner-all {
        background: #9ab7d7 !important;
        height: 20px !important;
        font-weight: bold;
        font-family: Arimo;
        font-size: 13px;
        padding: 0px;
        color: black;
      }

      .errorDialogClass .ui-corner-all {
        -moz-border-radius-topright: 0px;
        -webkit-border-top-right-radius: 0px;
        -khtml-border-top-right-radius: 0px;
        border-top-right-radius: 0px;

        -moz-border-radius-topleft: 0px;
        -webkit-border-top-left-radius: 0px;
        -khtml-border-top-left-radius: 0px;
        border-top-left-radius: 0px;

        -moz-border-radius-bottomright: 0px;
        -webkit-border-bottom-right-radius: 0px;
        -khtml-border-bottom-right-radius: 0px;
        border-bottom-right-radius: 0px;

        -moz-border-radius-bottomleft: 0px;
        -webkit-border-bottom-left-radius: 0px;
        -khtml-border-bottom-left-radius: 0px;
        border-bottom-left-radius: 0px;
      }

      .ui-menu.ui-widget.ui-widget-content.ui-autocomplete.ui-front.ui-autocomplete-panel {
        z-index: 10004 !important;
      }

      /* Authentication Overlay Styles */
      .auth-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        z-index: 20000;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .auth-container {
        max-width: 600px;
        margin: 0 20px;
        text-align: center;
      }

      .auth-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 40px;
        margin: 20px 0;
      }

      .auth-card h1 {
        color: #333;
        margin-bottom: 20px;
        font-family: Arial, sans-serif;
      }

      .auth-card p {
        color: #666;
        margin-bottom: 20px;
        font-family: Arial, sans-serif;
      }

      .auth-login-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 15px 30px;
        font-size: 18px;
        border-radius: 8px;
        cursor: pointer;
        transition: transform 0.2s;
        margin: 10px;
        font-family: Arial, sans-serif;
      }

      .auth-login-button:hover {
        transform: translateY(-2px);
      }

      .auth-login-button:disabled {
        background: #cccccc;
        cursor: not-allowed;
        transform: none;
      }

      .auth-logout-button {
        background: #dc3545 !important;
      }

      .auth-status-message {
        margin: 20px 0;
        padding: 15px;
        border-radius: 5px;
        font-weight: bold;
        font-family: Arial, sans-serif;
      }

      .auth-status-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .auth-status-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .auth-status-info {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }

      .auth-debug-section {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-top: 20px;
        text-align: left;
        font-family: Arial, sans-serif;
      }

      .auth-debug-section h3, .auth-debug-section h4 {
        color: #333;
      }

      .auth-debug-section button {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }

      .auth-debug-section pre {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 12px;
      }
    </style>
    <link type="text/css" href="assets/design-system/tokens/index.css" rel="stylesheet" />
  </head>

  <body>
    <!-- I may use jQM for any appropriate UI elements and for subpage management
        but the uBar will be custom assembled without using the jQuery Mobile Header. -->

    <!-- Authentication Overlay -->
    <div id="authOverlay" class="auth-overlay" style="display: none;">
      <div class="auth-container">
        <div class="auth-card">
          <h1>ProcessExplorer Authentication</h1>
          <p>Please sign in with your Microsoft account to access ProcessExplorer</p>
          
          <div id="authStatusMessage" class="auth-status-message" style="display: none;"></div>
          
          <div id="authLoginSection">
            <button id="authLoginBtn" class="auth-login-button">Sign In with Microsoft</button>
            <p><small>You will be redirected to Microsoft's secure login page</small></p>
          </div>
          
          <div id="authAuthenticatedSection" style="display: none;">
            <div class="auth-status-message auth-status-success">
              <p>Successfully authenticated!</p>
              <p>Welcome, <span id="authUserName">User</span></p>
            </div>
            <button id="authContinueBtn" class="auth-login-button">Continue to ProcessExplorer</button>
            <button id="authLogoutBtn" class="auth-login-button auth-logout-button">Sign Out</button>
          </div>
          
          <div id="authLoadingSection" style="display: none;">
            <div class="auth-status-message auth-status-info">
              <p>Processing authentication...</p>
            </div>
          </div>
        </div>
        
        <!-- Debug section (hidden by default) -->
        <div class="auth-debug-section" style="display: none;">
          <h3>Debug Information</h3>
          <p>Authentication Status: <span id="authDebugStatus">Checking...</span></p>
          <p>Return URL: <span id="authDebugReturnUrl">None</span></p>
          <button id="authShowDebugBtn">Show Debug Info</button>
          <div id="authDebugDetails" style="display: none;">
            <h4>Token Details:</h4>
            <pre id="authTokenContent">No token available</pre>
          </div>
        </div>
      </div>
    </div>

    <!-- feng react -->
    <div id="root" class="App-fixed"></div>

    <!-- feng global search ubar -->
    <div id="ubar" class="fixed" style="z-index: 10002; display: none">
      <div id="title_container">
        <div style="width: 45%; margin: auto; text-overflow: ellipsis; overflow: hidden">
          <span id="ubar_title"></span>
        </div>
      </div>
      <span id="ubar_search_container">
        <!-- value.length = 0 means get all content -->
        <input id="ubar_search_input" class="keyWord" type="text" placeholder="Search for Everything" value="" />
        <a id="ubar_search_history_a" class="ui-btn" href="#" role="button">
          <span class="ui-icon ui-icon-triangle-1-s"></span>
        </a>
        <a id="ubar_search_a" href="#" title="Search"></a>
      </span>
      <div id="ubar_left" style="float: left">
        <a id="home_btn" onclick="javascript: void(0)" title="Home" style="margin-left: 60px"></a>
        <!-- 565961 feng v15 -->

        <a id="AspenOne" onclick="javascript: void(0)" title="Home" style="margin-left: 35px"></a>

        <!-- back -->
        <!-- <a id="AspenOne" onclick="javascript: void(0)" title="Back" style="margin-left:7px;"></a> -->
        <!-- <img id="AspenOne" title="Back" src="aspenONECore/images/Back.svg" width=30 height=30 /> -->
        <!-- forward -->
        <!-- a id="AspenOne_forward" onclick="javascript: void(0)" title="Forward" style="margin-left:-73px;"></a -->
        <!-- <img id="AspenOne_forward" title="Forward" src="aspenONECore/images/Forward.svg" width=30 height=30 /> -->
        <!-- separator -->
        <!-- <img id="AspenOne_separator" title="" src="aspenONECore/images/Divider.svg" width=30 height=30 /> -->
        <!-- <div id="AspenOne_separator" title="" >|</div> -->
      </div>

      <div id="ubar_right" style="float: right">
        <!-- buttons go in here -->
        <div id="ubar_userName_div">
          <a href="javascript: void(0)" id="ubar_userName_a">
            <span id="ubar_userName_span"></span>
            <img id="user_pic" src="aspenONECore/images/user.svg" width="30" height="30" title="User Info" />
          </a>
          <!-- feng -->
          <!-- <a href="javascript: void(0)" id="ubar_userName_a"> <span id="ubar_userName_span"></span><img id="user_pic" src="aspenONECore/images/Userinfo.svg" width=30 height=30 title="User Info" /></a> -->
        </div>
        <!-- feng -->
        <div id="ubar_logout_div">
          <img title="Logout" src="aspenONECore/images/log_out.svg" width="30" height="30" />
        </div>
      </div>
    </div>
    <!--    <div id="abar_shadow_container" class="fixed"><div id="abar"></div></div>-->
    <div id="abar_shadow_container" class="fixed" style="display: none"><div id="abar"></div></div>
    <!-- This nestedness of the abar is in order to show the box shadow only at the bottom: see http://stackoverflow.com/questions/5115427/box-shadow-only-on-one-side -->
    <div id="ANYApplication" class="fixedbars"></div>
    <div id="ANYApplication-SDK" class="fixedbars"></div>
    <!-- 565961 feng v15 -->
    <!--    <div id="activated_services_bar" style="display: none"> &lt;!&ndash;feng search&ndash;&gt;-->
    <div id="activated_services_bar" style="display: none">
      <!--feng search-->
      <img id="show_hide_asb_icon" src="aspenONECore/images/Gold_Arrow.svg" />
    </div>

    <div id="whitePanel" style="display: none">
      <img id="centeredLogo" src="aspenONECore\images\aspenONE-splash-logo.svg" />
    </div>
    <div id="aspenONE_Tutorial" class="TutorialHide"></div>
    <div id="popupPanelGlass" style="display: none" />
    <div id="errorDialog" style="display: none">
      <div id="floater"></div>
      <div id="errorMessage">
        <table style="margin: 0px auto">
          <tr>
            <td>
              <img src="WebControls/images/alert_infor.png" class="gwt-Image" />
            </td>
            <td><div id="errorAlert"></div></td>
          </tr>
          <tr>
            <td></td>
            <td><div id="errorContent"></div></td>
          </tr>
        </table>
      </div>
    </div>
    <iframe src="sessiontrack.asp" style="visibility: hidden; width: 0px; height: 0px; margin: 0px; padding: 0px" seamless></iframe>

    <!--       <iframe id="aspenONEips" class='ipsFrame' src="http://sp-soc-3/aspenONEips.html" ></iframe>-->

    <!-- remove dependency of ANY SDK -->
    <script src="aspenONECore/scripts/aspenONESDKLibrary.min.js"></script>

    <!-- feng -->
    <!--<script type="text/javascript" language="javascript" src="WebControls/AtPBMPlots/AtPBMPlots.nocache.js"></script>-->
    <script type="text/javascript" language="javascript" src="WebControls/PivotTable/d3.min.js"></script>
    <script src="WebControls/scripts/aspenONESDKInterfaces.min.js"></script>
    <script src="WebControls/scripts/aspenONESDK.min.js"></script>
    <script src="WebControls/scripts/aspenONESDKProcessExplorer.js"></script>

    <script src="aspenONECore/scripts/aspenONEIPSInterfaces.min.js"></script>
    <script src="aspenONECore/scripts/aspenONEIPSServer.min.js"></script>
    <script src="aspenONECore/scripts/aspenONEIPSCore.min.js"></script>

    <script src="WebControls/AtPB.js"></script>

    <script>
      (function () {
        //feng search
        $("#ubar").hide();
        $("#activated_services_bar").hide();
        $("#abar_shadow_container").hide(); //feng search 2024-07-12 13-13-31-Friday

        $(document.body).append('<iframe id="aspenONEips" class="ipsFrame"></iframe>');
        $("#aspenONEips").attr("src", "/processexplorer/aspenONEips.html");
      })();

      function openInitialTrend(initialData) {
        // If we have a meaningful JSON object, then invoke initialPlotLoadFromJSONData(..) with JSON String
        if (initialData && "object" == typeof initialData) {
          if (typeof initialPlotLoadFromJSONData == "function") {
            initialPlotLoadFromJSONData(JSON.stringify(initialData));
          } else if (typeof window.parent[2].initialPlotLoadFromJSONData == "function") {
            //feng click tag from search result, open plot
            window.parent[2].initialPlotLoadFromJSONData(JSON.stringify(initialData));
          } else {
            setTimeout(openInitialTrend.bind(null, initialData), 1100);
          }
          return;
        }
        // default: treat it simply as a file name
        else if (typeof initialPlotLoad == "function") initialPlotLoad(initialData);
        else setTimeout(openInitialTrend.bind(null, initialData), 800);
      }

      function initUiAutoComplete(UserProfile) {
        console.log(`%c initUiAutoComplete`, "color: green; background: yellow; font-size: 12px");
        var $element1 = $("#global_search_input");
        if (!$element1.data("ui-autocomplete")) {
          // Initialize the autocomplete widget if not already initialized
          $element1.autocomplete({
            // Your autocomplete initialization options here
            source: ["widget"],
          });
        }

        $("#ubar_search_history_a2").click(function (e) {
          //feng UC 1384475 search. prevent hash changein  window.onpopstate
          e.preventDefault();
          e.stopImmediatePropagation();
          // Check if the autocomplete widget is visible
          var isAutocompleteVisible = $("#global_search_input").autocomplete("widget").is(":visible");
          // Focus on the search input and show autocomplete if not already visible
          $("#global_search_input").focus();
          if (!isAutocompleteVisible) {
            $("#global_search_input").autocomplete("search", "");
          }
        });

        //feng Defect 1396008
        // Remove any existing click event handlers for '#ReturnToSearch_btn3'
        $("#ReturnToSearch_btn3").off("click");
        $("#ReturnToSearch_btn3").on("click", function (event) {
          event.preventDefault();
          event.stopPropagation();

          var n = window.parent[1].aspenONE.searchHistory.getPreviousSearchCondition(),
            t = (n || {}).ResultItem,
            i = (t || {}).Category;
          var isFromDetailPage = window.location.href.endsWith("#/PESearchDetails");
          //Changlin Defect 1413208 - Automation-Return to Last Search button missed in some case.
          if (isFromDetailPage) {
            if (n) {
              $("#global_search_input").val(n.OriginalSearchText);
              window.condition = n;
            } else {
              $("#global_search_input").val("");
              window.condition = null;
            }
            window.OnSearch();
          } else {
            if (window.parent[1].aspenONE.ItemCategory.PECategory.indexOf(i) > -1) {
              //feng Defect 1396008
              window.parent[1].aspenONE.IPS.iPSSearch.addSearchHistory(n);
              // Construct the URL dynamically
              var newUrl = window.location.origin + window.location.pathname + "#/PESearchDetails";
              window.location.href = newUrl;
            } else if (t) {
              //feng Defect 1396008
              window.parent[1].aspenONE.IPS.iPSSearch.addSearchHistory(n);
              // Construct the URL dynamically
              var newUrl = window.location.origin + window.location.pathname + "#/Search";
              window.location.href = newUrl;
            } else if (n) {
              $("#global_search_input").val(n.OriginalSearchText);
              window.condition = n;
              if (window.location.href.includes("/Search")) {
                //deepcopy
                window.parent[2].aspenONE.Search.searchApp.searchCondition.condition = JSON.parse(JSON.stringify(n));
                window.parent[2].aspenONE.Search.searchApp.AppActivated({ condition: JSON.parse(JSON.stringify(n)) });
                window.parent[2].aspenONE.Search.searchApp.search();
              } else {
                window.OnSearch();
              }
            } else {
              $("#global_search_input").val("");
              window.condition = n;
              if (window.location.href.includes("/Search")) {
                window.parent[2].aspenONE.Search.searchApp.search();
              } else {
                window.OnSearch();
              }
            }
          }

          // feng search 1398204 Suggest able to show whole search string when it is too long.
          setTimeout(function () {
            const inputField = $("#global_search_input");
            const fullText = inputField.val();
            const truncatedText = fullText.length > 20 ? fullText.substring(0, 20) + "..." : fullText;

            // Otherwise, set the global search input value to the truncated text
            $("#global_search_input").val(truncatedText);
            // Set the title attribute of the global search input to the full text
            $("#global_search_input").attr("title", fullText);
          }, 250);
        });

        $("#global_search_input")
          .autocomplete({
            minLength: 0,
            source: function (event, ui) {
              var retrieveSearchHistoryStrings = window.parent[1].aspenONE.IPS.iPSSearch.retrieveSearchHistoryStrings;

              var i = [],
                r,
                u;
              var IsSuite = false; //feng UC 1384475 search
              var searchTypeahead = window.parent[1].aspenONE.IPS.iPSSearch.typeahead; //feng UC 1384475 search
              //hard code for now
              console.log("Window.UserProfile", Window.UserProfile);
              var UserProfile = window.UserProfile;

              event.term && 0 !== event.term.length
                ? ((r = IsSuite //feng UC 1384475 search
                    ? "q=*%3A*&wt=json&terms.fl=typeahead&terms.prefix="
                    : "q=*:*&wt=json&facet.limit=10&rows=0&omitHeader=true&facet=true&facet.field=typeahead&facet.prefix="),
                  (u = {
                    text: r + event.term.toLowerCase(),
                    domain: UserProfile.account.split("/")[0],
                    user: UserProfile.account.split("/")[1],
                  }),
                  // ANYPlatform.IPSCore.Search.typeahead(
                  searchTypeahead(JSON.stringify(u), null, null, function (event) {
                    var u, f, r;
                    var IsSuite = false; //feng search
                    try {
                      //feng https search with domain security ON
                      if (typeof event.data === "object") {
                        event.data = JSON.stringify(event.data);
                      }
                      // temp Fix for solr 9.6.1 SyntaxError: Unexpected token ','
                      // solution
                      let invalidStrings = [new RegExp('":,', "g"), new RegExp('":}', "g")]; // Add more invalid strings here if needed
                      invalidStrings.forEach(function (invalidRegex) {
                        if (invalidRegex.source === '":,') {
                          event.data = event.data.replace(invalidRegex, '":"",');
                        }
                        if (invalidRegex.source === '":}') {
                          event.data = event.data.replace(invalidRegex, '":""}');
                        }
                      });

                      for (
                        u = JSON.parse(event.data),
                          f = IsSuite /*feng UC 1384475 search ANYPlatform.IPSCore.IsSuite*/ ? u.terms.typeahead : u.facet_counts.facet_fields.typeahead,
                          r = 0;
                        r < f.length;
                        r += 2
                      )
                        i.push({ label: f[r], category: "", index: -1 });
                      retrieveSearchHistoryStrings().forEach(function (event, ui) {
                        let i18nFunc = window.i18n ? window.i18n : $.i18n;
                        let finalLabel = typeof event === "string" ? event : event?.SearchText || "";
                        i.push({
                          label: finalLabel,
                          category: i18nFunc("History"),
                          index: ui,
                        });
                      });
                      ui(i);
                    } catch (e) {
                      // feng https
                      // 510:The aspenONE Search Engine was unable to complete a request. The request did not include a valid Domain. This information is required to complete the request. Please contact your system administrator.
                      if (typeof event.data === "string") {
                        const data = event.data.trim();
                        const regex = /^\d{3}:(.*)$/s; // 's' flag allows '.' to match newline characters
                        const match = data.match(regex);

                        if (match) {
                          const message = match[0].trim();
                          alert(message);
                        }
                      }

                      console.error("Failed to retrieve type ahead data.");
                    }
                  }))
                : (retrieveSearchHistoryStrings().forEach(function (event, ui) {
                    //feng UC 1384475 search
                    let i18nFunc = window.i18n ? window.i18n : $.i18n;
                    let finalLabel = typeof event === "string" ? event : event?.SearchText || "";

                    i.push({
                      label: finalLabel,
                      category: i18nFunc("History"),
                      index: ui,
                    });
                  }),
                  ui(i));
            },
            select: function (t, i) {
              //feng UC 1384475 search

              getSearchHistoryItem = window.parent[1].aspenONE.IPS.iPSSearch.getSearchHistoryItem;
              //1414221
              window.ubarSearchTitle = i.item.index > -1 ? getSearchHistoryItem(i.item.index)?.OriginalSearchText : i.item.label;
              console.log("feng window.ubarSearchTitle", window.ubarSearchTitle);

              const fullText = i.item.label.slice();

              const truncatedText = fullText.length > 20 ? fullText.substring(0, 20) + "..." : fullText;
              // feng search 1398204 Suggest able to show whole search string when it is too long.
              setTimeout(function () {
                if (i.item.index > -1) {
                  // Otherwise, set the global search input value to the truncated text
                  $("#global_search_input").val(truncatedText);
                  // Set the title attribute of the global search input to the full text
                  $("#global_search_input").attr("title", fullText);

                  // If the item index is greater than -1, perform a search with the ubarSearchTitle
                  window.OnSearch(window.ubarSearchTitle);
                } else {
                  // Otherwise, set the global search input value to the truncated text
                  $("#global_search_input").val(truncatedText);
                  // Set the title attribute of the global search input to the full text
                  $("#global_search_input").attr("title", fullText);
                  // Perform a search with the item's label
                  window.OnSearch(i.item.label);
                }
              }, 250);

              $("#global_search_input").blur();
            },
            focus: function (event, ui) {
              try {
                $(event.currentTarget).attr("title", ui.item.label);
              } catch (error) {
                return true;
              }
            },
          })
          .data("ui-autocomplete")._renderMenu = function (event, ui) {
          event.addClass("ui-autocomplete-panel");
          var r = this,
            i = "";
          $.each(ui, function (ui, u) {
            u.category != i && (event.append("<li class='ui-menu-item ui-autocomplete-category'><div>" + u.category + "</div></li>"), (i = u.category));
            var f = r._renderItemData(event, u);
            var label = typeof u.label === "string" ? u.label : u.label?.SearchText || "";
            u.category && f.attr("aria-label", u.category + " : " + label);
          });
        };
      }
    </script>
    <!-- feng react -->
    <script type="module" src="static/index.js"></script>
    <!-- <link rel="manifest" href="manifest.json"/> -->
  </body>
</html>
