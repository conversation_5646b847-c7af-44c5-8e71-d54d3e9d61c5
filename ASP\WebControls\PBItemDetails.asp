<%@ language="VBScript" codepage="65001" %>
<%
   '--- initial config information
   Dim Solr_Server, Solr_Rows_Retrieve
   Solr_Server = "http://localhost:8080"
   Solr_Rows_Retrieve = 1000

   Dim NavPane_Ctx_MenuOptions_Rename, NavPane_Ctx_MenuOptions_Cut
   NavPane_Ctx_MenuOptions_Rename = 1
   NavPane_Ctx_MenuOptions_Cut = 1

   Dim NavPane_Ctx_MenuOptions_Paste, NavPane_Ctx_MenuOptions_Delete
   NavPane_Ctx_MenuOptions_Paste = 1
   NavPane_Ctx_MenuOptions_Delete = 1

   '--- additional data we will require
   Dim serverConfigXML, serverConfigJSON
   serverConfigXML = ""
   serverConfigJSON = ""

   Dim initialDataJSON1, initialDataJSON2
   initialDataJSON1 = ""
   initialDataJSON2 = ""

   Dim sLastError
   sLastError = ""

   ' for unit test requests
   Dim testMode
   testMode = ""

   ' for new health status checks
   Call IsDataSourcesValid()

   Sub IsDataSourcesValid()
      On Error Resume Next

      If Request.ServerVariables("QUERY_STRING") = "HealthStatus" Then
         Dim url, xmlHttp, authHeader
         url = "../ProcessData/AtProcessDataREST.dll/DataSources"
         Set xmlHttp = Server.CreateObject("Microsoft.XMLHTTP")

         xmlHttp.Open "POST", url, False
         
         ' Check for JWT Authorization header and forward it
         authHeader = Request.ServerVariables("HTTP_AUTHORIZATION")
         If authHeader <> "" Then
            xmlHttp.setRequestHeader "Authorization", authHeader
         End If
         
         xmlHttp.Send ""

         If xmlHttp.status = 200 And (InStr(xmlHttp.responseText, "[") + 1) <> InStr(xmlHttp.responseText, "]") Then
            Response.Status = "200"
         Else
            Response.Status = "500"
         End If

         If Err.Number <> 0 Then
            Response.Write Err.Description & " " & xmlHttp.responseText & " " & TypeName(xmlHttp)
            Err.Clear
         End If

         Set xmlHttp = Nothing
      End If
   End Sub

   '--- standard processing

   ' read config info from server side XML file
   Call ReadServerConfigXML()

   ' put XML config info into our vars
   Call GetXMLelements()

   ' convert config XML to a JSON string
   Call ConfigToJSON()

   ' write HTML code to the client side
   Response.write CreateContainer()

   '---------------------------------------------------------------------------
   ' build initial client html "Container" code so we can inject info
   ' we have gathered that defines the client
   '---------------------------------------------------------------------------
   Function CreateContainer()
      On Error Resume Next

      ' Create date objects for timezone calculations
      Dim serverDate, serverTimeZoneOffset, localTimeZoneOffset
      serverDate = Now()
      serverTimeZoneOffset = DateDiff("n", Now(), GetUTCTime()) * 60 * 1000
      localTimeZoneOffset = serverTimeZoneOffset ' Server and local timezone are the same in server-side code

      ' Properly encode JSON to avoid script errors
      Dim encodedServerConfigJSON
      encodedServerConfigJSON = Replace(serverConfigJSON, """", "\""")

      ' create client HTML text
      Dim clientHTML
      clientHTML = _
      "<!--[if lt IE 9]> <script type=""text/javascript"" src=""http://ajax.googleapis.com/ajax/libs/chrome-frame/1/CFInstall.min.js""></script> <script> window.attachEvent(""onload"", function() { CFInstall.check({mode: ""inline""}); }); </script> <![endif]-->" & _
      "<script> " & _
      "var g_culture = """ & Request.ServerVariables("http_accept_language") & """; " & _
      "var g_session = """ & Session.SessionID & """; " & _
      "var g_XML = ''; " & _
      "var g_initialDataJSON1 = """ & initialDataJSON1 & """; " & _
      "var g_initialDataJSON2 = """ & initialDataJSON2 & """; " & _
      "var g_serverConfigJSON = '" & encodedServerConfigJSON & "'; " & _
      "var g_serverTime = """ & serverDate & """; " & _
      "var g_serverUTCOffset = """ & serverTimeZoneOffset & """; " & _
      "var g_localUTCOffset = """ & localTimeZoneOffset & """;" & _
      "var g_timeDiff = g_localUTCOffset-g_serverUTCOffset;" & _
      "function updateClientId(id) { if (typeof window.external !== 'undefined' && typeof window.external.updateClientId !== 'undefined') { window.external.updateClientId(id); } }" & _
      "if (g_session) { updateClientId(g_session); }" & _
      "</script>" & _
      "</body>" & _
      "</html>"

      CreateContainer = clientHTML

      If Err.Number <> 0 Then
         sLastError = Err.Description
         Err.Clear
      End If
   End Function

   '---------------------------------------------------------------------------
   ' Helper function to get UTC time
   '---------------------------------------------------------------------------
   Function GetUTCTime()
      Dim nowDate, dateTimeNow
      dateTimeNow = Now()

      ' Get the date portion only as a string in YYYY-MM-DD format
      Dim y, m, d, dateString
      y = Year(dateTimeNow)
      m = Month(dateTimeNow)
      If m < 10 Then m = "0" & m
      d = Day(dateTimeNow)
      If d < 10 Then d = "0" & d
      dateString = y & "-" & m & "-" & d

      ' Get the time portion only as a string in HH:MM:SS format
      Dim h, min, s, timeString
      h = Hour(dateTimeNow)
      If h < 10 Then h = "0" & h
      min = Minute(dateTimeNow)
      If min < 10 Then min = "0" & min
      s = Second(dateTimeNow)
      If s < 10 Then s = "0" & s
      timeString = h & ":" & min & ":" & s

      ' Combine and return
      GetUTCTime = dateString & "T" & timeString & "Z"
   End Function

   '---------------------------------------------------------------------------
   ' read the server XML configuration file contents
   '---------------------------------------------------------------------------
   Sub ReadServerConfigXML()
      On Error Resume Next

      ' read XML config file into a string
      Dim fileObject, filePath, textStream
      Set fileObject = Server.CreateObject("Scripting.FileSystemObject")
      filePath = Server.MapPath("AtWebPlotsConfig.xml")

      If fileObject.FileExists(filePath) Then
         Set textStream = fileObject.OpenTextFile(filePath, 1, False, -2)
         serverConfigXML = textStream.ReadAll()
         textStream.Close
         Set textStream = Nothing

         ' CQ 453992 remove any commented sections
         Dim searchingForComments, pos, segment
         searchingForComments = True
         pos = 0
         segment = ""

         Do While searchingForComments
            searchingForComments = False
            pos = InStr(serverConfigXML, "<!--")

            If pos <> 0 Then
               segment = Left(serverConfigXML, pos - 1)
               pos = InStr(pos, serverConfigXML, "-->")

               If pos <> 0 Then
                  serverConfigXML = segment & Mid(serverConfigXML, pos + 3)
                  searchingForComments = True
               End If
            End If
         Loop
      Else
         sLastError = "File not found: " & filePath
      End If

      Set fileObject = Nothing

      If Err.Number <> 0 Then
         sLastError = Err.Description
         Err.Clear
      End If
   End Sub

   '---------------------------------------------------------------------------
   ' transform the server XML configuration string to JSON
   ' this is simplified for efficiency so currently has specific knowledge
   ' of XML element vars - update it as config settings are added or removed
   ' one example of general conversion that would automatically transform
   ' any XML changes: http://www.thomasfrank.se/xml_to_json.html
   ' but maintaining this function gives simplicity and faster execution
   '---------------------------------------------------------------------------
   Sub ConfigToJSON()
      On Error Resume Next

      serverConfigJSON = "{""Config"":{""Solr"":{""Server"":""" & Solr_Server & _
      """,""RowsRetrieve"":""" & Solr_Rows_Retrieve & """" & _
      "},""NavPane"":{""Ctx_MenuOptions_Rename"":" & NavPane_Ctx_MenuOptions_Rename & _
      ",""Ctx_MenuOptions_Cut"":" & NavPane_Ctx_MenuOptions_Cut & _
      ",""Ctx_MenuOptions_Paste"":" & NavPane_Ctx_MenuOptions_Paste & _
      ",""Ctx_MenuOptions_Delete"":" & NavPane_Ctx_MenuOptions_Delete & _
      "}}}"

      If Err.Number <> 0 Then
         serverConfigJSON = ""
         Err.Clear
      End If
   End Sub

   '---------------------------------------------------------------------------
   ' Read elements found in XML config string and update variables using MSXML2.DomDocument
   '---------------------------------------------------------------------------
   Function GetXMLelements()
      On Error Resume Next

      Dim lastError, xmlDoc
      lastError = 0

      If Len(serverConfigXML) > 0 Then
         ' Create an XML DOM object
         Set xmlDoc = Server.CreateObject("MSXML2.DomDocument.6.0")
         xmlDoc.async = False
         xmlDoc.loadXML(serverConfigXML)

         If xmlDoc.parseError.errorCode <> 0 Then
            sLastError = xmlDoc.parseError.reason
            lastError = xmlDoc.parseError.errorCode
         Else
            ' Use XPath to select nodes
            xmlDoc.setProperty "SelectionLanguage", "XPath"

            ' Solr section
            Solr_Server = xmlDoc.selectSingleNode("Config[1]/Solr[1]/Server[1]").text
            Solr_Rows_Retrieve = xmlDoc.selectSingleNode("Config[1]/Solr[1]/RowsRetrieve[1]").text

            ' NavPane section
            NavPane_Ctx_MenuOptions_Rename = CInt(xmlDoc.selectSingleNode("Config[1]/NavPane[1]/ContextMenuOptions[1]/Rename[1]").text)
            NavPane_Ctx_MenuOptions_Cut = CInt(xmlDoc.selectSingleNode("Config[1]/NavPane[1]/ContextMenuOptions[1]/Cut[1]").text)
            NavPane_Ctx_MenuOptions_Paste = CInt(xmlDoc.selectSingleNode("Config[1]/NavPane[1]/ContextMenuOptions[1]/Paste[1]").text)
            NavPane_Ctx_MenuOptions_Delete = CInt(xmlDoc.selectSingleNode("Config[1]/NavPane[1]/ContextMenuOptions[1]/Delete[1]").text)
         End If
         Set xmlDoc = Nothing
      Else
         lastError = -999
      End If

      GetXMLelements = lastError

      If Err.Number <> 0 Then
         GetXMLelements = -999
         Err.Clear
      End If
   End Function
%>
<!doctype html>
<html>
  <head>
     <meta http-equiv="content-type" content="text/html; charset=UTF-8">
     <meta http-equiv="X-UA-Compatible" content="chrome=1,IE=10"/>
     <meta names="apple-mobile-web-app-status-bar-style" content="black-translucent" />
     <meta name="apple-mobile-web-app-capable" content="yes" />
     <meta name="viewport" ID="viewport" content="width=device-width, height=device-height, initial-scale=1.0" />
    <title>aspenONE Process Explorer Item Details</title>
    <link type="text/css" rel="stylesheet" href="PBItemDetails.css">
    <link type="text/css" rel="stylesheet" href="AtPB.css">
    <link type="text/css" href="../assets/design-system/tokens/index.css" rel="stylesheet" />
    
    <!-- Authentication Overlay Styles -->
    <style type="text/css">
      /* Authentication Overlay Styles */
      .auth-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        z-index: 20000;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .auth-container {
        max-width: 600px;
        margin: 0 20px;
        text-align: center;
      }

      .auth-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 40px;
        margin: 20px 0;
      }

      .auth-card h1 {
        color: #333;
        margin-bottom: 20px;
        font-family: Arial, sans-serif;
      }

      .auth-card p {
        color: #666;
        margin-bottom: 20px;
        font-family: Arial, sans-serif;
      }

      .auth-login-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 15px 30px;
        font-size: 18px;
        border-radius: 8px;
        cursor: pointer;
        transition: transform 0.2s;
        margin: 10px;
        font-family: Arial, sans-serif;
      }

      .auth-login-button:hover {
        transform: translateY(-2px);
      }

      .auth-login-button:disabled {
        background: #cccccc;
        cursor: not-allowed;
        transform: none;
      }

      .auth-logout-button {
        background: #dc3545 !important;
      }

      .auth-status-message {
        margin: 20px 0;
        padding: 15px;
        border-radius: 5px;
        font-weight: bold;
        font-family: Arial, sans-serif;
      }

      .auth-status-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .auth-status-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .auth-status-info {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }

      .auth-debug-section {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-top: 20px;
        text-align: left;
        font-family: Arial, sans-serif;
      }

      .auth-debug-section h3, .auth-debug-section h4 {
        color: #333;
      }

      .auth-debug-section button {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }

      .auth-debug-section pre {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 12px;
      }
    </style>
    <script>
        if(!window.console) {
            var console = {
                log : function(){},
                warn : function(){},
                error : function(){},
                time : function(){},
                timeEnd : function(){}
            }
        }
        
        // MSAL Authentication Configuration for PBItemDetails
        window.isReact = false; // PBItemDetails is not React-based
        window.UserProfile = window.UserProfile || {};
        window.i18n = window.i18n || null;
        window.condition = window.condition || null;
        window.USE_JWT_AUTH = true;  // Enable JWT authentication
        
        // Override auth.js behavior for PBItemDetails custom flow
        window.showMsalLoginBtn = true;  // Enable auto-login functionality
        window.DISABLE_AUTO_REDIRECT = true;  // Prevent auto-redirect to landing page
        window.PBITEMDETAILS_CUSTOM_AUTH = true;  // Flag to indicate custom auth flow
        
        // Check URL parameters for auto-login control
        var urlParams = new URLSearchParams(window.location.search);
        var autoLoginParam = urlParams.get('autoLogin');
        if (autoLoginParam !== null) {
            // URL parameter overrides default behavior
            window.AUTO_LOGIN_ENABLED = autoLoginParam.toLowerCase() === 'true';
            console.log('PBItemDetails: Auto-login controlled by URL parameter:', window.AUTO_LOGIN_ENABLED);
        } else {
            // Default behavior: enable automatic login for PBItemDetails
            window.AUTO_LOGIN_ENABLED = true;
            console.log('PBItemDetails: Auto-login enabled by default');
        }
        
        // Completely disable auth.js redirect behavior
        window.FORCE_DISABLE_AUTH_REDIRECTS = true;
        window.isReact = true;  // Trick auth.js into thinking this is React (prevents redirects)
        
        // Override auth.js functions before they load
        window.showAuthOverlay = function() { 
            console.log('PBItemDetails: Blocked auth.js showAuthOverlay'); 
        };
        window.hideAuthOverlay = function() { 
            console.log('PBItemDetails: Blocked auth.js hideAuthOverlay'); 
        };
        
        // Context-specific overlay control flags for PBItemDetails
        window.showOverlayOnInitialLoad = false;      // Don't show overlay immediately (check auth first)
        window.showOverlayOnNoToken = true;           // Show overlay when no token exists
        window.showOverlayOnTokenExpiration = true;   // Always show on token expiry
        window.showOverlayOn401Error = true;          // Always show on 401 Unauthorized 
        window.showOverlayOn511Error = true;          // Always show on 511 Network Auth Required      
        
        console.log('PBItemDetails: MSAL Authentication system loading...');
        
        // Legacy compatibility - ensure key functions are available
        window.showAuthOverlay = window.showAuthOverlay || function() { 
            console.log('showAuthOverlay placeholder - waiting for MSAL initialization'); 
        };
        window.hideAuthOverlay = window.hideAuthOverlay || function() { 
            console.log('hideAuthOverlay placeholder - waiting for MSAL initialization'); 
        };
    </script>
    <script type="text/javascript" language="javascript" src="AtDataClientUtils.js"></script>
    <script type="text/javascript" language="javascript" src="AtSearchAdminUtils.js"></script>
    <script type="text/javascript" language="javascript" src="AtPBMItemDetails/AtPBMItemDetails.nocache.js"></script>
    <!-- MSAL Authentication System -->
    <script src="../msal/msal-browser-4.12.0.js"></script>
    <script>
        // Override the config path for PBItemDetails context before loading authConfig
        window.MSAL_CONFIG_PATH_OVERRIDE = '../msal/entraConfig.json';
        console.log('PBItemDetails: MSAL config path override set to:', window.MSAL_CONFIG_PATH_OVERRIDE);
        console.log('PBItemDetails: MSAL library loaded:', !!window.msal);
    </script>
    <script src="../msal/authConfig.js" onload="console.log('PBItemDetails: authConfig.js loaded successfully, msalConfig available:', !!window.msalConfig);" onerror="console.error('PBItemDetails: Failed to load authConfig.js');"></script>
    <!-- Skip auth.js to avoid conflicts with our custom implementation -->
    
    <script>
        // Immediate validation after all MSAL scripts should have loaded
        setTimeout(function() {
            console.log('PBItemDetails: Post-load validation - MSAL library:', !!window.msal);
            console.log('PBItemDetails: Post-load validation - msalConfig:', !!window.msalConfig);
            if (window.msalConfig) {
                console.log('PBItemDetails: msalConfig details:', {
                    clientId: window.msalConfig.auth.clientId,
                    authority: window.msalConfig.auth.authority,
                    redirectUri: window.msalConfig.auth.redirectUri
                });
            }
            
            // Check for script loading errors
            var scripts = document.querySelectorAll('script[src*="msal"]');
            console.log('PBItemDetails: Found ' + scripts.length + ' MSAL-related scripts');
            scripts.forEach(function(script, index) {
                console.log('PBItemDetails: Script ' + (index + 1) + ':', script.src);
            });
        }, 500); // Wait 500ms for scripts to execute
    </script>
    
    <script src="scripts/jquery-3.7.0.js"></script>
    <script src="scripts/jquery-ui-1.13.2.js"></script>
    <script src="scripts/aspenONESDKInterfaces.min.js"></script>
    <script src="scripts/aspenONESDK.min.js"></script>
    <script src="scripts/aspenONESDKPESearchDetails.js"></script>
    <script src="scripts/echarts.min.js"></script>
    <script src="scripts/controls-init.js"></script>
    <script src="AtPB.js"></script>
    <script>
        function GetWindowObject()
        {
           return self;
        }

        function LoadNativeItemDetails(data){
            if(typeof loadItemDetails == "function") {
                if(data) {
                    loadItemDetails(JSON.stringify(data));
                } else {
                    alert("no type specified!");
                }
                $("body").show();
            } else {
                setTimeout(function(){LoadNativeItemDetails(data)}, 50);
            }
        }

        /**
         * This saves the current ItemDetails to Workspace file.  We expect it to populate the following:
         *  - queryString : with the parameters in order to re-invoke this PBItemDetails.
         *  - title : a descriptive name for the saved display. The name does not change functionality when re-invoked
         *  - The type - which in this case will always be for "searchdetails"
        */
        function saveToHome(queryString){
            var option = parseQueryStringToJson(queryString);
            var Item = {};
            // we get only the parameters to keep from generating a queryString:queryString:?parameters if we should try to re-save.
            Item["queryString"] = getParametersFromQueryString(queryString, false);
            if(option.file) {
                var fileName;
                var str = option.file.replace(/\\/g, "/").split("/");
                fileName = str[str.length-1].substring(0, str[str.length-1].lastIndexOf("."));
                Item["title"] = "PBSearchDetails-" + option.type + "-" + fileName;
            } else {
                Item["title"] = "PBSearchDetails-" + option.type + "-" + (option.tag||option.characteristic||option.alias);
            }
            Item["type"] = "searchdetails";

            /*a1pe.ReadAllFiles(function(data){
                var isCreat = true;
                if(data && data.projects) {
                    var projects = data.projects;
                    for(var i = 0; i < projects.length; i++) {
                        if(projects[i].frametitle && projects[i].frametitle.toLowerCase() == "pesearchdetails" && projects[i].parameters && projects[i].parameters.type == Item["type"] && projects[i].parameters.title == Item["title"]) {
                            isCreat = window.confirm("A workspace item with this name already exists. Create another one? You can rename it later.");
                            break;
                        }
                    }
                    if(isCreat) {
                        saveToHomeWithValidName(Item);
                    }
                }
            });*/
            saveToHomeWithValidName(Item);
        }

        function saveToHomeWithValidName(Item) {
            a1pe.CreateNewItem(Item, function( data ){
                if(data){
                    if ($("#SaveSuccess")) {
                        $('#SaveSuccess').show("slide",{direction:"right"},1000);
                        $("#SaveSuccess").click(function () {
                            $("#SaveSuccess").fadeOut('fast');
                        });
                        setTimeout(function () {
                            $("#SaveSuccess").fadeOut("slow");
                        }, 3000);
                    }
                }
            });
        }

        /**
         * Gets just the parameters from the queryString so we can just get meaningful substrings
         * of name=value pairs separated by the ampersand character.
         * Parameters:
         *   queryString : the string to process.
         *   getParametersFromQueryString : true to remove any leading "?", false or not supplied to not remove.
         * Example: for queryString of '{"queryString":"?Type=TAG&Tag=TI4000&DS=IP21SERVER"}'
         * this will return 'Type=TAG&Tag=TI4000&DS=IP21SERVER'.
         */
        function getParametersFromQueryString(queryString, removeLeadingQuestionmark) {
            var retString = queryString;
            try {
                var parsedJson = JSON.parse(retString);
                if (parsedJson.queryString!==undefined) {
                    retString = parsedJson.queryString;
                }
            } catch (x) {
                // it may already have had the queryString removed. Ok.
            }
            if (removeLeadingQuestionmark && retString.indexOf('?')== 0) {
                    retString = retString.substring(1);
            }
            return retString;
        }

        function parseQueryStringToJson(queryString) {
            var item = {};
            var queryArray = getParametersFromQueryString(queryString, true).split("&");
            var query;
            for(var i = 0; i < queryArray.length; i++) {
                query = queryArray[i].split("=");
                item[query[0].toLowerCase()] = query[1];
            }
            return item;
        }

        function openA1Search(searchText, searchType) {
            aspenONESearch(searchText, true, false, searchType);
        }

        function defineProp(shapeName) {
            Object.defineProperty(window, shapeName, {
                configurable: true,
                get: function() { var x = getControlValue(shapeName); return getIfNumber(x); },
                set: function(v) { redrawControl(shapeName, v); }
            });
        }

        // ========== JWT USERNAME EXTRACTION FOR PBITEMDETAILS ==========
        
        /**
         * Get JWT username from PBItemDetails context for GWT components
         * This function provides multiple sources for username extraction:
         * 1. MSAL UserProfile.account (CORP/HUAF format)
         * 2. MSAL msalInstance active account
         * 3. JWT token extraction from localStorage
         * 4. Fallback to session storage
         */
        function getPBItemDetailsJWTUsername() {
            try {
                console.log('PBItemDetails: Getting JWT username...');
                
                // Priority 1: Try to get UserProfile from localStorage (most reliable, complete object from LoginReducer)
                try {
                    var storedUserProfile = localStorage.getItem('UserProfile');
                    if (storedUserProfile && storedUserProfile !== 'null' && storedUserProfile !== 'undefined') {
                        var userProfile = JSON.parse(storedUserProfile);
                        if (userProfile && userProfile.account) {
                            var username = userProfile.account.replace('/', '\\'); // Convert CORP/HUAF to CORP\HUAF
                            console.log('PBItemDetails: JWT username from localStorage UserProfile (Priority 1): ' + username);
                            window.g_jwtUsername = username; // Store globally for GWT access
                            return username;
                        }
                    }
                } catch (parseError) {
                    console.warn('PBItemDetails: Error parsing UserProfile from localStorage:', parseError);
                }
                
                // Priority 2: Try to get from localStorage msal.username as fallback
                var msalUsername = localStorage.getItem('msal.username');
                if (msalUsername && msalUsername !== 'null' && msalUsername !== 'undefined' && msalUsername !== '') {
                    console.log('PBItemDetails: JWT username from localStorage msal.username (Priority 2): ' + msalUsername);
                    window.g_jwtUsername = msalUsername; // Store globally for GWT access
                    return msalUsername;
                }
                
                // Priority 3: Use parent window UserProfile.account (already in CORP/HUAF format)
                if (window.parent && window.parent.UserProfile && window.parent.UserProfile.account) {
                    var username = window.parent.UserProfile.account.replace('/', '\\'); // Convert CORP/HUAF to CORP\HUAF
                    console.log('PBItemDetails: JWT username from parent UserProfile.account (Priority 3): ' + username);
                    window.g_jwtUsername = username; // Store globally for GWT access
                    return username;
                }
                
                // Priority 4: Use current window UserProfile if available
                if (window.UserProfile && window.UserProfile.account) {
                    var username = window.UserProfile.account.replace('/', '\\'); // Convert CORP/HUAF to CORP\HUAF
                    console.log('PBItemDetails: JWT username from window UserProfile.account (Priority 4): ' + username);
                    window.g_jwtUsername = username; // Store globally for GWT access
                    return username;
                }
                
                // Priority 5: Try to get username from global MSAL instance (myMSALObj)
                if (window.myMSALObj) {
                    try {
                        var account = window.myMSALObj.getActiveAccount();
                        if (account && account.username) {
                            console.log('PBItemDetails: JWT username from myMSALObj active account (Priority 5): ' + account.username);
                            window.g_jwtUsername = account.username; // Store globally for GWT access
                            return account.username;
                        }
                        
                        // Also try to get account info from MSAL cache
                        var accounts = window.myMSALObj.getAllAccounts();
                        if (accounts && accounts.length > 0 && accounts[0].username) {
                            console.log('PBItemDetails: JWT username from myMSALObj getAllAccounts (Priority 5b): ' + accounts[0].username);
                            window.g_jwtUsername = accounts[0].username; // Store globally for GWT access
                            return accounts[0].username;
                        }
                    } catch (msalError) {
                        console.warn('PBItemDetails: Error accessing myMSALObj:', msalError);
                    }
                }
                
                // Priority 6: Try to get username from parent MSAL instance
                if (window.parent && window.parent.msalInstance) {
                    var account = window.parent.msalInstance.getActiveAccount();
                    if (account && account.username) {
                        console.log('PBItemDetails: JWT username from parent MSAL active account (Priority 6): ' + account.username);
                        window.g_jwtUsername = account.username; // Store globally for GWT access
                        return account.username;
                    }
                }
                
                // Priority 7: Try to get username from current window MSAL
                if (window.msalInstance) {
                    var account = window.msalInstance.getActiveAccount();
                    if (account && account.username) {
                        console.log('PBItemDetails: JWT username from window MSAL active account (Priority 7): ' + account.username);
                        window.g_jwtUsername = account.username; // Store globally for GWT access
                        return account.username;
                    }
                }
                
                // Priority 8: Try to get from sessionStorage
                var sessionUsername = sessionStorage.getItem('msal.username');
                if (sessionUsername && sessionUsername !== 'null' && sessionUsername !== 'undefined' && sessionUsername !== '') {
                    console.log('PBItemDetails: JWT username from sessionStorage (Priority 8): ' + sessionUsername);
                    window.g_jwtUsername = sessionUsername; // Store globally for GWT access
                    return sessionUsername;
                }
                
                // Priority 9: Try to extract from JWT token in localStorage
                var accessToken = localStorage.getItem('msal.accessToken');
                if (accessToken && accessToken !== 'null' && accessToken !== 'undefined' && accessToken !== '') {
                    var username = extractUsernameFromJWTToken(accessToken);
                    if (username) {
                        console.log('PBItemDetails: JWT username extracted from localStorage token (Priority 9): ' + username);
                        window.g_jwtUsername = username; // Store globally for GWT access
                        return username;
                    }
                }
                
                // Priority 10: Try to extract from JWT token in sessionStorage
                var sessionToken = sessionStorage.getItem('msal.accessToken');
                if (sessionToken && sessionToken !== 'null' && sessionToken !== 'undefined' && sessionToken !== '') {
                    var username = extractUsernameFromJWTToken(sessionToken);
                    if (username) {
                        console.log('PBItemDetails: JWT username extracted from sessionStorage token (Priority 10): ' + username);
                        window.g_jwtUsername = username; // Store globally for GWT access
                        return username;
                    }
                }
                
                console.log('PBItemDetails: No JWT username found, will fallback to Windows auth');
                window.g_jwtUsername = null; // Clear global variable
                return null;
                
            } catch (error) {
                console.warn('PBItemDetails: Error getting JWT username: ' + error.message);
                window.g_jwtUsername = null; // Clear global variable
                return null;
            }
        }
        
        /**
         * Extract username from JWT token payload
         * @param {string} token - The JWT access token
         * @returns {string|null} Username from token claims
         */
        function extractUsernameFromJWTToken(token) {
            try {
                // JWT has 3 parts separated by dots
                var parts = token.split('.');
                if (parts.length >= 2) {
                    // Decode the payload (second part)
                    var payload = parts[1];
                    
                    // Add padding if needed for base64 decoding
                    while (payload.length % 4) {
                        payload += '=';
                    }
                    
                    // Decode base64
                    var decodedPayload = atob(payload);
                    var claims = JSON.parse(decodedPayload);
                    
                    // Try different username claims in order of preference
                    return claims.preferred_username || claims.upn || claims.unique_name || claims.name || null;
                }
            } catch (error) {
                console.warn('PBItemDetails: Error extracting username from JWT token: ' + error.message);
            }
            
            return null;
        }
        
        /**
         * Initialize JWT username on page load and check MSAL authentication
         * This ensures the username is available when GWT components load
         */
        function initializePBItemDetailsJWT() {
            console.log('PBItemDetails: ========== STARTING AUTHENTICATION INITIALIZATION ==========');
            console.log('PBItemDetails: Current URL:', window.location.href);
            
            // One-time URL cleanup to prevent infinite loops
            if (window.location.hash && window.location.hash.includes('/landing')) {
                console.log('PBItemDetails: Detected auth.js redirect, cleaning URL once...');
                
                // Set a flag to prevent repeated cleanups
                if (!window.PB_URL_CLEANED) {
                    window.PB_URL_CLEANED = true;
                    var cleanUrl = window.location.origin + window.location.pathname + window.location.search;
                    console.log('PBItemDetails: Replacing URL with: ' + cleanUrl);
                    window.location.replace(cleanUrl);
                    return;
                } else {
                    console.log('PBItemDetails: URL already cleaned, continuing...');
                }
            }
            
            // Check if JWT auth is enabled
            var useJWTAuth = (window.parent && window.parent.USE_JWT_AUTH) || window.USE_JWT_AUTH || false;
            console.log('PBItemDetails: JWT Auth enabled: ' + useJWTAuth);
            console.log('PBItemDetails: window.USE_JWT_AUTH:', window.USE_JWT_AUTH);
            console.log('PBItemDetails: window.parent.USE_JWT_AUTH:', window.parent ? window.parent.USE_JWT_AUTH : 'N/A');
            
            if (useJWTAuth) {
                // First check if we already have authentication in localStorage
                var localStorageCheck = false;
                var debugInfo = {
                    userProfileJson: null,
                    msalUsername: null,
                    accessToken: null
                };
                
                try {
                    debugInfo.userProfileJson = localStorage.getItem('UserProfile');
                    debugInfo.msalUsername = localStorage.getItem('msal.username');
                    debugInfo.accessToken = localStorage.getItem('msal.accessToken');
                    
                    console.log('PBItemDetails: LocalStorage debug info:', debugInfo);
                    
                    if ((debugInfo.userProfileJson && debugInfo.userProfileJson !== 'null') || 
                        (debugInfo.msalUsername && debugInfo.msalUsername !== 'null')) {
                        localStorageCheck = true;
                        console.log('PBItemDetails: Found existing authentication in localStorage');
                    } else {
                        console.log('PBItemDetails: No valid authentication found in localStorage');
                    }
                } catch (e) {
                    console.log('PBItemDetails: Error checking localStorage:', e);
                }
                
                if (localStorageCheck) {
                    // We have localStorage auth - extract username and continue
                    var jwtUsername = getPBItemDetailsJWTUsername();
                    console.log('PBItemDetails: getPBItemDetailsJWTUsername result:', jwtUsername);
                    if (jwtUsername) {
                        console.log('PBItemDetails: JWT username from localStorage: ' + jwtUsername);
                        hideAuthOverlay();
                        console.log('PBItemDetails: Authentication complete via localStorage, exiting initialization');
                        return;
                    } else {
                        console.log('PBItemDetails: localStorage check passed but getPBItemDetailsJWTUsername returned null, continuing with MSAL initialization');
                    }
                }
                
                // No existing authentication found - wait for MSAL and initialize
                console.log('PBItemDetails: No existing authentication found, waiting for MSAL...');
                waitForMSALAndInitialize();
            } else {
                console.log('PBItemDetails: JWT Auth disabled, using Windows authentication');
                hideAuthOverlay();
                window.g_jwtUsername = null;
                console.log('PBItemDetails: Authentication complete via Windows auth, exiting initialization');
            }
        }

        /**
         * Wait for MSAL to load and then initialize authentication
         */
        function waitForMSALAndInitialize() {
            console.log('PBItemDetails: ========== WAITING FOR MSAL LIBRARIES ==========');
            
            // Show overlay immediately to provide user feedback
            showAuthOverlay();
            updateAuthStatus('Loading authentication system...', false);
            
            var attempts = 0;
            var msalCheckInterval = setInterval(function() {
                attempts++;
                console.log('PBItemDetails: MSAL check attempt ' + attempts + ' - msal:', !!window.msal, 'msalConfig:', !!window.msalConfig);
                
                // More detailed logging about missing components
                if (!window.msal) {
                    console.log('PBItemDetails: MSAL library not loaded yet');
                }
                if (!window.msalConfig) {
                    console.log('PBItemDetails: msalConfig not available yet');
                    console.log('PBItemDetails: Checking if authConfig.js script exists...');
                    var authConfigScript = document.querySelector('script[src*="authConfig.js"]');
                    console.log('PBItemDetails: authConfig.js script element found:', !!authConfigScript);
                    if (authConfigScript) {
                        console.log('PBItemDetails: authConfig.js script src:', authConfigScript.src);
                    }
                }
                
                if (window.msal && window.msalConfig) {
                    clearInterval(msalCheckInterval);
                    console.log('PBItemDetails: MSAL is available after ' + attempts + ' attempts, initializing authentication');
                    initializeMSALAuthentication();
                }
            }, 100); // Check every 100ms
            
            // Timeout after 10 seconds if MSAL doesn't load
            setTimeout(function() {
                clearInterval(msalCheckInterval);
                console.warn('PBItemDetails: MSAL timeout after ' + attempts + ' attempts');
                console.warn('PBItemDetails: Final state - msal:', !!window.msal, 'msalConfig:', !!window.msalConfig);
                
                // Try to load authConfig.js manually if it failed (but only if it hasn't been loaded before)
                if (window.msal && !window.msalConfig) {
                    if (window.msalConfigLoaded) {
                        console.error('PBItemDetails: authConfig.js was loaded but msalConfig not set. Check for JavaScript errors in authConfig.js');
                        showAuthError('Authentication configuration loaded but failed to initialize. Check browser console for errors.');
                    } else {
                        console.warn('PBItemDetails: MSAL library loaded but msalConfig missing, attempting manual script load...');
                        loadAuthConfigManually();
                    }
                } else if (!window.msal) {
                    console.error('PBItemDetails: MSAL library failed to load after timeout');
                    showAuthError('Authentication library failed to load. Please refresh the page or check your network connection.');
                } else {
                    console.warn('PBItemDetails: MSAL timeout reached, showing login panel as fallback');
                    showLoginPanel();
                }
            }, 10000);
        }
        
        /**
         * Manually load authConfig.js if it failed to load automatically
         */
        function loadAuthConfigManually() {
            try {
                var script = document.createElement('script');
                script.src = '../msal/authConfig.js';
                script.onload = function() {
                    console.log('PBItemDetails: authConfig.js loaded manually, msalConfig available:', !!window.msalConfig);
                    if (window.msalConfig) {
                        initializeMSALAuthentication();
                    } else {
                        console.error('PBItemDetails: authConfig.js loaded but msalConfig still not available');
                        showAuthError('Authentication configuration failed to load');
                    }
                };
                script.onerror = function() {
                    console.error('PBItemDetails: Failed to load authConfig.js manually');
                    showAuthError('Authentication configuration could not be loaded');
                };
                document.head.appendChild(script);
            } catch (error) {
                console.error('PBItemDetails: Error loading authConfig.js manually:', error);
                showAuthError('Failed to load authentication configuration');
            }
        }

        /**
         * Initialize MSAL authentication system for PBItemDetails
         */
        function initializeMSALAuthentication() {
            try {
                console.log('PBItemDetails: Starting MSAL initialization...');
                
                // Show loading message
                showAuthOverlay();
                updateAuthStatus('Checking authentication status...', false);
                
                // CRITICAL: Check MSAL configuration first (same logic as auth.js)
                if (!checkMSALConfiguration()) {
                    console.log('PBItemDetails: MSAL not configured, allowing access without authentication (same as aspenONE.html)');
                    hideAuthOverlay();
                    window.g_jwtUsername = null;
                    window.authenticationSuccessful = true; // Mark as successful to prevent re-authentication attempts
                    window.authenticationComplete = true;
                    return;
                }
                
                // Set JWT auth flag for REST endpoints (CRITICAL for getUserProfile to work)
                window.USE_JWT_AUTH = true;
                if (window.parent) {
                    window.parent.USE_JWT_AUTH = true;
                }
                if (window.top) {
                    window.top.USE_JWT_AUTH = true;
                }
                console.log('PBItemDetails: Set USE_JWT_AUTH flag for REST endpoints');
                
                // Enhanced MSAL configuration validation
                console.log('PBItemDetails: Validating MSAL configuration...');
                console.log('PBItemDetails: msal library available:', !!window.msal);
                console.log('PBItemDetails: msalConfig available:', !!window.msalConfig);
                
                if (window.msalConfig) {
                    console.log('PBItemDetails: msalConfig.auth.clientId:', window.msalConfig.auth.clientId);
                    console.log('PBItemDetails: msalConfig.auth.authority:', window.msalConfig.auth.authority);
                    
                    // Check for placeholder values
                    if (window.msalConfig.auth.clientId === "Enter_the_Application_Id_Here") {
                        console.error('PBItemDetails: MSAL clientId is not configured! Still contains placeholder value.');
                        showAuthError('Authentication not configured: Missing Azure Application ID. Please configure entraConfig.json with valid Azure AD application credentials.');
                        return;
                    }
                    
                    if (window.msalConfig.auth.authority === "https://login.microsoftonline.com/Enter_the_Tenant_ID_Here") {
                        console.error('PBItemDetails: MSAL authority is not configured! Still contains placeholder value.');
                        showAuthError('Authentication not configured: Missing Azure Tenant ID. Please configure entraConfig.json with valid Azure AD tenant credentials.');
                        return;
                    }
                } else {
                    console.error('PBItemDetails: msalConfig is not available!');
                    showAuthError('Authentication configuration missing: msalConfig not loaded. Please check that authConfig.js is loaded properly.');
                    return;
                }
                
                // Initialize MSAL instance if not already done
                if (!window.myMSALObj && window.msalConfig) {
                    console.log('PBItemDetails: Creating MSAL instance with validated config...');
                    window.myMSALObj = new msal.PublicClientApplication(window.msalConfig);
                }
                
                if (window.myMSALObj) {
                                    // Initialize the MSAL instance (required in newer versions)
                console.log('PBItemDetails: Initializing MSAL instance...');
                window.myMSALObj.initialize().then(function() {
                    console.log('PBItemDetails: MSAL instance initialized successfully');
                    
                    // CRITICAL: Check for redirect authentication response first
                    return window.myMSALObj.handleRedirectPromise();
                }).then(function(redirectResponse) {
                    if (redirectResponse) {
                        console.log('PBItemDetails: Redirect authentication response received:', redirectResponse);
                        handleSuccessfulLogin(redirectResponse);
                        return; // Exit early since we handled redirect response
                    }
                    
                    // Check if user is already signed in
                    var accounts = window.myMSALObj.getAllAccounts();
                    console.log('PBItemDetails: Found ' + accounts.length + ' MSAL accounts');
                    
                    if (accounts.length > 0) {
                        // User is already signed in
                        var account = accounts[0];
                        console.log('PBItemDetails: User already signed in: ' + account.username);
                        
                        // CRITICAL FIX: Acquire access token for existing account
                        var tokenRequest = {
                            scopes: ["openid", "profile", "User.Read"],
                            account: account
                        };
                        
                        window.myMSALObj.acquireTokenSilent(tokenRequest)
                            .then(function(tokenResponse) {
                                console.log('PBItemDetails: Access token acquired silently:', tokenResponse);
                                
                                // Store BOTH access token AND ID token
                                if (tokenResponse.accessToken) {
                                    localStorage.setItem('msal.accessToken', tokenResponse.accessToken);
                                    sessionStorage.setItem('msal.accessToken', tokenResponse.accessToken);
                                    console.log('PBItemDetails: Stored access token from silent acquisition');
                                }
                                
                                // Store ID token (required by auth.js for authentication validation)
                                if (tokenResponse.idToken) {
                                    localStorage.setItem('msal.idToken', tokenResponse.idToken);
                                    sessionStorage.setItem('msal.idToken', tokenResponse.idToken);
                                    console.log('PBItemDetails: Stored ID token from silent acquisition');
                                }
                                
                                // Store account information (required by auth.js)
                                if (account) {
                                    localStorage.setItem('msal.account', JSON.stringify(account));
                                    sessionStorage.setItem('msal.account', JSON.stringify(account));
                                    console.log('PBItemDetails: Stored account information from silent acquisition');
                                }
                                
                                // Store authentication data (overlay will be hidden in callback)
                                storeAuthenticationData(account);
                            })
                            .catch(function(tokenError) {
                                console.warn('PBItemDetails: Silent token acquisition failed:', tokenError);
                                
                                // Fallback: Store authentication data without token (overlay will be hidden in callback)
                                storeAuthenticationData(account);
                            });
                    } else {
                        // User needs to sign in - show login button
                        console.log('PBItemDetails: User not signed in, showing login panel');
                        showLoginPanel();
                    }
                }).catch(function(error) {
                        console.error('PBItemDetails: MSAL initialization failed:', error);
                        showAuthError('MSAL initialization failed: ' + error.message);
                    });
                } else {
                    console.error('PBItemDetails: Failed to create MSAL instance');
                    showAuthError('Failed to create authentication system');
                }
            } catch (error) {
                console.error('PBItemDetails: MSAL initialization error:', error);
                showAuthError('Authentication initialization failed: ' + error.message);
            }
        }

        /**
         * Check if MSAL is properly configured (same logic as AuthManager.checkMSALConfiguration in auth.js)
         * This ensures PBItemDetails behaves the same as aspenONE.html for invalid configurations
         */
        function checkMSALConfiguration() {
            try {
                // Check if MSAL config objects exist
                if (typeof msalConfig === 'undefined' || typeof msal === 'undefined') {
                    console.log('PBItemDetails: MSAL libraries not loaded');
                    return false;
                }

                // Validate MSAL configuration
                if (!msalConfig.auth || !msalConfig.auth.clientId || !msalConfig.auth.authority) {
                    console.log('PBItemDetails: MSAL configuration incomplete');
                    return false;
                }

                // Check for placeholder clientId values
                if (msalConfig.auth.clientId === "Enter_the_Application_Id_Here" || 
                    msalConfig.auth.clientId.includes("Enter_the_Application_Id")) {
                    console.log('PBItemDetails: clientId contains placeholder value - configuration not complete');
                    return false;
                }

                // Validate clientId format (should be a GUID)
                var guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
                if (!guidRegex.test(msalConfig.auth.clientId)) {
                    console.log('PBItemDetails: Invalid clientId format - must be a valid GUID');
                    return false;
                }

                // Validate authority URL
                try {
                    var authorityUrl = new URL(msalConfig.auth.authority);
                    
                    // Check for Microsoft domains
                    if (!authorityUrl.hostname.includes('login.microsoftonline.com') && 
                        !authorityUrl.hostname.includes('login.microsoft.com')) {
                        console.log('PBItemDetails: Invalid authority URL - must use Microsoft login domain');
                        return false;
                    }

                    // Extract tenant ID from authority URL path
                    var pathSegments = authorityUrl.pathname.split('/').filter(function(segment) { return segment.length > 0; });
                    if (pathSegments.length > 0) {
                        var tenantId = pathSegments[0];
                        
                        // Check for placeholder tenant ID
                        if (tenantId === "Enter_the_Tenant_ID_Here" || tenantId.includes("Enter_the_Tenant_ID")) {
                            console.log('PBItemDetails: authority contains placeholder tenant ID - configuration not complete');
                            return false;
                        }
                        
                        // Check if tenant ID is a valid GUID (unless it's a special tenant like 'common', 'organizations', 'consumers')
                        var specialTenants = ['common', 'organizations', 'consumers'];
                        if (specialTenants.indexOf(tenantId.toLowerCase()) === -1 && !guidRegex.test(tenantId)) {
                            console.log('PBItemDetails: Invalid tenant ID format in authority URL - must be a valid GUID or special tenant');
                            return false;
                        }
                    }
                } catch (e) {
                    console.log('PBItemDetails: Malformed authority URL');
                    return false;
                }

                console.log('PBItemDetails: MSAL properly configured');
                return true;

            } catch (error) {
                console.error('PBItemDetails: Error checking MSAL configuration:', error);
                return false;
            }
        }

        /**
         * Store authentication data in localStorage and global variables
         * Includes protection against multiple simultaneous calls and data validation
         */
        function storeAuthenticationData(account) {
            try {
                // Prevent multiple simultaneous calls for the same account
                var accountKey = 'storeAuth_' + account.username;
                if (window[accountKey + '_inProgress']) {
                    console.log('PBItemDetails: storeAuthenticationData already in progress for: ' + account.username);
                    return;
                }
                window[accountKey + '_inProgress'] = true;
                
                console.log('PBItemDetails: Storing authentication data for: ' + account.username);
                
                // Check if we already have good authentication data
                var existingUserProfile = null;
                try {
                    var existingData = localStorage.getItem('UserProfile');
                    if (existingData && existingData !== 'null') {
                        existingUserProfile = JSON.parse(existingData);
                        // If we already have internal format (contains \ or /), don't overwrite unless we get a better result
                        if (existingUserProfile && existingUserProfile.account && 
                            (existingUserProfile.account.includes('\\') || existingUserProfile.account.includes('/'))) {
                            console.log('PBItemDetails: Already have internal format username: ' + existingUserProfile.account);
                        }
                    }
                } catch (e) {
                    console.log('PBItemDetails: No existing authentication data found');
                }
                
                // First, try to get the internal domain format from getUserProfile REST endpoint
                getUserProfileFromREST(account.username, function(internalUsername) {
                    // Clear the in-progress flag
                    window[accountKey + '_inProgress'] = false;
                    
                    var finalUsername = internalUsername || account.username;
                    console.log('PBItemDetails: Using username: ' + finalUsername + ' (from ' + (internalUsername ? 'REST' : 'MSAL') + ')');
                    
                    // If we have existing internal format data and the new result is just email format, 
                    // don't overwrite unless the existing data is for a different user
                    if (existingUserProfile && existingUserProfile.account && 
                        (existingUserProfile.account.includes('\\') || existingUserProfile.account.includes('/')) &&
                        !internalUsername && existingUserProfile.email === account.username) {
                        console.log('PBItemDetails: Keeping existing internal format data: ' + existingUserProfile.account);
                        finalUsername = existingUserProfile.account;
                    }
                    
                    // Validate the final username
                    if (!finalUsername || finalUsername.trim() === '' || finalUsername === 'null' || finalUsername === 'undefined') {
                        console.error('PBItemDetails: Invalid final username, falling back to account username');
                        finalUsername = account.username;
                    }
                    
                    // Create UserProfile object similar to LoginReducer.ts
                    var userProfile = {
                        account: finalUsername, // Internal format (CORP/HUAF) or email fallback
                        userName: finalUsername,
                        name: account.name || account.username,
                        version: '1.0',
                        email: account.username // Keep original email for reference
                    };
                    
                    // Only store if we have valid data
                    if (userProfile.account && userProfile.account.trim() !== '') {
                        // Store in localStorage (same as LoginReducer.ts)
                        localStorage.setItem('UserProfile', JSON.stringify(userProfile));
                        localStorage.setItem('msal.username', finalUsername);
                        sessionStorage.setItem('msal.username', finalUsername);
                        
                        // Store in window objects for compatibility
                        window.UserProfile = userProfile;
                        if (window.parent) {
                            window.parent.UserProfile = userProfile;
                        }
                        
                        // Store JWT username globally for GWT access (ensure backslash format)
                        var jwtUsername = finalUsername.includes('/') ? finalUsername.replace('/', '\\') : finalUsername;
                        window.g_jwtUsername = jwtUsername;
                        
                        console.log('PBItemDetails: Authentication data stored successfully with username: ' + jwtUsername);
                    } else {
                        console.error('PBItemDetails: Invalid user profile data, not storing');
                    }
                    
                    // CRITICAL FIX: Mark authentication as successful to prevent re-authentication
                    window.authenticationSuccessful = true;
                    window.authenticationComplete = true;
                    console.log('PBItemDetails: Authentication marked as successful');
                    

                    
                    // CRITICAL FIX: Hide overlay AFTER authentication data is successfully stored
                    hideAuthOverlay();
                    console.log('PBItemDetails: Authentication overlay hidden after successful data storage');
                });
                
            } catch (error) {
                // Clear the in-progress flag
                var accountKey = 'storeAuth_' + (account ? account.username : 'unknown');
                window[accountKey + '_inProgress'] = false;
                console.error('PBItemDetails: Error storing authentication data:', error);
                // Hide overlay even on error to prevent stuck overlay
                hideAuthOverlay();
            }
        }

        /**
         * Get user profile from ProcessData REST endpoint to convert email to internal format
         * Includes protection against multiple simultaneous calls
         */
        function getUserProfileFromREST(email, callback) {
            try {
                // Prevent multiple simultaneous calls for the same email
                var callKey = 'getUserProfile_' + email;
                if (window[callKey + '_inProgress']) {
                    console.log('PBItemDetails: getUserProfile already in progress for: ' + email);
                    return;
                }
                window[callKey + '_inProgress'] = true;
                
                console.log('PBItemDetails: Getting user profile from REST for: ' + email);
                
                // Ensure JWT auth flag is set for REST endpoint
                window.USE_JWT_AUTH = true;
                if (window.parent) {
                    window.parent.USE_JWT_AUTH = true;
                }
                if (window.top) {
                    window.top.USE_JWT_AUTH = true;
                }
                
                var xhr = new XMLHttpRequest();
                var restUrl = '/ProcessExplorer/ProcessData/AtProcessDataREST.dll/Command?command=getUserProfile&client=ProcessBrowser';
                
                xhr.open('GET', restUrl, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                
                // Add authorization header if we have a token
                var accessToken = localStorage.getItem('msal.accessToken');
                if (accessToken && accessToken !== 'null' && accessToken !== 'undefined' && accessToken !== '') {
                    xhr.setRequestHeader('Authorization', 'Bearer ' + accessToken);
                    console.log('PBItemDetails: Added JWT Authorization header for getUserProfile');
                } else {
                    console.warn('PBItemDetails: No valid access token found for getUserProfile');
                }
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        // Clear the in-progress flag
                        window[callKey + '_inProgress'] = false;
                        
                        if (xhr.status === 200) {
                            try {
                                // Enhanced response validation
                                var responseText = xhr.responseText;
                                if (!responseText || responseText.trim() === '') {
                                    console.warn('PBItemDetails: getUserProfile returned empty response');
                                    callback(null);
                                    return;
                                }
                                
                                var response = JSON.parse(responseText);
                                console.log('PBItemDetails: getUserProfile response:', response);
                                
                                // Enhanced response validation
                                if (!response || typeof response !== 'object') {
                                    console.warn('PBItemDetails: getUserProfile returned invalid response format');
                                    callback(null);
                                    return;
                                }
                                
                                // Extract internal username from response
                                var internalUsername = null;
                                if (response && response.account && response.account.trim() !== '') {
                                    internalUsername = response.account.trim();
                                } else if (response && response.userName && response.userName.trim() !== '') {
                                    internalUsername = response.userName.trim();
                                } else if (response && response.username && response.username.trim() !== '') {
                                    internalUsername = response.username.trim();
                                }
                                
                                // Validate that the internal username is different from email and not empty
                                if (internalUsername && internalUsername !== email && internalUsername !== 'null' && internalUsername !== 'undefined') {
                                    console.log('PBItemDetails: Converted ' + email + ' to ' + internalUsername);
                                    callback(internalUsername);
                                } else {
                                    console.log('PBItemDetails: No valid conversion available, using email format');
                                    callback(null);
                                }
                            } catch (parseError) {
                                console.error('PBItemDetails: Error parsing getUserProfile response:', parseError);
                                callback(null);
                            }
                        } else {
                            console.warn('PBItemDetails: getUserProfile failed with status ' + xhr.status + ': ' + xhr.responseText);
                            callback(null);
                        }
                    }
                };
                
                xhr.onerror = function() {
                    // Clear the in-progress flag
                    window[callKey + '_inProgress'] = false;
                    console.error('PBItemDetails: getUserProfile request failed');
                    callback(null);
                };
                
                xhr.ontimeout = function() {
                    // Clear the in-progress flag
                    window[callKey + '_inProgress'] = false;
                    console.error('PBItemDetails: getUserProfile request timed out');
                    callback(null);
                };
                
                // Set timeout to prevent hanging requests
                xhr.timeout = 10000; // 10 seconds
                xhr.send();
                
            } catch (error) {
                // Clear the in-progress flag
                var callKey = 'getUserProfile_' + email;
                window[callKey + '_inProgress'] = false;
                console.error('PBItemDetails: Error calling getUserProfile REST:', error);
                callback(null);
            }
        }

        /**
         * Show the authentication overlay
         */
        function showAuthOverlay() {
            var overlay = document.getElementById('authOverlay');
            if (overlay) {
                overlay.style.display = 'flex';
                console.log('PBItemDetails: Authentication overlay shown');
                
                // Ensure our overlay is on top of any auth.js overlays
                overlay.style.zIndex = '30000';
            }
        }

        /**
         * Hide the authentication overlay
         */
        function hideAuthOverlay() {
            var overlay = document.getElementById('authOverlay');
            if (overlay) {
                overlay.style.display = 'none';
                console.log('PBItemDetails: Authentication overlay hidden');
            }
        }

        /**
         * Show the login panel with sign-in button
         */
        function showLoginPanel() {
            console.log('PBItemDetails: ========== SHOWING LOGIN PANEL ==========');
            
            // CRITICAL: Don't show login panel if we already have successful authentication
            if (window.authenticationSuccessful) {
                console.log('PBItemDetails: Ignoring showLoginPanel call after successful authentication');
                return;
            }
            
            console.log('PBItemDetails: Checking overlay elements...');
            var overlay = document.getElementById('authOverlay');
            var loginSection = document.getElementById('authLoginSection');
            var loadingSection = document.getElementById('authLoadingSection');
            var loginBtn = document.getElementById('authLoginBtn');
            var statusMessage = document.getElementById('authStatusMessage');
            
            console.log('PBItemDetails: Overlay elements check:');
            console.log('  - authOverlay:', !!overlay);
            console.log('  - authLoginSection:', !!loginSection);
            console.log('  - authLoadingSection:', !!loadingSection);
            console.log('  - authLoginBtn:', !!loginBtn);
            console.log('  - authStatusMessage:', !!statusMessage);
            
            if (!overlay) {
                console.error('PBItemDetails: authOverlay element not found! Cannot show login panel.');
                return;
            }
            
            console.log('PBItemDetails: Showing authentication overlay...');
            showAuthOverlay();
            updateAuthStatus('Please sign in to access ProcessExplorer Item Details', false);
            
            if (loginSection) {
                loginSection.style.display = 'block';
                console.log('PBItemDetails: Login section displayed');
            } else {
                console.error('PBItemDetails: authLoginSection element not found!');
            }
            
            if (loadingSection) {
                loadingSection.style.display = 'none';
                console.log('PBItemDetails: Loading section hidden');
            } else {
                console.error('PBItemDetails: authLoadingSection element not found!');
            }
            
            // Set up login button click handler
            if (loginBtn) {
                console.log('PBItemDetails: Setting up login button click handler');
                loginBtn.onclick = function() {
                    console.log('PBItemDetails: Login button clicked!');
                    performMSALLogin();
                };
                
                // Auto-click login button if AUTO_LOGIN_ENABLED is true
                if (window.AUTO_LOGIN_ENABLED === true) {
                    console.log('PBItemDetails: AUTO_LOGIN_ENABLED is true, auto-clicking login button in 500ms...');
                    setTimeout(function() {
                        if (loginBtn && !window.authenticationSuccessful) {
                            console.log('PBItemDetails: Auto-clicking login button');
                            
                            // Check for popup blocking before auto-login
                            var popupBlocked = isPopupBlocked();
                            if (popupBlocked) {
                                updateAuthStatus('Popup windows are blocked. Redirecting to secure login page...', false);
                            } else {
                                updateAuthStatus('Automatically signing you in...', false);
                            }
                            
                            loginBtn.click();
                        } else {
                            console.log('PBItemDetails: Auto-click cancelled - button not found or auth already successful');
                        }
                    }, 500); // Small delay to ensure UI is rendered
                } else {
                    console.log('PBItemDetails: AUTO_LOGIN_ENABLED is false, manual login required');
                }
            } else {
                console.error('PBItemDetails: authLoginBtn element not found! Cannot set up click handler.');
            }
            
            console.log('PBItemDetails: Login panel setup complete');
        }

        /**
         * Check if popups are blocked or likely to be blocked
         */
        function isPopupBlocked() {
            try {
                var popup = window.open('', '_blank', 'width=1,height=1');
                if (!popup || popup.closed || typeof popup.closed === 'undefined') {
                    return true;
                }
                popup.close();
                return false;
            } catch (e) {
                return true;
            }
        }

        /**
         * Perform MSAL login with popup fallback to redirect
         */
        function performMSALLogin() {
            try {
                console.log('PBItemDetails: Starting MSAL login...');
                
                // Show loading state
                updateAuthStatus('Signing in...', false);
                
                var loginSection = document.getElementById('authLoginSection');
                var loadingSection = document.getElementById('authLoadingSection');
                
                if (loginSection) loginSection.style.display = 'none';
                if (loadingSection) loadingSection.style.display = 'block';
                
                // Create login request if not available
                if (!window.loginRequest) {
                    window.loginRequest = {
                        scopes: ["openid", "profile", "User.Read"],
                        prompt: "select_account"
                    };
                }
                
                // Debug MSAL availability before login attempt
                console.log('PBItemDetails: MSAL login attempt - myMSALObj available:', !!window.myMSALObj);
                console.log('PBItemDetails: MSAL login attempt - loginRequest available:', !!window.loginRequest);
                if (window.msalConfig) {
                    console.log('PBItemDetails: msalConfig clientId:', window.msalConfig.auth.clientId);
                } else {
                    console.log('PBItemDetails: msalConfig not available');
                }
                
                // Check if popups are blocked
                var popupBlocked = isPopupBlocked();
                console.log('PBItemDetails: Popup blocked check result:', popupBlocked);
                
                // Perform MSAL login
                if (window.myMSALObj && window.loginRequest) {
                    if (popupBlocked) {
                        console.log('PBItemDetails: Popups appear to be blocked, using redirect authentication...');
                        performRedirectLogin();
                    } else {
                        console.log('PBItemDetails: Attempting popup authentication...');
                        attemptPopupLogin();
                    }
                } else {
                    // Enhanced error message with more details
                    var errorDetails = [];
                    if (!window.myMSALObj) {
                        errorDetails.push('MSAL instance not initialized');
                        if (!window.msal) {
                            errorDetails.push('MSAL library not loaded');
                        }
                        if (!window.msalConfig) {
                            errorDetails.push('msalConfig not available');
                        } else if (window.msalConfig.auth.clientId === "Enter_the_Application_Id_Here") {
                            errorDetails.push('Azure Application ID not configured');
                        }
                    }
                    if (!window.loginRequest) {
                        errorDetails.push('loginRequest not available');
                    }
                    
                    var detailedError = 'MSAL authentication system not ready: ' + errorDetails.join(', ');
                    console.error('PBItemDetails: ' + detailedError);
                    throw new Error(detailedError);
                }
                
            } catch (error) {
                console.error('PBItemDetails: Login error:', error);
                showAuthError('Login failed: ' + error.message);
            }
        }

        /**
         * Attempt popup-based login with fallback to redirect
         */
        function attemptPopupLogin() {
            console.log('PBItemDetails: Attempting popup login...');
            updateAuthStatus('Opening login window...', false);
            
            window.myMSALObj.loginPopup(window.loginRequest)
                .then(function(response) {
                    console.log('PBItemDetails: MSAL popup login successful:', response);
                    handleSuccessfulLogin(response);
                })
                .catch(function(error) {
                    console.error('PBItemDetails: MSAL popup login error:', error);
                    
                    // Check if it's a popup-related error
                    if (error.message && (
                        error.message.includes('popup_window_error') ||
                        error.message.includes('popup') ||
                        error.message.includes('blocked')
                    )) {
                        console.log('PBItemDetails: Popup login failed, falling back to redirect authentication...');
                        updateAuthStatus('Popup blocked. Redirecting to secure login page...', false);
                        
                        // Small delay to show the message, then redirect
                        setTimeout(function() {
                            performRedirectLogin();
                        }, 2000);
                    } else {
                        showAuthError('Login failed: ' + error.message);
                    }
                });
        }

        /**
         * Perform redirect-based login
         */
        function performRedirectLogin() {
            try {
                console.log('PBItemDetails: Performing redirect login...');
                updateAuthStatus('Redirecting to secure login page...', false);
                
                // Use redirect login instead of popup
                window.myMSALObj.loginRedirect(window.loginRequest);
                
            } catch (error) {
                console.error('PBItemDetails: Redirect login error:', error);
                showAuthError('Redirect login failed: ' + error.message);
            }
        }

        /**
         * Handle successful login response (common for both popup and redirect)
         */
        function handleSuccessfulLogin(response) {
            console.log('PBItemDetails: Handling successful login response:', response);
            
            // CRITICAL FIX: Store BOTH access token AND ID token in localStorage
            if (response.accessToken) {
                localStorage.setItem('msal.accessToken', response.accessToken);
                sessionStorage.setItem('msal.accessToken', response.accessToken);
                console.log('PBItemDetails: Stored access token in localStorage');
            } else {
                console.warn('PBItemDetails: No access token in login response');
            }
            
            // CRITICAL FIX: Store ID token (required by auth.js for authentication validation)
            if (response.idToken) {
                localStorage.setItem('msal.idToken', response.idToken);
                sessionStorage.setItem('msal.idToken', response.idToken);
                console.log('PBItemDetails: Stored ID token in localStorage');
            } else {
                console.warn('PBItemDetails: No ID token in login response');
            }
            
            // CRITICAL FIX: Store account information (required by auth.js)
            if (response.account) {
                localStorage.setItem('msal.account', JSON.stringify(response.account));
                sessionStorage.setItem('msal.account', JSON.stringify(response.account));
                console.log('PBItemDetails: Stored account information in localStorage');
            } else {
                console.warn('PBItemDetails: No account information in login response');
            }
            
            // Store authentication data (overlay will be hidden in callback)
            storeAuthenticationData(response.account);
            
            // Debug current URL state
            console.log('PBItemDetails: Current URL before redirect:', window.location.href);
            console.log('PBItemDetails: Origin:', window.location.origin);
            console.log('PBItemDetails: Pathname:', window.location.pathname);
            console.log('PBItemDetails: Search:', window.location.search);
            console.log('PBItemDetails: Hash:', window.location.hash);
            
            // Try different approaches based on URL state
            if (window.location.hash && window.location.hash.includes('/landing')) {
                // If we have the problematic hash, clean it and reload
                console.log('PBItemDetails: Detected problematic hash, cleaning URL...');
                var cleanUrl = window.location.origin + window.location.pathname + window.location.search;
                console.log('PBItemDetails: Replacing with clean URL: ' + cleanUrl);
                window.location.replace(cleanUrl);
            } else {
                // No problematic hash, just reload the page
                console.log('PBItemDetails: Reloading page to reinitialize with authentication...');
                window.location.reload();
            }
        }

        /**
         * Update authentication status message
         */
        function updateAuthStatus(message, isError) {
            var statusElement = document.getElementById('authStatusMessage');
            if (statusElement) {
                statusElement.textContent = message;
                statusElement.style.display = 'block';
                statusElement.className = 'auth-status-message' + (isError ? ' error' : '');
            }
        }

        /**
         * Show authentication error (with protection against showing after successful auth)
         */
        function showAuthError(errorMessage) {
            // CRITICAL: Don't show authentication errors if we already have successful authentication
            if (window.authenticationSuccessful) {
                console.log('PBItemDetails: Ignoring auth error after successful authentication: ' + errorMessage);
                return;
            }
            
            console.error('PBItemDetails: Auth error: ' + errorMessage);
            updateAuthStatus(errorMessage, true);
            
            var loginSection = document.getElementById('authLoginSection');
            var loadingSection = document.getElementById('authLoadingSection');
            
            if (loginSection) loginSection.style.display = 'block';
            if (loadingSection) loadingSection.style.display = 'none';
        }
        
        // ========== END JWT USERNAME EXTRACTION ==========

        /**
         * Initialize GWT application with authentication protection
         */
        function initializeGWTApplication() {
            try {
                console.log('PBItemDetails: ========== INITIALIZING GWT APPLICATION ==========');
                console.log('PBItemDetails: Authentication status - successful:', window.authenticationSuccessful, 'complete:', window.authenticationComplete);
                console.log('PBItemDetails: USE_JWT_AUTH:', window.USE_JWT_AUTH);
                
                // If JWT auth is disabled, proceed immediately
                if (!window.USE_JWT_AUTH) {
                    console.log('PBItemDetails: JWT Auth disabled, proceeding with GWT initialization');
                    proceedWithGWTInitialization();
                    return;
                }
                
                // If JWT auth is enabled, wait for authentication to complete
                if (window.authenticationSuccessful) {
                    console.log('PBItemDetails: Authentication already successful, proceeding with GWT initialization');
                    proceedWithGWTInitialization();
                } else {
                    console.log('PBItemDetails: Waiting for authentication to complete before GWT initialization...');
                    
                    // Set up a periodic check for authentication completion
                    var authCheckInterval = setInterval(function() {
                        if (window.authenticationSuccessful) {
                            clearInterval(authCheckInterval);
                            console.log('PBItemDetails: Authentication completed, proceeding with GWT initialization');
                            proceedWithGWTInitialization();
                        }
                    }, 500); // Check every 500ms
                    
                    // Timeout after 30 seconds
                    setTimeout(function() {
                        clearInterval(authCheckInterval);
                        if (!window.authenticationSuccessful) {
                            console.warn('PBItemDetails: Authentication timeout, proceeding with GWT initialization anyway');
                            proceedWithGWTInitialization();
                        }
                    }, 30000);
                }
                
            } catch (error) {
                console.error('PBItemDetails: Error initializing GWT application:', error);
                // Fallback to proceed anyway
                proceedWithGWTInitialization();
            }
        }

        /**
         * Proceed with GWT application initialization after authentication is ready
         */
        function proceedWithGWTInitialization() {
            try {
                console.log('PBItemDetails: Proceeding with GWT application initialization');
                
                //feng UC 1384475 search
                if(window.parent && window.parent.isReact){
                    $("body").show();
                    //example data
                    var exampledata = {
                                   "DocumentURL": "http://HF2V14DEV.rnd.aspentech.com/ProcessExplorer/Webcontrols/PBItemDetails.asp?Type=TAG&Tag=A102A.PV&DS=HF2V14DEV",
                                   "FileName": "A102A.PV",
                                   "DocumentID": "http://HF2V14DEV.rnd.aspentech.com/ProcessExplorer/Webcontrols/PBItemDetails.asp?Type=TAG&Tag=A102A.PV&DS=HF2V14DEV",
                                   "TagName": "A102A.PV",
                                   "TagType": "Analog",
                                   "TagArea": "Undefined",
                                   "TagDescription": "Butane Comp. in Top (DMCplus)",
                                   "Tagdatasource": "HF2V14DEV",
                                   "TagUom": "%",
                                   "TagMap": "IP_ANALOGMAP",
                                   "ThumbnailUrl": "../Webcontrols/images/Tag_Image.svg",
                                   "PeelbackUrl": "../Webcontrols/AtMiniChartStub.asp?Tag=A102A.PV&DS=HF2V14DEV",
                                   "ShareTime": "2024-07-10T14:02:18.417Z",
                                   "Name": "A102A.PV",
                                   "Datasource": "HF2V14DEV",
                                   "PEArea": "Undefined",
                                   "FilePath": "null",
                                   "isDownloadable": false,
                                   "Category": "Tags",
                                   "FileType": "IP.21 Process Browser",
                                   "Highlight_Text": {},
                                   "UUID": "http://HF2V14DEV.rnd.aspentech.com/ProcessExplorer/Webcontrols/PBItemDetails.asp?Type=TAG&Tag=A102A.PV&DS=HF2V14DEV",
                                   "ModelType": "MES",
                                   "ImageUrl": "images/lg_ICGEN.gif",
                                   "deferred": {}
                               };
                     //in SearchApp.js, we setItem
                     //window.localStorage.setItem("PESearchDetailsResultItem",JSON.stringify(_this.solrResult.resultItems[post]));
                     var data = JSON.parse(window.localStorage.getItem("PESearchDetailsResultItem"));
                     console.log('HandleAppActivated(data);',data);

                    a1pe.HandleAppActivated(data);
                }
                
                // Register GWT listeners
                console.log('PBItemDetails: Registering GWT listeners');
                if (typeof a1pe !== 'undefined' && typeof a1pe.RegisterListeners === 'function') {
                    a1pe.RegisterListeners();
                    console.log('PBItemDetails: GWT listeners registered successfully');
                } else {
                    console.warn('PBItemDetails: a1pe.RegisterListeners not available, will retry...');
                    // Retry after a short delay
                    setTimeout(function() {
                        if (typeof a1pe !== 'undefined' && typeof a1pe.RegisterListeners === 'function') {
                            a1pe.RegisterListeners();
                            console.log('PBItemDetails: GWT listeners registered successfully on retry');
                        } else {
                            console.error('PBItemDetails: a1pe.RegisterListeners still not available after retry');
                        }
                    }, 2000);
                }
                
            } catch (error) {
                console.error('PBItemDetails: Error in proceedWithGWTInitialization:', error);
            }
        }

        $(function(){
            console.log('PBItemDetails: ========== DOCUMENT READY ==========');
            console.log('PBItemDetails: DOM loaded, starting authentication...');
            
            // Add a debug function to window for manual testing
            window.debugShowLoginPanel = function() {
                console.log('PBItemDetails: DEBUG - Forcing login panel to show');
                showLoginPanel();
            };
            
            window.debugAuthInfo = function() {
                console.log('PBItemDetails: DEBUG - Authentication info:');
                console.log('  - USE_JWT_AUTH:', window.USE_JWT_AUTH);
                console.log('  - AUTO_LOGIN_ENABLED:', window.AUTO_LOGIN_ENABLED);
                console.log('  - authenticationSuccessful:', window.authenticationSuccessful);
                console.log('  - localStorage UserProfile:', localStorage.getItem('UserProfile'));
                console.log('  - localStorage msal.username:', localStorage.getItem('msal.username'));
                console.log('  - overlay element exists:', !!document.getElementById('authOverlay'));
            };
            
            console.log('PBItemDetails: Debug functions added to window: debugShowLoginPanel(), debugAuthInfo()');
            
            // Initialize JWT username before other operations
            initializePBItemDetailsJWT();
            
            // Initialize GWT application with authentication protection
            initializeGWTApplication();
        })

        function doLicenseCheck(appID, clientID) {
            if(typeof doLicenseCheckOut == "function") {
                doLicenseCheckOut(appID, clientID);
            } else {
                setTimeout(function(){
                    doLicenseCheck(appID, clientID);
                }, 50);
            }
        }

        function getIfNumber(n) {
              if(!isNaN(parseFloat(n)) && isFinite(n))
                            return Number(n);
              if(!isNaN(n) && n.toLowerCase() == 'true')
                    return 1;
              else if(!isNaN(n) && n.toLowerCase() == 'false')
                    return 0;
              else
                    return n;
        }

        function defineProp(shapeName)
        {
            Object.defineProperty(window, shapeName, {
                            configurable: true,
                            get: function() { var x = getControlValue(shapeName); return getIfNumber(x); },
                            set: function(v) { redrawControl(shapeName, v); }
            });
        }
    </script>
  </head>

  <body>
    <!-- Authentication Overlay -->
    <div id="authOverlay" class="auth-overlay" style="display: none;">
      <div class="auth-container">
        <div class="auth-card">
          <h1>ProcessExplorer Item Details Authentication</h1>
          <p>Please sign in with your Microsoft account to access ProcessExplorer Item Details</p>
          
          <div id="authStatusMessage" class="auth-status-message" style="display: none;"></div>
          
          <div id="authLoginSection">
            <button id="authLoginBtn" class="auth-login-button">Sign In with Microsoft</button>
            <p><small>You will be redirected to Microsoft's secure login page</small></p>
          </div>
          
          <div id="authAuthenticatedSection" style="display: none;">
            <div class="auth-status-message auth-status-success">
              <p>Successfully authenticated!</p>
              <p>Welcome, <span id="authUserName">User</span></p>
            </div>
            <button id="authContinueBtn" class="auth-login-button">Continue to Item Details</button>
            <button id="authLogoutBtn" class="auth-login-button auth-logout-button">Sign Out</button>
          </div>
          
          <div id="authLoadingSection" style="display: none;">
            <div class="auth-status-message auth-status-info">
              <p>Processing authentication...</p>
            </div>
          </div>
        </div>
        
        <!-- Debug section (hidden by default) -->
        <div class="auth-debug-section" style="display: none;">
          <h3>Debug Information</h3>
          <p>Authentication Status: <span id="authDebugStatus">Checking...</span></p>
          <p>Return URL: <span id="authDebugReturnUrl">None</span></p>
          <button id="authShowDebugBtn">Show Debug Info</button>
          <div id="authDebugDetails" style="display: none;">
            <h4>Token Details:</h4>
            <pre id="authTokenContent">No token available</pre>
          </div>
        </div>
      </div>
    </div>
    
    <iframe src="javascript:''" id="__gwt_historyFrame" tabIndex='-1' style="position:absolute;width:0;height:0;border:0"></iframe>
    <div id="SaveSuccess" style="z-index:9999; display:none; height: 10px;  position: fixed; top: 40px; right: 0px;"></div>
    <div id="UBAR"></div>
    <div id="ABAR"></div>
    <div id="MainTable"></div>
    <div id="divHidden" style="visibility:hidden;float:left;"></div>
  </body>
</html>