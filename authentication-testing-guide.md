# Authentication Flow Testing Guide

## Overview

This guide explains how to test the 8 authentication scenarios you described. The implementation has been added to both `PBItemDetails.asp` and `aspenONE.html` to handle different authentication states based on JSON configuration validity and localStorage presence.

## Test Scenarios

### Scenario 1: First visit, valid JSON, no localStorage
- **Expected**: See overlay then login panel (MSAL configured, no auth data)
- **Action**: Configure valid JSON, clear localStorage, visit page
- **Test Command**: `TestConfigs.runScenario(1)`

### Scenario 2: Revisit, valid JSON, has localStorage  
- **Expected**: No overlay (auth data exists, continues normally)
- **Action**: Configure valid JSON, set localStorage, visit page
- **Test Command**: `TestConfigs.runScenario(2)`

### Scenario 3: Valid→Invalid JSON, refresh
- **Expected**: No overlay, clears localStorage (invalid config = allow access without auth)
- **Action**: Change to invalid config, refresh page
- **Test Command**: `TestConfigs.runScenario(3)`

### Scenario 4: Refresh with invalid JSON
- **Expected**: No overlay, localStorage cleared (invalid config continues to allow access)
- **Action**: Refresh page with invalid config
- **Test Command**: `TestConfigs.runScenario(4)`

### Scenario 5: Invalid→Valid JSON, refresh
- **Expected**: Shows overlay + login panel (valid config detected, no auth data)
- **Action**: Change to valid config, refresh page
- **Test Command**: `TestConfigs.runScenario(5)`

### Scenario 6: Refresh with valid JSON + localStorage
- **Expected**: No overlay (has auth data, continues normally)  
- **Action**: Refresh with valid config and localStorage
- **Test Command**: `TestConfigs.runScenario(6)`

### Scenario 7: Valid→Invalid JSON, refresh
- **Expected**: No overlay, clears localStorage (same as #3)
- **Action**: Change to invalid config, refresh
- **Test Command**: `TestConfigs.runScenario(7)`

### Scenario 8: Refresh with invalid JSON
- **Expected**: No overlay, localStorage cleared (same as #4)
- **Action**: Refresh with invalid config
- **Test Command**: `TestConfigs.runScenario(8)`

## Manual Testing Methods

### Method 1: Using Browser Console Commands

1. Open either `ASP/aspenONE.html` or `ASP/WebControls/PBItemDetails.asp` in browser
2. Open browser Developer Tools (F12)
3. Use the following console commands:

```javascript
// Run individual scenarios
TestConfigs.runScenario(1);  // Run scenario 1
TestConfigs.runScenario(2);  // Run scenario 2
// ... etc for scenarios 3-8

// Or run all scenarios in sequence
TestConfigs.runAllScenarios();

// Manual configuration changes
TestConfigs.setValidConfig();    // Set valid Azure AD config
TestConfigs.setInvalidConfig();  // Set invalid placeholder config

// Manual localStorage changes
TestConfigs.setMockLocalStorage(); // Add mock auth data
TestConfigs.clearAuthStorage();    // Clear all auth data

// Check current state
TestConfigs.getAuthState();        // Get current auth state
TestConfigs.logCurrentConfig();    // Log current MSAL config
```

### Method 2: File-Based Configuration

1. **For Valid JSON**: Copy `entraConfig.json.valid` to `entraConfig.json`
2. **For Invalid JSON**: Copy `entraConfig.json.invalid` to `entraConfig.json`

```bash
# Set valid configuration
cp ASP/msal/entraConfig.json.valid ASP/msal/entraConfig.json

# Set invalid configuration  
cp ASP/msal/entraConfig.json.invalid ASP/msal/entraConfig.json
```

### Method 3: Using Test Page

Open `test-auth-scenarios.html` for a dedicated testing interface with buttons for each scenario.

## Key Implementation Files

### Modified Files:
- **`ASP/WebControls/PBItemDetails.asp`** - Enhanced with scenario-based authentication logic
- **`ASP/aspenONE.html`** - Added scenario checking integration

### New Files:
- **`ASP/msal/enhanced-auth-flow.js`** - Core authentication flow logic
- **`ASP/msal/test-configs.js`** - Testing utilities and configuration management
- **`test-auth-scenarios.html`** - Standalone testing interface
- **`ASP/msal/entraConfig.json.valid`** - Valid configuration template
- **`ASP/msal/entraConfig.json.invalid`** - Invalid configuration template

## Authentication Logic Flow

The authentication flow follows this decision tree:

```
1. Check JSON Configuration Validity
   ├── Invalid Configuration
   │   ├── Has localStorage? → Clear storage, no overlay
   │   └── No localStorage? → No overlay
   └── Valid Configuration  
       ├── Has localStorage? → No overlay (continue normally)
       └── No localStorage? → Show overlay + login panel
```

## Configuration Validation Rules

**Valid Configuration Requirements:**
- `clientId` must be a valid GUID format
- `clientId` cannot be "Enter_the_Application_Id_Here" 
- `authority` must use Microsoft login domains
- `authority` cannot contain "Enter_the_Tenant_ID_Here"

**Invalid Configuration:**
- Any placeholder values
- Malformed GUIDs
- Invalid authority URLs

## localStorage Keys Managed

The system manages these localStorage/sessionStorage keys:
- `UserProfile` - Complete user profile object
- `msal.username` - Username in CORP\\user format
- `msal.accessToken` - OAuth access token
- `msal.idToken` - OpenID Connect ID token
- `msal.account` - MSAL account object

## Debug Information

All authentication flow decisions are logged to browser console with prefixes:
- `PBItemDetails:` - Logs from PBItemDetails.asp
- `aspenONE:` - Logs from aspenONE.html  
- `EnhancedAuthFlow:` - Logs from enhanced authentication logic
- `TestConfigs:` - Logs from testing utilities

## Troubleshooting

1. **No overlay showing when expected**: Check browser console for authentication flow logs
2. **localStorage not clearing**: Verify invalid configuration is properly set
3. **Test commands not working**: Ensure all scripts loaded properly (check for 404 errors)
4. **Authentication stuck**: Clear browser cache and localStorage manually

## Example Test Sequence

```javascript
// Complete test sequence for all 8 scenarios
console.log('Starting 8-scenario authentication test...');

// Scenario 1: First visit, valid JSON, no localStorage
TestConfigs.setValidConfig();
TestConfigs.clearAuthStorage();
location.reload(); // Should show overlay + login

// After reload, continue with scenario 2
TestConfigs.setMockLocalStorage();
location.reload(); // Should not show overlay

// Continue with remaining scenarios...
```

This implementation provides a comprehensive testing framework for all 8 authentication scenarios while maintaining compatibility with the existing MSAL authentication system.