/**
 * Shared 8-Scenario Authentication Flow Module
 * This module provides reusable authentication logic for both aspenONE.html and PBItemDetails.asp
 * 
 * 8 Scenarios:
 * 1. First visit (no localStorage) + valid JSON -> show overlay + login panel
 * 2. Revisit + localStorage + valid JSON -> no overlay, no login panel
 * 3. Invalid JSON + any localStorage -> clear localStorage, no overlay, no login panel
 * 4. Refresh after invalid JSON -> no overlay, no login panel (localStorage already cleared)
 * 5. Invalid->Valid JSON + no localStorage -> show overlay + login panel
 * 6. Valid JSON + localStorage (after login) -> no overlay, no login panel
 * 7. Valid->Invalid JSON + localStorage -> clear localStorage, no overlay, no login panel
 * 8. Refresh after clearing -> no overlay, no login panel
 */

(function() {
    'use strict';
    
    // Namespace for shared authentication functions
    window.SharedAuth = window.SharedAuth || {};
    
    /**
     * Check if JSON configuration is valid (not placeholder values)
     */
    window.SharedAuth.isJSONConfigValid = function() {
        try {
            if (!window.msalConfig || !window.msalConfig.auth) {
                console.log('SharedAuth: msalConfig not available');
                return false;
            }
            
            var clientId = window.msalConfig.auth.clientId;
            var authority = window.msalConfig.auth.authority;
            
            var isValidClientId = clientId && 
                clientId !== "Enter_the_Application_Id_Here" && 
                /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId);
            
            var isValidAuthority = authority && 
                authority !== "https://login.microsoftonline.com/Enter_the_Tenant_ID_Here" && 
                authority.includes('login.microsoftonline.com');
            
            var isValid = isValidClientId && isValidAuthority;
            console.log('SharedAuth: JSON Config validation:', {
                clientId: clientId,
                authority: authority,
                isValidClientId: isValidClientId,
                isValidAuthority: isValidAuthority,
                isValid: isValid
            });
            
            return isValid;
        } catch (error) {
            console.error('SharedAuth: Error validating JSON config:', error);
            return false;
        }
    };
    
    /**
     * Check if user has authentication in localStorage
     */
    window.SharedAuth.hasValidLocalStorage = function() {
        try {
            var userProfile = localStorage.getItem('UserProfile');
            var msalUsername = localStorage.getItem('msal.username');
            var accessToken = localStorage.getItem('msal.accessToken');
            
            var hasUserProfile = userProfile && userProfile !== 'null' && userProfile !== 'undefined';
            var hasUsername = msalUsername && msalUsername !== 'null' && msalUsername !== 'undefined';
            var hasToken = accessToken && accessToken !== 'null' && accessToken !== 'undefined';
            
            var isValid = hasUserProfile || hasUsername || hasToken;
            console.log('SharedAuth: LocalStorage validation:', {
                hasUserProfile: hasUserProfile,
                hasUsername: hasUsername,
                hasToken: hasToken,
                isValid: isValid
            });
            
            return isValid;
        } catch (error) {
            console.error('SharedAuth: Error checking localStorage:', error);
            return false;
        }
    };
    
    /**
     * Clear all authentication data from localStorage
     */
    window.SharedAuth.clearAllAuthData = function() {
        try {
            console.log('SharedAuth: Clearing all authentication data...');
            
            // Clear MSAL-related items
            var keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.startsWith('msal.') || key === 'UserProfile' || key === 'processExplorer_session')) {
                    keysToRemove.push(key);
                }
            }
            
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
                console.log('SharedAuth: Removed localStorage key:', key);
            });
            
            // Clear sessionStorage as well
            var sessionKeysToRemove = [];
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                if (key && key.startsWith('msal.')) {
                    sessionKeysToRemove.push(key);
                }
            }
            
            sessionKeysToRemove.forEach(key => {
                sessionStorage.removeItem(key);
                console.log('SharedAuth: Removed sessionStorage key:', key);
            });
            
            // Clear global authentication flags
            window.authenticationSuccessful = false;
            window.authenticationComplete = false;
            window.UserProfile = null;
            window.g_jwtUsername = null;
            
            console.log('SharedAuth: All authentication data cleared');
        } catch (error) {
            console.error('SharedAuth: Error clearing auth data:', error);
        }
    };
    
    /**
     * Determine authentication flow based on 8 scenarios
     * Returns: { showOverlay: boolean, showLoginPanel: boolean, clearData: boolean, scenario: string }
     */
    window.SharedAuth.determineAuthFlow = function() {
        var jsonValid = window.SharedAuth.isJSONConfigValid();
        var hasLocalStorage = window.SharedAuth.hasValidLocalStorage();
        
        console.log('SharedAuth: Auth flow determination:', {
            jsonValid: jsonValid,
            hasLocalStorage: hasLocalStorage
        });
        
        // Scenario logic:
        // 1. First visit (no localStorage) + valid JSON -> show overlay + login panel
        // 2. Revisit + localStorage + valid JSON -> no overlay, no login panel  
        // 3. Invalid JSON + any localStorage -> clear localStorage, no overlay, no login panel
        // 4. Refresh after invalid JSON -> no overlay, no login panel (localStorage already cleared)
        // 5. Invalid->Valid JSON + no localStorage -> show overlay + login panel
        // 6. Valid JSON + localStorage (after login) -> no overlay, no login panel
        // 7. Valid->Invalid JSON + localStorage -> clear localStorage, no overlay, no login panel
        // 8. Refresh after clearing -> no overlay, no login panel
        
        if (!jsonValid) {
            // Scenarios 3, 4, 7, 8: Invalid JSON
            console.log('SharedAuth: Invalid JSON - clearing data and no overlay');
            return { 
                showOverlay: false, 
                showLoginPanel: false, 
                clearData: true, 
                scenario: hasLocalStorage ? 'Scenario 3 or 7' : 'Scenario 4 or 8'
            };
        } else {
            // Valid JSON
            if (hasLocalStorage) {
                // Scenarios 2, 6: Valid JSON + localStorage
                console.log('SharedAuth: Valid JSON + localStorage - no overlay needed');
                return { 
                    showOverlay: false, 
                    showLoginPanel: false, 
                    clearData: false, 
                    scenario: 'Scenario 2 or 6'
                };
            } else {
                // Scenarios 1, 5: Valid JSON + no localStorage
                console.log('SharedAuth: Valid JSON + no localStorage - show overlay and login');
                return { 
                    showOverlay: true, 
                    showLoginPanel: true, 
                    clearData: false, 
                    scenario: 'Scenario 1 or 5'
                };
            }
        }
    };
    
    /**
     * Check if MSAL is properly configured (same logic as AuthManager.checkMSALConfiguration in auth.js)
     */
    window.SharedAuth.checkMSALConfiguration = function() {
        try {
            // Check if MSAL config objects exist
            if (typeof msalConfig === 'undefined' || typeof msal === 'undefined') {
                console.log('SharedAuth: MSAL libraries not loaded');
                return false;
            }

            // Validate MSAL configuration
            if (!msalConfig.auth || !msalConfig.auth.clientId || !msalConfig.auth.authority) {
                console.log('SharedAuth: MSAL configuration incomplete');
                return false;
            }

            // Check for placeholder clientId values
            if (msalConfig.auth.clientId === "Enter_the_Application_Id_Here" || 
                msalConfig.auth.clientId.includes("Enter_the_Application_Id")) {
                console.log('SharedAuth: clientId contains placeholder value - configuration not complete');
                return false;
            }

            // Validate clientId format (should be a GUID)
            var guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            if (!guidRegex.test(msalConfig.auth.clientId)) {
                console.log('SharedAuth: Invalid clientId format - must be a valid GUID');
                return false;
            }

            // Validate authority URL
            try {
                var authorityUrl = new URL(msalConfig.auth.authority);
                
                // Check for Microsoft domains
                if (!authorityUrl.hostname.includes('login.microsoftonline.com') && 
                    !authorityUrl.hostname.includes('login.microsoft.com')) {
                    console.log('SharedAuth: Invalid authority URL - must use Microsoft login domain');
                    return false;
                }

                // Extract tenant ID from authority URL path
                var pathSegments = authorityUrl.pathname.split('/').filter(function(segment) { return segment.length > 0; });
                if (pathSegments.length > 0) {
                    var tenantId = pathSegments[0];
                    
                    // Check for placeholder tenant ID
                    if (tenantId === "Enter_the_Tenant_ID_Here" || tenantId.includes("Enter_the_Tenant_ID")) {
                        console.log('SharedAuth: authority contains placeholder tenant ID - configuration not complete');
                        return false;
                    }
                    
                    // Check if tenant ID is a valid GUID (unless it's a special tenant like 'common', 'organizations', 'consumers')
                    var specialTenants = ['common', 'organizations', 'consumers'];
                    if (specialTenants.indexOf(tenantId.toLowerCase()) === -1 && !guidRegex.test(tenantId)) {
                        console.log('SharedAuth: Invalid tenant ID format in authority URL - must be a valid GUID or special tenant');
                        return false;
                    }
                }
            } catch (e) {
                console.log('SharedAuth: Malformed authority URL');
                return false;
            }

            console.log('SharedAuth: MSAL properly configured');
            return true;

        } catch (error) {
            console.error('SharedAuth: Error checking MSAL configuration:', error);
            return false;
        }
    };
    
    /**
     * Extract username from JWT token payload
     * @param {string} token - The JWT access token
     * @returns {string|null} Username from token claims
     */
    window.SharedAuth.extractUsernameFromJWTToken = function(token) {
        try {
            // JWT has 3 parts separated by dots
            var parts = token.split('.');
            if (parts.length >= 2) {
                // Decode the payload (second part)
                var payload = parts[1];
                
                // Add padding if needed for base64 decoding
                while (payload.length % 4) {
                    payload += '=';
                }
                
                // Decode base64
                var decodedPayload = atob(payload);
                var claims = JSON.parse(decodedPayload);
                
                // Try different username claims in order of preference
                return claims.preferred_username || claims.upn || claims.unique_name || claims.name || null;
            }
        } catch (error) {
            console.warn('SharedAuth: Error extracting username from JWT token: ' + error.message);
        }
        
        return null;
    };
    
    /**
     * Get JWT username with multiple fallback sources
     * @param {string} context - Context identifier for logging (e.g., 'aspenONE' or 'PBItemDetails')
     * @returns {string|null} Username
     */
    window.SharedAuth.getJWTUsername = function(context) {
        context = context || 'SharedAuth';
        
        try {
            console.log(context + ': Getting JWT username...');
            
            // Priority 1: Try to get UserProfile from localStorage
            try {
                var storedUserProfile = localStorage.getItem('UserProfile');
                if (storedUserProfile && storedUserProfile !== 'null' && storedUserProfile !== 'undefined') {
                    var userProfile = JSON.parse(storedUserProfile);
                    if (userProfile && userProfile.account) {
                        var username = userProfile.account.replace('/', '\\'); // Convert CORP/HUAF to CORP\HUAF
                        console.log(context + ': JWT username from localStorage UserProfile (Priority 1): ' + username);
                        window.g_jwtUsername = username; // Store globally
                        return username;
                    }
                }
            } catch (parseError) {
                console.warn(context + ': Error parsing UserProfile from localStorage:', parseError);
            }
            
            // Priority 2: Try to get from localStorage msal.username
            var msalUsername = localStorage.getItem('msal.username');
            if (msalUsername && msalUsername !== 'null' && msalUsername !== 'undefined' && msalUsername !== '') {
                console.log(context + ': JWT username from localStorage msal.username (Priority 2): ' + msalUsername);
                window.g_jwtUsername = msalUsername;
                return msalUsername;
            }
            
            // Priority 3: Use parent window UserProfile.account
            if (window.parent && window.parent.UserProfile && window.parent.UserProfile.account) {
                var username = window.parent.UserProfile.account.replace('/', '\\');
                console.log(context + ': JWT username from parent UserProfile.account (Priority 3): ' + username);
                window.g_jwtUsername = username;
                return username;
            }
            
            // Priority 4: Use current window UserProfile
            if (window.UserProfile && window.UserProfile.account) {
                var username = window.UserProfile.account.replace('/', '\\');
                console.log(context + ': JWT username from window UserProfile.account (Priority 4): ' + username);
                window.g_jwtUsername = username;
                return username;
            }
            
            // Priority 5: Try to get username from global MSAL instance
            if (window.myMSALObj) {
                try {
                    var account = window.myMSALObj.getActiveAccount();
                    if (account && account.username) {
                        console.log(context + ': JWT username from myMSALObj active account (Priority 5): ' + account.username);
                        window.g_jwtUsername = account.username;
                        return account.username;
                    }
                    
                    var accounts = window.myMSALObj.getAllAccounts();
                    if (accounts && accounts.length > 0 && accounts[0].username) {
                        console.log(context + ': JWT username from myMSALObj getAllAccounts (Priority 5b): ' + accounts[0].username);
                        window.g_jwtUsername = accounts[0].username;
                        return accounts[0].username;
                    }
                } catch (msalError) {
                    console.warn(context + ': Error accessing myMSALObj:', msalError);
                }
            }
            
            // Priority 6: Try to extract from JWT token in localStorage
            var accessToken = localStorage.getItem('msal.accessToken');
            if (accessToken && accessToken !== 'null' && accessToken !== 'undefined' && accessToken !== '') {
                var username = window.SharedAuth.extractUsernameFromJWTToken(accessToken);
                if (username) {
                    console.log(context + ': JWT username extracted from localStorage token (Priority 6): ' + username);
                    window.g_jwtUsername = username;
                    return username;
                }
            }
            
            // Priority 7: Try to extract from JWT token in sessionStorage
            var sessionToken = sessionStorage.getItem('msal.accessToken');
            if (sessionToken && sessionToken !== 'null' && sessionToken !== 'undefined' && sessionToken !== '') {
                var username = window.SharedAuth.extractUsernameFromJWTToken(sessionToken);
                if (username) {
                    console.log(context + ': JWT username extracted from sessionStorage token (Priority 7): ' + username);
                    window.g_jwtUsername = username;
                    return username;
                }
            }
            
            console.log(context + ': No JWT username found, will fallback to Windows auth');
            window.g_jwtUsername = null;
            return null;
            
        } catch (error) {
            console.warn(context + ': Error getting JWT username: ' + error.message);
            window.g_jwtUsername = null;
            return null;
        }
    };
    
    console.log('SharedAuth: 8-Scenario Authentication Flow Module loaded successfully');
    
})();