/**
 * Enhanced Authentication Flow for 8-Scenario Testing
 * 
 * This module provides the enhanced authentication logic for both
 * PBItemDetails.asp and aspenONE.html to handle the 8 test scenarios:
 * 
 * 1. First visit, valid JSON, no localStorage → show overlay + login panel
 * 2. <PERSON>isit, valid JSON, has localStorage → no overlay
 * 3. Valid→Invalid JSON, refresh → clear localStorage, no overlay
 * 4. Refresh invalid JSON → clear localStorage, no overlay  
 * 5. Invalid→Valid JSON, refresh → show overlay + login panel
 * 6. Refresh valid JSON + localStorage → no overlay
 * 7. Valid→Invalid again → clear localStorage, no overlay
 * 8. Refresh invalid again → clear localStorage, no overlay
 */

(function(window) {
    'use strict';

    // Enhanced Authentication Flow Manager
    const EnhancedAuthFlow = {
        
        /**
         * Main entry point for authentication scenario checking
         */
        checkAuthenticationScenarios: function() {
            console.log('EnhancedAuthFlow: ========== CHECKING AUTHENTICATION SCENARIOS ==========');
            
            // Check JSON configuration validity
            const isValidConfig = this.checkMSALConfiguration();
            console.log('EnhancedAuthFlow: Configuration valid:', isValidConfig);
            
            // Check localStorage state  
            const hasAuthData = this.checkLocalStorageAuth();
            console.log('EnhancedAuthFlow: Has auth data:', hasAuthData);
            
            // Determine authentication action based on scenario
            const authAction = this.determineAuthAction(isValidConfig, hasAuthData);
            console.log('EnhancedAuthFlow: Auth action:', authAction);
            
            // Execute the determined action
            this.executeAuthAction(authAction);
            
            return authAction;
        },
        
        /**
         * Check if MSAL configuration is valid
         */
        checkMSALConfiguration: function() {
            try {
                // Check if MSAL config objects exist
                if (typeof msalConfig === 'undefined' || typeof msal === 'undefined') {
                    console.log('EnhancedAuthFlow: MSAL libraries not loaded');
                    return false;
                }

                // Validate MSAL configuration
                if (!msalConfig.auth || !msalConfig.auth.clientId || !msalConfig.auth.authority) {
                    console.log('EnhancedAuthFlow: MSAL configuration incomplete');
                    return false;
                }

                // Check for placeholder clientId values
                if (msalConfig.auth.clientId === "Enter_the_Application_Id_Here" || 
                    msalConfig.auth.clientId.includes("Enter_the_Application_Id")) {
                    console.log('EnhancedAuthFlow: clientId contains placeholder value - configuration not complete');
                    return false;
                }

                // Validate clientId format (should be a GUID)
                const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
                if (!guidRegex.test(msalConfig.auth.clientId)) {
                    console.log('EnhancedAuthFlow: Invalid clientId format - must be a valid GUID');
                    return false;
                }

                // Validate authority URL
                try {
                    const authorityUrl = new URL(msalConfig.auth.authority);
                    
                    // Check for Microsoft domains
                    if (!authorityUrl.hostname.includes('login.microsoftonline.com') && 
                        !authorityUrl.hostname.includes('login.microsoft.com')) {
                        console.log('EnhancedAuthFlow: Invalid authority URL - must use Microsoft login domain');
                        return false;
                    }

                    // Extract tenant ID from authority URL path
                    const pathSegments = authorityUrl.pathname.split('/').filter(function(segment) { return segment.length > 0; });
                    if (pathSegments.length > 0) {
                        const tenantId = pathSegments[0];
                        
                        // Check for placeholder tenant ID
                        if (tenantId === "Enter_the_Tenant_ID_Here" || tenantId.includes("Enter_the_Tenant_ID")) {
                            console.log('EnhancedAuthFlow: authority contains placeholder tenant ID - configuration not complete');
                            return false;
                        }
                        
                        // Check if tenant ID is a valid GUID (unless it's a special tenant)
                        const specialTenants = ['common', 'organizations', 'consumers'];
                        if (specialTenants.indexOf(tenantId.toLowerCase()) === -1 && !guidRegex.test(tenantId)) {
                            console.log('EnhancedAuthFlow: Invalid tenant ID format in authority URL - must be a valid GUID or special tenant');
                            return false;
                        }
                    }
                } catch (e) {
                    console.log('EnhancedAuthFlow: Malformed authority URL');
                    return false;
                }

                console.log('EnhancedAuthFlow: MSAL properly configured');
                return true;

            } catch (error) {
                console.error('EnhancedAuthFlow: Error checking MSAL configuration:', error);
                return false;
            }
        },
        
        /**
         * Check if we have valid authentication data in localStorage
         */
        checkLocalStorageAuth: function() {
            try {
                const userProfile = localStorage.getItem('UserProfile');
                const msalUsername = localStorage.getItem('msal.username');
                const accessToken = localStorage.getItem('msal.accessToken');
                
                const hasUserProfile = userProfile && userProfile !== 'null' && userProfile !== 'undefined';
                const hasUsername = msalUsername && msalUsername !== 'null' && msalUsername !== 'undefined';
                const hasToken = accessToken && accessToken !== 'null' && accessToken !== 'undefined';
                
                console.log('EnhancedAuthFlow: LocalStorage check:', {
                    userProfile: !!hasUserProfile,
                    username: !!hasUsername,
                    token: !!hasToken
                });
                
                return hasUserProfile || hasUsername;
            } catch (error) {
                console.warn('EnhancedAuthFlow: Error checking localStorage:', error);
                return false;
            }
        },
        
        /**
         * Determine what authentication action to take based on configuration and auth state
         */
        determineAuthAction: function(isValidConfig, hasAuthData) {
            if (!isValidConfig) {
                // Invalid configuration scenarios (3, 4, 7, 8)
                if (hasAuthData) {
                    return 'clear_storage_no_overlay'; // Clear localStorage, no overlay
                } else {
                    return 'no_overlay'; // Just no overlay
                }
            } else {
                // Valid configuration scenarios (1, 2, 5, 6)
                if (hasAuthData) {
                    return 'no_overlay'; // Has auth data, continue normally (2, 6)
                } else {
                    return 'show_overlay_login'; // No auth data, show overlay + login (1, 5)
                }
            }
        },
        
        /**
         * Execute the determined authentication action
         */
        executeAuthAction: function(action) {
            switch (action) {
                case 'show_overlay_login':
                    console.log('EnhancedAuthFlow: Executing - Show overlay + login panel');
                    this.showAuthOverlay();
                    this.showLoginPanel();
                    break;
                    
                case 'clear_storage_no_overlay':
                    console.log('EnhancedAuthFlow: Executing - Clear localStorage, no overlay');
                    this.clearAuthenticationStorage();
                    this.hideAuthOverlay();
                    this.markAuthenticationComplete();
                    break;
                    
                case 'no_overlay':
                    console.log('EnhancedAuthFlow: Executing - No overlay, continue normally');
                    this.hideAuthOverlay();
                    this.markAuthenticationComplete();
                    break;
                    
                default:
                    console.warn('EnhancedAuthFlow: Unknown auth action:', action);
                    this.hideAuthOverlay();
                    break;
            }
        },
        
        /**
         * Show authentication overlay
         */
        showAuthOverlay: function() {
            const overlay = document.getElementById('authOverlay');
            if (overlay) {
                overlay.style.display = 'flex';
                console.log('EnhancedAuthFlow: Authentication overlay shown');
            } else if (window.showAuthOverlay && typeof window.showAuthOverlay === 'function') {
                window.showAuthOverlay();
            }
        },
        
        /**
         * Hide authentication overlay
         */
        hideAuthOverlay: function() {
            const overlay = document.getElementById('authOverlay');
            if (overlay) {
                overlay.style.display = 'none';
                console.log('EnhancedAuthFlow: Authentication overlay hidden');
            } else if (window.hideAuthOverlay && typeof window.hideAuthOverlay === 'function') {
                window.hideAuthOverlay();
            }
        },
        
        /**
         * Show login panel
         */
        showLoginPanel: function() {
            const loginSection = document.getElementById('authLoginSection');
            const loadingSection = document.getElementById('authLoadingSection');
            
            if (loginSection) {
                loginSection.style.display = 'block';
            }
            if (loadingSection) {
                loadingSection.style.display = 'none';
            }
            
            console.log('EnhancedAuthFlow: Login panel shown');
        },
        
        /**
         * Clear all authentication-related storage
         */
        clearAuthenticationStorage: function() {
            try {
                console.log('EnhancedAuthFlow: Clearing authentication storage');
                
                // Clear localStorage
                localStorage.removeItem('UserProfile');
                localStorage.removeItem('msal.username');
                localStorage.removeItem('msal.accessToken');
                localStorage.removeItem('msal.idToken');
                localStorage.removeItem('msal.account');
                
                // Clear sessionStorage
                sessionStorage.removeItem('msal.username');
                sessionStorage.removeItem('msal.accessToken');
                sessionStorage.removeItem('msal.idToken');
                sessionStorage.removeItem('msal.account');
                
                // Clear window objects
                if (window.UserProfile) {
                    window.UserProfile = {};
                }
                if (window.g_jwtUsername) {
                    window.g_jwtUsername = null;
                }
                
                console.log('EnhancedAuthFlow: Authentication storage cleared');
            } catch (error) {
                console.error('EnhancedAuthFlow: Error clearing authentication storage:', error);
            }
        },
        
        /**
         * Mark authentication as complete for pages that need it
         */
        markAuthenticationComplete: function() {
            window.authenticationSuccessful = true;
            window.authenticationComplete = true;
            console.log('EnhancedAuthFlow: Authentication marked as complete');
        },
        
        /**
         * Test scenario runner for manual testing
         */
        runTestScenario: function(scenarioNumber) {
            console.log('EnhancedAuthFlow: Running test scenario', scenarioNumber);
            
            const scenarios = {
                1: () => {
                    // First visit, valid JSON, no localStorage
                    this.setValidConfig();
                    this.clearAuthenticationStorage();
                    this.checkAuthenticationScenarios();
                },
                2: () => {
                    // Revisit, valid JSON, has localStorage
                    this.setValidConfig();
                    this.setMockLocalStorage();
                    this.checkAuthenticationScenarios();
                },
                3: () => {
                    // Change to invalid, refresh
                    this.setInvalidConfig();
                    this.checkAuthenticationScenarios();
                },
                4: () => {
                    // Refresh with invalid
                    this.setInvalidConfig();
                    this.checkAuthenticationScenarios();
                },
                5: () => {
                    // Change to valid, refresh
                    this.setValidConfig();
                    this.clearAuthenticationStorage();
                    this.checkAuthenticationScenarios();
                },
                6: () => {
                    // Refresh with valid + localStorage
                    this.setValidConfig();
                    this.setMockLocalStorage();
                    this.checkAuthenticationScenarios();
                },
                7: () => {
                    // Change back to invalid
                    this.setInvalidConfig();
                    this.checkAuthenticationScenarios();
                },
                8: () => {
                    // Refresh with invalid
                    this.setInvalidConfig();
                    this.checkAuthenticationScenarios();
                }
            };
            
            if (scenarios[scenarioNumber]) {
                scenarios[scenarioNumber]();
            } else {
                console.error('EnhancedAuthFlow: Unknown scenario:', scenarioNumber);
            }
        },
        
        /**
         * Helper: Set valid config for testing
         */
        setValidConfig: function() {
            if (typeof window.msalConfig === 'undefined') {
                window.msalConfig = {};
            }
            window.msalConfig.auth = {
                clientId: "12345678-1234-1234-1234-123456789abc",
                authority: "https://login.microsoftonline.com/your-tenant-id"
            };
            console.log('EnhancedAuthFlow: Set valid config for testing');
        },
        
        /**
         * Helper: Set invalid config for testing
         */
        setInvalidConfig: function() {
            if (typeof window.msalConfig === 'undefined') {
                window.msalConfig = {};
            }
            window.msalConfig.auth = {
                clientId: "Enter_the_Application_Id_Here",
                authority: "https://login.microsoftonline.com/Enter_the_Tenant_ID_Here"
            };
            console.log('EnhancedAuthFlow: Set invalid config for testing');
        },
        
        /**
         * Helper: Set mock localStorage for testing
         */
        setMockLocalStorage: function() {
            const mockProfile = {
                account: "CORP\\testuser",
                userName: "Test User",
                email: "<EMAIL>",
                version: "1.0"
            };
            localStorage.setItem('UserProfile', JSON.stringify(mockProfile));
            localStorage.setItem('msal.username', 'CORP\\testuser');
            localStorage.setItem('msal.accessToken', 'mock-access-token');
            console.log('EnhancedAuthFlow: Set mock localStorage for testing');
        }
    };

    // Expose to global scope
    window.EnhancedAuthFlow = EnhancedAuthFlow;
    
    // Auto-initialize if DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            console.log('EnhancedAuthFlow: DOM loaded, authentication flow available');
        });
    } else {
        console.log('EnhancedAuthFlow: Loaded and ready');
    }

})(window);