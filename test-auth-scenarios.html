<!DOCTYPE html>
<html>
<head>
    <title>Authentication Flow Test Scenarios</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .scenario { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .controls { margin: 20px 0; padding: 15px; background: #f5f5f5; border-radius: 5px; }
        button { margin: 5px; padding: 10px 15px; cursor: pointer; }
        .valid { background: #4CAF50; color: white; }
        .invalid { background: #f44336; color: white; }
        .test { background: #2196F3; color: white; }
        .status { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        
        /* Authentication Overlay Styles */
        .auth-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 20000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .auth-container {
            max-width: 600px;
            margin: 0 20px;
            text-align: center;
        }

        .auth-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin: 20px 0;
        }

        .auth-login-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 10px;
        }

        .auth-login-button:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Authentication Overlay -->
    <div id="authOverlay" class="auth-overlay" style="display: none;">
        <div class="auth-container">
            <div class="auth-card">
                <h1>ProcessExplorer Authentication</h1>
                <p>Please sign in with your Microsoft account to access ProcessExplorer</p>
                
                <div id="authStatusMessage" style="display: none;"></div>
                
                <div id="authLoginSection">
                    <button id="authLoginBtn" class="auth-login-button">Sign In with Microsoft</button>
                    <p><small>You will be redirected to Microsoft's secure login page</small></p>
                </div>
            </div>
        </div>
    </div>

    <h1>Authentication Flow Test Scenarios</h1>
    
    <div class="controls">
        <h3>Configuration Controls</h3>
        <button class="valid" onclick="setValidJSON()">Set Valid JSON Config</button>
        <button class="invalid" onclick="setInvalidJSON()">Set Invalid JSON Config</button>
        <button onclick="clearLocalStorage()">Clear LocalStorage</button>
        <button onclick="setMockLocalStorage()">Set Mock LocalStorage</button>
        <button onclick="refreshPage()">Refresh Page</button>
    </div>

    <div id="statusDisplay" class="status info">
        Current Status: <span id="currentStatus">Loading...</span>
    </div>

    <div class="controls">
        <h3>Test Scenarios</h3>
        <button class="test" onclick="runScenario(1)">1. First visit, valid JSON, no localStorage</button>
        <button class="test" onclick="runScenario(2)">2. Revisit, valid JSON, has localStorage</button>
        <button class="test" onclick="runScenario(3)">3. Change valid→invalid, refresh</button>
        <button class="test" onclick="runScenario(4)">4. Refresh with invalid JSON</button>
        <button class="test" onclick="runScenario(5)">5. Change invalid→valid, refresh</button>
        <button class="test" onclick="runScenario(6)">6. Refresh with valid JSON + localStorage</button>
        <button class="test" onclick="runScenario(7)">7. Change valid→invalid again</button>
        <button class="test" onclick="runScenario(8)">8. Refresh with invalid JSON again</button>
    </div>

    <div id="scenarioResults"></div>

    <script>
        // Mock MSAL Configuration
        let currentConfig = {
            clientId: "Enter_the_Application_Id_Here",
            authority: "https://login.microsoftonline.com/Enter_the_Tenant_ID_Here"
        };

        const validConfig = {
            clientId: "12345678-1234-1234-1234-123456789abc",
            authority: "https://login.microsoftonline.com/your-tenant-id"
        };

        const invalidConfig = {
            clientId: "Enter_the_Application_Id_Here",
            authority: "https://login.microsoftonline.com/Enter_the_Tenant_ID_Here"
        };

        // Authentication state management
        function isValidJSON() {
            return currentConfig.clientId !== "Enter_the_Application_Id_Here" && 
                   currentConfig.authority !== "https://login.microsoftonline.com/Enter_the_Tenant_ID_Here" &&
                   /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(currentConfig.clientId);
        }

        function hasLocalStorage() {
            return localStorage.getItem('UserProfile') && 
                   localStorage.getItem('UserProfile') !== 'null' && 
                   localStorage.getItem('msal.username');
        }

        function shouldShowOverlay() {
            const validJSON = isValidJSON();
            const hasAuth = hasLocalStorage();
            
            console.log('Auth check:', { validJSON, hasAuth });
            
            if (!validJSON) {
                // Invalid JSON - no overlay, clear localStorage
                if (hasAuth) {
                    clearLocalStorage();
                    updateStatus('Invalid JSON detected, cleared localStorage', 'info');
                }
                return false;
            }
            
            // Valid JSON - show overlay only if no localStorage
            return !hasAuth;
        }

        function showAuthOverlay() {
            document.getElementById('authOverlay').style.display = 'flex';
            updateStatus('Authentication overlay shown - please login', 'error');
        }

        function hideAuthOverlay() {
            document.getElementById('authOverlay').style.display = 'none';
            updateStatus('Authentication overlay hidden - access granted', 'success');
        }

        function setValidJSON() {
            currentConfig = { ...validConfig };
            updateStatus('Configuration set to VALID', 'success');
            checkAuthenticationFlow();
        }

        function setInvalidJSON() {
            currentConfig = { ...invalidConfig };
            updateStatus('Configuration set to INVALID', 'error');
            checkAuthenticationFlow();
        }

        function clearLocalStorage() {
            localStorage.removeItem('UserProfile');
            localStorage.removeItem('msal.username');
            localStorage.removeItem('msal.accessToken');
            localStorage.removeItem('msal.idToken');
            updateStatus('LocalStorage cleared', 'info');
            checkAuthenticationFlow();
        }

        function setMockLocalStorage() {
            const mockProfile = {
                account: "CORP\\testuser",
                userName: "Test User",
                email: "<EMAIL>",
                version: "1.0"
            };
            localStorage.setItem('UserProfile', JSON.stringify(mockProfile));
            localStorage.setItem('msal.username', 'CORP\\testuser');
            localStorage.setItem('msal.accessToken', 'mock-access-token');
            updateStatus('Mock localStorage data set', 'success');
            checkAuthenticationFlow();
        }

        function refreshPage() {
            location.reload();
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('statusDisplay');
            const statusSpan = document.getElementById('currentStatus');
            
            statusSpan.textContent = message;
            statusDiv.className = `status ${type}`;
            
            // Also log to console
            console.log(`Status (${type}): ${message}`);
        }

        function checkAuthenticationFlow() {
            const validJSON = isValidJSON();
            const hasAuth = hasLocalStorage();
            const showOverlay = shouldShowOverlay();
            
            const statusText = `JSON: ${validJSON ? 'Valid' : 'Invalid'}, LocalStorage: ${hasAuth ? 'Present' : 'None'}, Overlay: ${showOverlay ? 'Show' : 'Hide'}`;
            
            if (showOverlay) {
                showAuthOverlay();
            } else {
                hideAuthOverlay();
            }
            
            updateStatus(statusText, showOverlay ? 'error' : 'success');
        }

        function runScenario(number) {
            const scenarios = {
                1: () => {
                    // First visit, valid JSON, no localStorage → see overlay then login panel
                    setValidJSON();
                    clearLocalStorage();
                    logScenario(1, "First visit with valid JSON, no localStorage", "Should show overlay + login panel");
                },
                2: () => {
                    // Revisit, has localStorage → no overlay, no login panel
                    setValidJSON();
                    setMockLocalStorage();
                    logScenario(2, "Revisit with valid JSON and localStorage", "Should not show overlay");
                },
                3: () => {
                    // Change to invalid, refresh → should not see overlay and clear localStorage
                    setInvalidJSON();
                    logScenario(3, "Changed JSON to invalid, refreshing", "Should clear localStorage and not show overlay");
                },
                4: () => {
                    // Refresh with invalid → should not see overlay and clear localStorage
                    setInvalidJSON();
                    logScenario(4, "Refresh with invalid JSON", "Should not show overlay and clear localStorage");
                },
                5: () => {
                    // Change to valid, refresh → should see overlay and login panel
                    setValidJSON();
                    clearLocalStorage();
                    logScenario(5, "Changed JSON to valid, refreshing", "Should see overlay and login panel");
                },
                6: () => {
                    // Refresh with valid + localStorage → should not see overlay
                    setValidJSON();
                    setMockLocalStorage();
                    logScenario(6, "Refresh with valid JSON and localStorage", "Should not see overlay");
                },
                7: () => {
                    // Change back to invalid → clear localStorage, no overlay
                    setInvalidJSON();
                    logScenario(7, "Changed JSON back to invalid", "Should clear localStorage and not show overlay");
                },
                8: () => {
                    // Refresh with invalid → clear localStorage, no overlay
                    setInvalidJSON();
                    logScenario(8, "Refresh with invalid JSON", "Should clear localStorage and not show overlay");
                }
            };
            
            scenarios[number]();
        }

        function logScenario(number, description, expected) {
            const resultsDiv = document.getElementById('scenarioResults');
            const scenarioDiv = document.createElement('div');
            scenarioDiv.className = 'scenario';
            
            const validJSON = isValidJSON();
            const hasAuth = hasLocalStorage();
            const showOverlay = shouldShowOverlay();
            
            scenarioDiv.innerHTML = `
                <h4>Scenario ${number}: ${description}</h4>
                <p><strong>Expected:</strong> ${expected}</p>
                <p><strong>Actual:</strong> 
                    JSON: ${validJSON ? 'Valid' : 'Invalid'}, 
                    LocalStorage: ${hasAuth ? 'Present' : 'None'}, 
                    Overlay: ${showOverlay ? 'Shown' : 'Hidden'}
                </p>
                <p><strong>Result:</strong> 
                    <span style="color: ${showOverlay === expected.includes('overlay') ? 'green' : 'red'}">
                        ${showOverlay === expected.includes('overlay') ? 'PASS' : 'Review Required'}
                    </span>
                </p>
            `;
            
            resultsDiv.appendChild(scenarioDiv);
            
            // Scroll to the new result
            scenarioDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // Mock login functionality
        document.getElementById('authLoginBtn').addEventListener('click', function() {
            updateStatus('Mock login successful - setting localStorage', 'success');
            setMockLocalStorage();
            hideAuthOverlay();
        });

        // Initialize on page load
        window.addEventListener('load', function() {
            checkAuthenticationFlow();
        });
    </script>
</body>
</html>