# 8-Scenario Authentication Testing Guide

## Overview
This guide provides step-by-step instructions for testing all 8 authentication scenarios across both `aspenONE.html` and `PBItemDetails.asp`.

## Prerequisites
1. Both pages have SharedAuth module integrated
2. Test configuration files are available:
   - `ASP/msal/entraConfig.json.valid-test` (valid configuration)
   - `ASP/msal/entraConfig.json.invalid-test` (invalid configuration)

## Test Setup

### Browser Setup
1. Open browser developer tools (F12)
2. Go to Console tab to monitor authentication logs
3. Go to Application/Storage tab to monitor localStorage

### Configuration Files
- **Valid JSON**: Contains real GUID values for clientId and authority
- **Invalid JSON**: Contains placeholder values like "Enter_the_Application_Id_Here"

## Test Scenarios

### Scenario 1: First Visit + Valid JSON
**Expected**: Show overlay + login panel

**Steps**:
1. Clear all browser data (localStorage, sessionStorage, cookies)
2. Copy `entraConfig.json.valid-test` to `entraConfig.json`
3. Navigate to `aspenONE.html`
4. **Verify**: Authentication overlay appears with login button
5. **Verify**: Console shows "SharedAuth: Valid JSON + no localStorage - show overlay and login"

### Scenario 2: Revisit + Valid JSON  
**Expected**: No overlay, no login panel

**Steps**:
1. Complete Scenario 1 (login successfully)
2. **Verify**: localStorage contains `UserProfile`, `msal.username`, `msal.accessToken`
3. Refresh the page
4. **Verify**: No overlay appears, page loads normally
5. **Verify**: Console shows "SharedAuth: Valid JSON + localStorage - no overlay needed"

### Scenario 3: Invalid JSON + Any localStorage
**Expected**: Clear localStorage, no overlay, no login panel

**Steps**:
1. Start with localStorage from Scenario 2
2. Copy `entraConfig.json.invalid-test` to `entraConfig.json`
3. Refresh the page
4. **Verify**: No overlay appears
5. **Verify**: localStorage is cleared (UserProfile, msal.* keys removed)
6. **Verify**: Console shows "SharedAuth: Invalid JSON - clearing data and no overlay"

### Scenario 4: Refresh After Invalid JSON
**Expected**: No overlay, no login panel

**Steps**:
1. Continue from Scenario 3 (localStorage already cleared)
2. Refresh the page again
3. **Verify**: No overlay appears
4. **Verify**: localStorage remains empty
5. **Verify**: Console shows same as Scenario 3

### Scenario 5: Invalid→Valid JSON Change
**Expected**: Show overlay + login panel

**Steps**:
1. Start with invalid JSON and empty localStorage (from Scenario 4)
2. Copy `entraConfig.json.valid-test` to `entraConfig.json`
3. Refresh the page
4. **Verify**: Authentication overlay appears with login button
5. **Verify**: Console shows "SharedAuth: Valid JSON + no localStorage - show overlay and login"

### Scenario 6: Valid JSON + Post-Login localStorage
**Expected**: No overlay, no login panel

**Steps**:
1. Complete Scenario 5 (login successfully)
2. **Verify**: localStorage contains authentication data
3. Refresh the page
4. **Verify**: No overlay appears, page loads normally
5. **Verify**: Same behavior as Scenario 2

### Scenario 7: Valid→Invalid JSON Change
**Expected**: Clear localStorage, no overlay, no login panel

**Steps**:
1. Start with valid JSON and localStorage (from Scenario 6)
2. Copy `entraConfig.json.invalid-test` to `entraConfig.json`
3. Refresh the page
4. **Verify**: No overlay appears
5. **Verify**: localStorage is cleared
6. **Verify**: Same behavior as Scenario 3

### Scenario 8: Refresh After Clearing
**Expected**: No overlay, no login panel

**Steps**:
1. Continue from Scenario 7 (localStorage cleared)
2. Refresh the page again
3. **Verify**: No overlay appears
4. **Verify**: localStorage remains empty
5. **Verify**: Same behavior as Scenario 4

## Cross-Page Testing

### Test Both Pages
Repeat all 8 scenarios for both:
- `aspenONE.html`
- `PBItemDetails.asp`

### Test Page Navigation
1. Set up Scenario 2 (valid JSON + localStorage) on `aspenONE.html`
2. Navigate to `PBItemDetails.asp`
3. **Verify**: No authentication required, localStorage is shared
4. Navigate back to `aspenONE.html`
5. **Verify**: Still authenticated

## Debugging Tools

### Console Commands
```javascript
// Check SharedAuth status
SharedAuth.isJSONConfigValid()
SharedAuth.hasValidLocalStorage()
SharedAuth.determineAuthFlow()

// Check localStorage
localStorage.getItem('UserProfile')
localStorage.getItem('msal.username')
localStorage.getItem('msal.accessToken')

// Clear authentication data
SharedAuth.clearAllAuthData()
```

### Expected Console Messages
- **Valid JSON**: "SharedAuth: JSON Config validation: {...isValid: true}"
- **Invalid JSON**: "SharedAuth: JSON Config validation: {...isValid: false}"
- **Has localStorage**: "SharedAuth: LocalStorage validation: {...isValid: true}"
- **No localStorage**: "SharedAuth: LocalStorage validation: {...isValid: false}"

## Troubleshooting

### Common Issues
1. **SharedAuth not loaded**: Check script import in HTML
2. **JSON not updating**: Clear browser cache, check file path
3. **localStorage not clearing**: Check browser security settings
4. **Overlay stuck**: Check console for JavaScript errors

### Reset to Clean State
```javascript
// Complete reset
SharedAuth.clearAllAuthData();
sessionStorage.clear();
location.reload();
```

## Success Criteria
- [ ] All 8 scenarios work on aspenONE.html
- [ ] All 8 scenarios work on PBItemDetails.asp  
- [ ] localStorage is properly managed across scenarios
- [ ] Cross-page navigation maintains authentication state
- [ ] Console logs show correct SharedAuth decisions
- [ ] No JavaScript errors in console
- [ ] Smooth user experience with appropriate feedback
