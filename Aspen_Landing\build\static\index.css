@layer component{:scope,avatar-element{--avatar-align-items-default:center;--avatar-align-self-default:center;--avatar-background-color-default:var(--color-interactive-weak);--avatar-border-block-end-color-default:var(--border-color-default);--avatar-border-block-end-style-default:var(--border-style-default);--avatar-border-block-end-width-default:var(--border-width-small);--avatar-border-block-start-color-default:var(--border-color-default);--avatar-border-block-start-style-default:var(--border-style-default);--avatar-border-block-start-width-default:var(--border-width-small);--avatar-border-end-end-radius-default:50%;--avatar-border-end-start-radius-default:50%;--avatar-border-inline-start-color-default:var(--border-color-default);--avatar-border-inline-start-style-default:var(--border-style-default);--avatar-border-inline-start-width-default:var(--border-width-small);--avatar-border-inline-end-color-default:var(--border-color-default);--avatar-border-inline-end-style-default:var(--border-style-default);--avatar-border-inline-end-width-default:var(--border-width-small);--avatar-border-start-end-radius-default:50%;--avatar-border-start-start-radius-default:50%;--avatar-clip-path-default:circle(50%);--avatar-color-default:var(--color-text-button-tertiary-enabled);--avatar-display-default:grid;--avatar-font-family-default:var(--font-family-default);--avatar-font-size-default-enabled:var(--size-10);--avatar-font-size-medium-default-enabled:var(--size-12);--avatar-font-size-large-default-enabled:var(--size-32);--avatar-grid-auto-flow-default:row;--avatar-height-default:var(--size-32);--avatar-height-medium-default:var(--size-40);--avatar-height-large-default:var(--size-100);--avatar-justify-content-default:center;--avatar-overflow-default:hidden;--avatar-position-default:relative;--avatar-row-gap-default:var(--gap-medium);--avatar-width-default:var(--size-32);--avatar-width-medium-default:var(--size-40);--avatar-width-large-default:var(--size-100)}:scope,badge-element{--badge-background-color-info-default:var(--background-color-info);--badge-background-color-success-default:var(--background-color-success);--badge-background-color-warning-default:var(--background-color-warning);--badge-background-color-error-default:var(--background-color-error);--badge-color-info-default:var(--color-text-info);--badge-color-success-default:var(--color-text-success);--badge-color-warning-default:var(--color-text-warning);--badge-color-error-default:var(--color-text-error);--badge-padding-block-start-default:var(--padding-x-small);--badge-padding-block-end-default:var(--padding-x-small);--badge-padding-inline-end-default:var(--padding-small);--badge-padding-inline-start-default:var(--padding-small);--badge-border-start-start-radius-default:var(--border-radius-medium);--badge-border-start-end-radius-default:var(--border-radius-medium);--badge-border-end-start-radius-default:var(--border-radius-medium);--badge-border-end-end-radius-default:var(--border-radius-medium);--badge-font-weight-default:var(--font-weight-strong)}:scope,bread-crumb{--bread-crumb-align-self-default:center;--bread-crumb-column-gap-default:var(--gap-x-small);--bread-crumb-display-default:block;--bread-crumb-display-grid:grid;--bread-crumb-display-not-allowed:none;--bread-crumb-grid-auto-flow-default:column;--bread-crumb-grid-template-columns-default:max-content;--bread-crumb-justify-content-default:start;--bread-crumb-padding-inline-end:initial}:scope,bread-crumb-item{--bread-crumb-item-align-items-default:center;--bread-crumb-item-border-start-start-radius-default:var(--border-radius-medium);--bread-crumb-item-border-start-end-radius-default:var(--border-radius-medium);--bread-crumb-item-border-end-start-radius-default:var(--border-radius-medium);--bread-crumb-item-border-end-end-radius-default:var(--border-radius-medium);--bread-crumb-item-color-default:var(--color-text-secondary);--bread-crumb-item-color-active:var(--color-text-default);--bread-crumb-item-display-last-item:none;--bread-crumb-item-display-not-allowed:none;--bread-crumb-item-font-style-default-enabled:var(--font-style-default);--bread-crumb-item-font-size-default-enabled:var(--font-size-default);--bread-crumb-item-font-family-default-enabled:var(--font-family-default);--bread-crumb-item-font-weight-default-enabled:var(--font-weight-default);--bread-crumb-item-line-height-default-enabled:var(--line-height-default);--bread-crumb-item-outline-color-default:var(--border-color-selected);--bread-crumb-item-outline-offset-default:var(--outline-offset-default);--bread-crumb-item-outline-style-default:var(--border-style-default);--bread-crumb-item-outline-width-default-enabled:var(--outline-width-none);--bread-crumb-item-outline-width-default-focus-visible:var(--outline-width-default);--bread-crumb-item-text-decoration-enabled-default:none}:scope,button{--button-background-color-default-enabled:var(--background-color-button-secondary-enabled);--button-background-color-default-active:var(--background-color-button-secondary-active);--button-background-color-default-disabled:var(--background-color-button-secondary-disabled);--button-background-color-default-focus:var(--background-color-button-secondary-enabled);--button-background-color-default-hover:var(--background-color-button-secondary-hover);--button-background-color-submit-enabled:var(--background-color-button-primary-enabled);--button-background-color-submit-active:var(--background-color-button-primary-active);--button-background-color-submit-disabled:var(--background-color-button-primary-disabled);--button-background-color-submit-focus:var(--background-color-button-primary-enabled);--button-background-color-submit-hover:var(--background-color-button-primary-hover);--button-background-color-reset-enabled:var(--background-color-button-tertiary-enabled);--button-background-color-reset-active:var(--background-color-button-tertiary-active);--button-background-color-reset-disabled:var(--background-color-button-tertiary-disabled);--button-background-color-reset-focus:var(--background-color-button-tertiary-enabled);--button-background-color-reset-hover:var(--background-color-button-tertiary-hover);--button-background-image-default:initial;--button-background-position-x-default:initial;--button-background-position-y-default:initial;--button-border-inline-start-color-default-enabled:var(--border-color-button-secondary-enabled);--button-border-inline-start-color-default-hover:var(--border-color-button-secondary-hover);--button-border-inline-start-color-default-active:var(--border-color-button-secondary-active);--button-border-inline-start-color-default-disabled:var(--border-color-button-secondary-disabled);--button-border-inline-start-color-submit-enabled:var(--border-color-button-primary-enabled);--button-border-inline-start-color-submit-hover:var(--border-color-button-primary-active);--button-border-inline-start-color-submit-active:var(--border-color-button-tertiary-active);--button-border-inline-start-color-submit-disabled:var(--border-color-button-tertiary-disabled);--button-border-inline-start-color-reset-enabled:var(--border-color-button-tertiary-enabled);--button-border-inline-start-color-reset-hover:var(--border-color-button-tertiary-active);--button-border-inline-start-color-reset-active:var(--border-color-button-tertiary-active);--button-border-inline-start-color-reset-disabled:var(--border-color-button-tertiary-disabled);--button-border-block-start-color-submit-enabled:var(--border-color-button-primary-enabled);--button-border-block-start-color-submit-hover:var(--border-color-button-primary-hover);--button-border-block-start-color-submit-active:var(--border-color-button-tertiary-active);--button-border-block-start-color-submit-disabled:var(--border-color-button-tertiary-disabled);--button-border-block-start-color-default-enabled:var(--border-color-button-secondary-enabled);--button-border-block-start-color-default-hover:var(--border-color-button-secondary-hover);--button-border-block-start-color-default-active:var(--border-color-button-secondary-active);--button-border-block-start-color-default-disabled:var(--border-color-button-secondary-disabled);--button-border-block-start-color-reset-enabled:var(--border-color-button-tertiary-enabled);--button-border-block-start-color-reset-hover:var(--border-color-button-tertiary-hover);--button-border-block-start-color-reset-active:var(--border-color-button-tertiary-active);--button-border-block-start-color-reset-disabled:var(--border-color-button-tertiary-disabled);--button-border-inline-start-style-default-enabled:var(--border-style-default);--button-border-inline-start-width-default-enabled:var(--border-width-small);--button-border-inline-end-color-default-enabled:var(--border-color-button-secondary-enabled);--button-border-inline-end-color-default-hover:var(--border-color-button-secondary-hover);--button-border-inline-end-color-default-active:var(--border-color-button-secondary-active);--button-border-inline-end-color-default-disabled:var(--border-color-button-secondary-disabled);--button-border-inline-end-color-submit-enabled:var(--border-color-button-primary-enabled);--button-border-inline-end-color-submit-hover:var(--border-color-button-primary-hover);--button-border-inline-end-color-submit-active:var(--border-color-button-tertiary-active);--button-border-inline-end-color-submit-disabled:var(--border-color-button-tertiary-disabled);--button-border-inline-end-color-reset-enabled:var(--border-color-button-tertiary-enabled);--button-border-inline-end-color-reset-hover:var(--border-color-button-tertiary-hover);--button-border-inline-end-color-reset-active:var(--border-color-button-tertiary-active);--button-border-inline-end-color-reset-disabled:var(--border-color-button-tertiary-disabled);--button-border-inline-end-color-reset-focus-visible:var(--border-color-button-secondary-enabled);--button-border-inline-end-style-default-enabled:var(--border-style-default);--button-border-inline-end-width-default-enabled:var(--border-width-small);--button-border-block-start-style-default-enabled:var(--border-style-default);--button-border-block-start-width-default-enabled:var(--border-width-small);--button-border-block-end-color-default-enabled:var(--border-color-button-secondary-enabled);--button-border-block-end-color-default-hover:var(--border-color-button-secondary-hover);--button-border-block-end-color-default-active:var(--border-color-button-secondary-active);--button-border-block-end-color-default-disabled:var(--border-color-button-secondary-disabled);--button-border-block-end-color-submit-enabled:var(--border-color-button-primary-enabled);--button-border-block-end-color-submit-hover:var(--border-color-button-primary-hover);--button-border-block-end-color-submit-active:var(--border-color-button-tertiary-active);--button-border-block-end-color-submit-disabled:var(--border-color-button-tertiary-disabled);--button-border-block-end-color-reset-enabled:var(--border-color-button-tertiary-enabled);--button-border-block-end-color-reset-hover:var(--background-color-button-tertiary-active);--button-border-block-end-color-reset-active:var(--border-color-button-tertiary-active);--button-border-block-end-color-reset-disabled:var(--border-color-button-tertiary-disabled);--button-border-block-end-style-default-enabled:var(--border-style-default);--button-border-block-end-width-default-enabled:var(--border-width-small);--button-border-start-start-radius-default-enabled:var(--border-radius-medium);--button-border-start-end-radius-default-enabled:var(--border-radius-medium);--button-border-end-start-radius-default-enabled:var(--border-radius-medium);--button-border-end-end-radius-default-enabled:var(--border-radius-medium);--button-box-shadow:initial;--button-color-default-enabled:var(--color-text-button-secondary-enabled);--button-color-default-active:var(--color-text-button-tertiary-enabled);--button-color-default-disabled:var(--color-text-button-secondary-disabled);--button-color-default-hover:var(--color-text-button-tertiary-enabled);--button-color-submit-enabled:var(--color-text-button-primary-enabled);--button-color-submit-active:var(--color-text-button-primary-enabled);--button-color-submit-disabled:var(--color-text-button-primary-disabled);--button-color-reset-enabled:var(--color-text-button-tertiary-enabled);--button-color-reset-active:var(--color-text-button-tertiary-enabled);--button-color-reset-disabled:var(--color-text-button-tertiary-disabled);--button-cursor-default:var(--cursor-auto);--button-cursor-hover:var(--cursor-auto);--button-cursor-disabled:var(--cursor-auto);--button-display-default:inline grid;--button-font-style-default-enabled:var(--font-style-default);--button-font-size-default-enabled:var(--font-size-medium);--button-font-family-default-enabled:var(--font-family-default);--button-font-weight-default-enabled:var(--font-weight-strong);--button-line-height-default-enabled:var(--size-16);--button-outline-color-default:var(--outline-color-default);--button-outline-offset-default:var(--outline-offset-default);--button-outline-style-default:var(--border-style-default);--button-outline-width-default-enabled:var(--outline-width-none);--button-outline-width-default-focus-visible:var(--outline-width-default);--button-padding-inline-start:var(--padding-medium);--button-padding-inline-end:var(--padding-medium);--button-padding-block-start:var(--padding-small);--button-padding-block-end:var(--padding-small);--button-text-align-default-enabled:var(--text-align-center);--button-text-transform-default-enabled:var(--text-transform-default);--button-user-select-default:none}:scope,card-body{--card-body-color-default:var(--color-text-default);--card-body-font-family-default:var(--font-family-default)}:scope,card-element{--card-background-color-default:var(--background-color-shell-default);--card-display-default:grid;--card-grid-auto-flow-default:row;--card-padding-block-end-default:var(--padding-xx-large);--card-padding-block-start-default:var(--padding-large);--card-padding-inline-end-default:var(--padding-x-large);--card-padding-inline-start-default:var(--padding-x-large);--card-row-gap-default:var(--gap-medium);--card-border-inline-start-color-default:var(--border-color-container);--card-border-block-start-color-default:var(--border-color-container);--card-border-inline-start-style-default:var(--border-style-default);--card-border-inline-start-width-default:var(--border-width-small);--card-border-inline-end-color-default:var(--border-color-container);--card-border-inline-end-style-default:var(--border-style-default);--card-border-inline-end-width-default:var(--border-width-small);--card-border-block-start-style-default:var(--border-style-default);--card-border-block-start-width-default:var(--border-width-small);--card-border-block-end-color-default:var(--border-color-container);--card-border-block-end-style-default:var(--border-style-default);--card-border-block-end-width-default:var(--border-width-small);--card-border-start-start-radius-default:var(--border-radius-large);--card-border-start-end-radius-default:var(--border-radius-large);--card-border-end-start-radius-default:var(--border-radius-large);--card-border-end-end-radius-default:var(--border-radius-large)}:scope,card-header{--card-header-color-default:var(--color-text-default);--card-header-font-family-default:var(--font-family-default);--card-header-font-weight-default:var(--font-weight-strong);--card-header-font-size-default:var(--font-size-x-large);--card-header-font-style-default-enabled:var(--font-style-default);--card-header-line-height-default-enabled:var(--line-height-default);--card-header-margin-block-end-default:0rem;--card-header-margin-block-start-default:0rem;--card-header-margin-inline-end-default:0rem;--card-header-margin-inline-start-default:0rem}:scope,chip-element{--chip-element-align-items-default:center;--chip-element-background-color-default:var(--background-color-selected);--chip-element-background-color-focus-visible:var(--background-color-selected);--chip-element-background-color-disabled:var(--background-color-button-tertiary-disabled);--chip-element-border-block-end-color-default-enabled:var(--border-color-button-tertiary-enabled);--chip-element-border-block-end-color-default-focus-visible:var(--border-color-button-tertiary-enabled);--chip-element-border-block-end-color-default-disabled:var(--border-color-button-tertiary-disabled);--chip-element-border-block-end-style-default-enabled:var(--border-style-default);--chip-element-border-block-end-width-default:var(--size-0);--chip-element-border-block-start-color-default-enabled:var(--border-color-button-tertiary-enabled);--chip-element-border-block-start-color-default-focus-visible:var(--border-color-button-tertiary-enabled);--chip-element-border-block-start-color-default-disabled:var(--border-color-button-tertiary-disabled);--chip-element-border-block-start-style-default-enabled:var(--border-style-default);--chip-element-border-block-start-width-default:var(--size-0);--chip-element-border-end-end-radius-default-enabled:var(--border-radius-medium);--chip-element-border-end-start-radius-default-enabled:var(--border-radius-medium);--chip-element-border-inline-end-color-default-enabled:var(--border-color-button-tertiary-enabled);--chip-element-border-inline-end-color-default-focus-visible:var(--border-color-button-tertiary-enabled);--chip-element-border-inline-end-color-default-disabled:var(--border-color-button-tertiary-disabled);--chip-element-border-inline-end-style-default-enabled:var(--border-style-default);--chip-element-border-inline-end-width-default:var(--size-0);--chip-element-border-inline-start-color-default-enabled:var(--border-color-button-tertiary-enabled);--chip-element-border-inline-start-color-default-focus-visible:var(--border-color-button-tertiary-enabled);--chip-element-border-inline-start-color-default-disabled:var(--border-color-button-tertiary-disabled);--chip-element-border-inline-start-style-default-enabled:var(--border-style-default);--chip-element-border-inline-start-width-default:var(--size-0);--chip-element-border-start-end-radius-default-enabled:var(--border-radius-medium);--chip-element-border-start-start-radius-default-enabled:var(--border-radius-medium);--chip-element-color-default:var(--color-text-selected);--chip-element-color-disabled:var(--color-text-disabled);--chip-element-color-href-default:var(--color-text-selected);--chip-element-color-href-hover:var(--color-text-selected);--chip-element-column-gap-default:var(--gap-xx-small);--chip-element-display-inline:inline grid;--chip-element-display-inline-grid:inline grid;--chip-element-display-not-allowed:none;--chip-element-font-style-default-enabled:var(--font-style-default);--chip-element-font-size-default-enabled:var(--font-size-medium);--chip-element-font-family-default-enabled:var(--font-family-default);--chip-element-font-weight-default-enabled:var(--font-weight-strong);--chip-element-grid-auto-flow-default:column;--chip-element-line-height-default-enabled:var(--line-height-default);--chip-element-outline-color-default:var(--border-color-selected);--chip-element-outline-offset-default:var(--outline-offset-none);--chip-element-outline-style-default:var(--border-style-default);--chip-element-outline-width-default-enabled:var(--outline-width-none);--chip-element-outline-width-default-focus-visible:var(--outline-width-default);--chip-element-padding-inline-start:var(--padding-x-small);--chip-element-padding-inline-end:var(--padding-x-small);--chip-element-padding-block-start:var(--padding-x-small);--chip-element-padding-block-end:var(--padding-x-small);--chip-element-text-decoration-enabled-default:none}:scope,content-switcher{--content-switcher-align-items-default:center;--content-switcher-background-color-default-enabled:var(--background-color-navigation-enabled);--content-switcher-background-image-default-enabled:initial;--content-switcher-background-position-x-default-enabled:initial;--content-switcher-background-position-y-default-enabled:initial;--content-switcher-border-inline-start-color-default-enabled:var(--border-color-button-tertiary-enabled);--content-switcher-border-inline-start-style-default-enabled:var(--border-style-default);--content-switcher-border-inline-start-width-default-enabled:var(--border-width-small);--content-switcher-border-inline-end-color-default-enabled:var(--border-color-button-tertiary-enabled);--content-switcher-border-inline-end-style-default-enabled:var(--border-style-default);--content-switcher-border-inline-end-width-default-enabled:var(--border-width-small);--content-switcher-border-block-start-color-default-enabled:var(--border-color-button-tertiary-enabled);--content-switcher-border-block-start-style-default-enabled:var(--border-style-default);--content-switcher-border-block-start-width-default-enabled:var(--border-width-small);--content-switcher-border-block-end-color-default-enabled:var(--border-color-button-tertiary-enabled);--content-switcher-border-block-end-style-default-enabled:var(--border-style-default);--content-switcher-border-block-end-width-default-enabled:var(--border-width-small);--content-switcher-border-start-start-radius-default-enabled:.5em;--content-switcher-border-start-start-radius-slot-enabled:.4em;--content-switcher-border-start-end-radius-default-enabled:.5em;--content-switcher-border-start-end-radius-slot-enabled:.4em;--content-switcher-border-end-start-radius-default-enabled:.5em;--content-switcher-border-end-start-radius-slot-enabled:.4em;--content-switcher-border-end-end-radius-default-enabled:.5em;--content-switcher-border-end-end-radius-slot-enabled:.4em;--content-switcher-box-shadow:initial;--content-switcher-column-gap-default:var(--gap-xx-small);--content-switcher-display-default:grid;--content-switcher-grid-auto-flow-default:column;--content-switcher-grid-auto-flow-vertical:row;--content-switcher-justify-content-default:start;--content-switcher-padding-inline-start:initial;--content-switcher-padding-inline-end:initial;--content-switcher-padding-block-start:initial;--content-switcher-padding-block-end:initial;--content-switcher-width-default:min-content}:scope,content-switcher-item{--content-switcher-item-align-items-default:center;--content-switcher-item-background-color-default-enabled:var(--background-color-navigation-enabled);--content-switcher-item-background-color-default-focus-visible:var(--background-color-navigation-enabled);--content-switcher-item-background-color-default-disabled:var(--background-color-navigation-disabled);--content-switcher-item-background-color-default-hover:var(--background-color-selected);--content-switcher-item-background-color-selected-enabled:var(--background-color-selected);--content-switcher-item-background-color-selected-focus-visible:var(--background-color-selected);--content-switcher-item-background-color-selected-disabled:var(--background-color-button-primary-disabled);--content-switcher-item-background-color-selected-hover:var(--background-color-selected);--content-switcher-item-background-image-default-enabled:initial;--content-switcher-item-background-position-x-default-enabled:initial;--content-switcher-item-background-position-y-default-enabled:initial;--content-switcher-item-border-inline-start-color-default-enabled:var(--border-color-button-tertiary-enabled);--content-switcher-item-border-inline-start-color-default-disabled:var(--border-color-button-tertiary-disabled);--content-switcher-item-border-inline-start-style-default-enabled:var(--border-style-default);--content-switcher-item-border-inline-start-width-default-enabled:var(--border-width-small);--content-switcher-item-border-inline-end-color-default-enabled:var(--border-color-button-tertiary-enabled);--content-switcher-item-border-inline-end-color-default-disabled:var(--border-color-button-tertiary-disabled);--content-switcher-item-border-inline-end-style-default-enabled:var(--border-style-default);--content-switcher-item-border-inline-end-width-default-enabled:var(--border-width-small);--content-switcher-item-border-block-start-color-default-enabled:var(--border-color-button-tertiary-enabled);--content-switcher-item-border-block-start-color-default-disabled:var(--border-color-button-tertiary-disabled);--content-switcher-item-border-block-start-style-default-enabled:var(--border-style-default);--content-switcher-item-border-block-start-width-default-enabled:var(--border-width-small);--content-switcher-item-border-block-end-color-default-enabled:var(--border-color-button-tertiary-enabled);--content-switcher-item-border-block-end-color-default-disabled:var(--border-color-button-tertiary-disabled);--content-switcher-item-border-block-end-style-default-enabled:var(--border-style-default);--content-switcher-item-border-block-end-width-default-enabled:var(--border-width-small);--content-switcher-item-border-start-start-radius-default-enabled:var(--border-radius-medium);--content-switcher-item-border-start-end-radius-default-enabled:var(--border-radius-medium);--content-switcher-item-border-end-start-radius-default-enabled:var(--border-radius-medium);--content-switcher-item-border-end-end-radius-default-enabled:var(--border-radius-medium);--content-switcher-item-box-shadow:initial;--content-switcher-item-color-default-enabled:var(--color-text-default);--content-switcher-item-color-default-disabled:var(--color-text-disabled);--content-switcher-item-color-selected-enabled:var(--color-text-selected);--content-switcher-item-color-selected-disabled:var(--color-text-disabled);--content-switcher-item-cursor-default:var(--cursor-pointer);--content-switcher-item-cursor-hover:var(--cursor-pointer);--content-switcher-item-cursor-disabled:var(--cursor-not-allowed);--content-switcher-item-display-default:inline grid;--content-switcher-item-font-style-default-enabled:var(--font-style-default);--content-switcher-item-font-size-default-enabled:var(--font-size-default);--content-switcher-item-font-family-default-enabled:var(--font-family-default);--content-switcher-item-font-weight-default-enabled:var(--font-weight-strong);--content-switcher-item-grid-auto-flow-default:column;--content-switcher-item-line-height-default-enabled:var(--line-height-default);--content-switcher-item-outline-color-default:var(--border-color-selected);--content-switcher-item-outline-offset-default:var(--outline-offset-default);--content-switcher-item-outline-style-default:var(--border-style-default);--content-switcher-item-outline-width-default-enabled:var(--outline-width-none);--content-switcher-item-outline-width-default-focus-visible:var(--outline-width-default);--content-switcher-item-padding-inline-start:var(--padding-medium);--content-switcher-item-padding-inline-end:var(--padding-medium);--content-switcher-item-padding-block-start:var(--padding-small);--content-switcher-item-pointer-events-default:none;--content-switcher-item-padding-block-end:var(--padding-small);--content-switcher-item-text-align-default-enabled:var(--text-align-center);--content-switcher-item-text-transform-default-enabled:var(--text-transform-default);--content-switcher-item-user-select-default:none}:scope,form-footer{--form-footer-reset-grid-column-start-default:var(--form-footer-reset-grid-column-start-horizontal);--form-footer-reset-grid-column-start-vertical:initial;--form-footer-reset-grid-column-start-horizontal:1;--form-footer-reset-grid-column-end-default:var(--form-footer-reset-grid-column-end-horizontal);--form-footer-reset-grid-column-end-vertical:initial;--form-footer-reset-grid-column-end-horizontal:2;--form-footer-submit-grid-column-start-default:var(--form-footer-submit-grid-column-start-horizontal);--form-footer-submit-grid-column-start-vertical:initial;--form-footer-submit-grid-column-start-horizontal:3;--form-footer-submit-grid-column-end-default:var(--form-footer-submit-grid-column-end-horizontal);--form-footer-submit-grid-column-end-vertical:initial;--form-footer-submit-grid-column-end-horizontal:4;--form-footer-column-gap:var(--gap-medium);--form-footer-display:grid;--form-footer-grid-auto-flow-default:var(--form-footer-grid-auto-flow-horizontal);--form-footer-grid-auto-flow-horizontal:column;--form-footer-grid-auto-flow-vertical:row;--form-footer-grid-column-start-default:initial;--form-footer-grid-column-end-default:initial;--form-footer-grid-template-columns-default:var(--form-footer-grid-template-columns-horizontal);--form-footer-grid-template-columns-horizontal:max-content 1fr max-content;--form-footer-grid-template-columns-vertical:initial;--form-footer-justify-content-default:var(--form-footer-justify-content-horizontal);--form-footer-justify-content-horizontal:initial;--form-footer-justify-content-vertical:center;--form-footer-padding-block-start:var(--padding-none);--form-footer-padding-block-end:var(--padding-none);--form-footer-padding-inline-start:var(--padding-none);--form-footer-padding-inline-end:var(--padding-none);--form-footer-row-gap:var(--gap-medium);--form-footer-width:initial}:scope,form-message{--form-message-align-items-default:center;--form-message-color-default:var(--color-text-secondary);--form-message-color-error:var(--color-text-error);--form-message-column-gap-default:var(--gap-x-small);--form-message-font-family-default:var(--font-family-default);--form-message-font-size-default:var(--font-size-medium);--form-message-icon-error:url("data:image/sgv+html;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'><path d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/></svg>")}:scope,input{--input-accent-color-default:var(--color-icon-selected);--input-background-color-default-enabled:var(--background-color-form-input-enabled);--input-background-color-default-disabled:var(--background-color-form-input-disabled);--input-background-color-default-read-only:var(--background-color-form-input-read-only);--input-background-image-default-enabled:var(--background-image-default);--input-background-position-x-default-enabled:var(--background-position-x-default);--input-background-position-y-default-enabled:var(--background-position-y-default);--input-border-inline-start-color-default-enabled:var(--border-color-form-input-enabled);--input-border-inline-start-color-default-hover:var(--border-color-selected);--input-border-inline-start-color-default-valid:var(--border-color-form-input-enabled);--input-border-inline-start-color-default-invalid:var(--border-color-error);--input-border-inline-start-color-default-disabled:var(--border-color-form-input-disabled);--input-border-inline-start-color-default-read-only:var(--border-color-form-input-read-only);--input-border-inline-start-style-default-enabled:var(--border-style-default);--input-border-inline-start-width-default-enabled:var(--border-width-small);--input-border-inline-end-color-default-enabled:var(--border-color-form-input-enabled);--input-border-inline-end-color-default-hover:var(--border-color-selected);--input-border-inline-end-color-default-valid:var(--border-color-form-input-enabled);--input-border-inline-end-color-default-invalid:var(--border-color-error);--input-border-inline-end-color-default-disabled:var(--border-color-form-input-disabled);--input-border-inline-end-color-default-read-only:var(--border-color-form-input-read-only);--input-border-inline-end-style-default-enabled:var(--border-style-default);--input-border-inline-end-width-default-enabled:var(--border-width-small);--input-border-block-start-color-default-enabled:var(--border-color-form-input-enabled);--input-border-block-start-color-default-hover:var(--border-color-selected);--input-border-block-start-color-default-valid:var(--border-color-form-input-enabled);--input-border-block-start-color-default-invalid:var(--border-color-error);--input-border-block-start-color-default-disabled:var(--border-color-form-input-disabled);--input-border-block-start-color-default-read-only:var(--border-color-form-input-read-only);--input-border-block-start-style-default-enabled:var(--border-style-default);--input-border-block-start-width-default-enabled:var(--border-width-small);--input-border-block-end-color-default-enabled:var(--border-color-form-input-enabled);--input-border-block-end-color-default-hover:var(--border-color-selected);--input-border-block-end-color-default-valid:var(--border-color-form-input-enabled);--input-border-block-end-color-default-invalid:var(--border-color-error);--input-border-block-end-color-default-disabled:var(--border-color-form-input-disabled);--input-border-block-end-color-default-read-only:var(--border-color-form-input-read-only);--input-border-block-end-style-default-enabled:var(--border-style-default);--input-border-block-end-width-default-enabled:var(--border-width-small);--input-border-start-start-radius-default-enabled:var(--border-radius-medium);--input-border-start-end-radius-default-enabled:var(--border-radius-medium);--input-border-end-start-radius-default-enabled:var(--border-radius-medium);--input-border-end-end-radius-default-enabled:var(--border-radius-medium);--input-box-shadow-default-enabled:var(--box-shadow-default);--input-color-default-enabled:var(--color-text-default);--input-color-default-active:var(--color-text-default);--input-color-default-valid:var(--color-text-default);--input-color-default-invalid:var(--color-text-default);--input-color-default-disabled:var(--color-text-disabled);--input-color-default-read-only:var(--color-text-default);--input-color-checkbox-enabled:var(--font-color-default);--input-color-checkbox-active:var(--font-color-default);--input-color-checkbox-valid:var(--font-color-default);--input-color-checkbox-invalid:var(--font-color-default);--input-color-checkbox-disabled:var(--color-text-disabled);--input-color-file-invalid:var(--font-color-default);--input-color-file-disabled:var(--color-text-disabled);--input-color-range-enabled:var(--font-color-default);--input-color-range-hover:var(--font-color-default);--input-color-range-active:var(--font-color-default);--input-color-range-disabled:var(--color-text-disabled);--input-color-radio-enabled:var(--font-color-default);--input-color-radio-active:var(--font-color-default);--input-color-radio-valid:var(--font-color-default);--input-color-radio-invalid:var(--font-color-default);--input-color-radio-disabled:var(--color-text-disabled);--input-cursor-default-enabled:var(--cursor-default);--input-display-default-enabled:var(--display-initial);--input-font-style-default-enabled:var(--font-style-default);--input-font-size-default-enabled:var(--font-size-default);--input-font-family-default-enabled:var(--font-family-default);--input-font-weight-default-enabled:var(--font-weight-default);--input-line-height-default-enabled:var(--line-height-default);--input-outline-color-default:var(--outline-color-default);--input-outline-offset-default:var(--outline-offset-default);--input-outline-style-default:var(--border-style-default);--input-outline-width-default-enabled:var(--outline-width-none);--input-outline-width-default-focus-visible:var(--outline-width-default);--input-padding-inline-start:var(--padding-medium);--input-padding-inline-end:var(--padding-medium);--input-padding-block-start:var(--padding-small);--input-padding-block-end:var(--padding-small);--input-text-align-default-enabled:var(--text-align-start);--input-width-default-enabled:initial}:scope,label{--label-color:var(--color-text-default);--label-font-family:var(--font-family-default);--label-font-size-default:var(--font-size-medium);--label-font-weight:var(--font-weight-strong)}:scope,menu-item{--menu-item-menu-indicator-display-default:none;--menu-item-menu-indicator-display-enabled:grid;--menu-item-menu-indicator-grid-column-end-default:icon-end;--menu-item-menu-indicator-grid-column-start-default:icon-start;--menu-item-menu-indicator-grid-row-end-default:main-end;--menu-item-menu-indicator-grid-row-start-default:main-start;--menu-item-popover-target-align-items-default:end;--menu-item-popover-target-background-color-default-enabled:var(--background-color-button-tertiary-enabled);--menu-item-popover-target-background-color-default-active:var(--background-color-button-tertiary-active);--menu-item-popover-target-background-color-default-disabled:var(--background-color-button-tertiary-enabled);--menu-item-popover-target-background-color-default-focus-visible:var(--background-color-button-tertiary-enabled);--menu-item-popover-target-background-color-default-hover:var(--background-color-button-tertiary-hover);--menu-item-popover-target-background-image-default:initial;--menu-item-popover-target-background-position-x-default:initial;--menu-item-popover-target-background-position-y-default:initial;--menu-item-popover-target-border-inline-start-color-default-enabled:var(--border-color-button-tertiary-enabled);--menu-item-popover-target-border-inline-start-style-default-enabled:var(--border-style-default);--menu-item-popover-target-border-inline-start-width-default-enabled:var(--border-width-small);--menu-item-popover-target-border-inline-end-color-default-enabled:var(--border-color-button-tertiary-enabled);--menu-item-popover-target-border-inline-end-style-default-enabled:var(--border-style-default);--menu-item-popover-target-border-inline-end-width-default-enabled:var(--border-width-small);--menu-item-popover-target-border-block-start-color-default-enabled:var(--border-color-button-tertiary-enabled);--menu-item-popover-target-border-block-start-style-default-enabled:var(--border-style-default);--menu-item-popover-target-border-block-start-width-default-enabled:var(--border-width-small);--menu-item-popover-target-border-block-end-color-default-enabled:var(--border-color-button-tertiary-enabled);--menu-item-popover-target-border-block-end-style-default-enabled:var(--border-style-default);--menu-item-popover-target-border-block-end-width-default-enabled:var(--border-width-small);--menu-item-popover-target-border-start-start-radius-default-enabled:var(--border-radius-medium);--menu-item-popover-target-border-start-end-radius-default-enabled:var(--border-radius-medium);--menu-item-popover-target-border-end-start-radius-default-enabled:var(--border-radius-medium);--menu-item-popover-target-border-end-end-radius-default-enabled:var(--border-radius-medium);--menu-item-popover-target-color-default-enabled:var(--color-text-default);--menu-item-popover-target-color-default-active:var(--color-text-default);--menu-item-popover-target-color-default-disabled:var(--color-text-disabled);--menu-item-popover-target-color-default-focus-visible:var(--color-text-default);--menu-item-popover-target-color-default-hover:var(--color-text-default);--menu-item-popover-target-column-gap-default:var(--gap-medium);--menu-item-popover-target-cursor-default:auto;--menu-item-popover-target-cursor-hover:auto;--menu-item-popover-target-cursor-disabled:auto;--menu-item-popover-target-display-default-enabled:grid;--menu-item-popover-target-font-style-default-enabled:var(--font-style-default);--menu-item-popover-target-font-size-default-enabled:var(--font-size-default);--menu-item-popover-target-font-family-default-enabled:var(--font-family-default);--menu-item-popover-target-font-weight-default-enabled:var(--font-weight-default);--menu-item-popover-target-grid-template-columns-default:[main-start] 1fr [main-end];--menu-item-popover-target-grid-template-columns-sub-menu:[main-start] 1fr [icon-start] max-content [icon-end main-end];--menu-item-popover-target-grid-template-rows-default:[main-start] max-content [ main-end];--menu-item-popover-target-line-height-default-enabled:var(--line-height-default);--menu-item-popover-target-outline-color-default:var(--border-color-button-secondary-hover);--menu-item-popover-target-outline-offset-default:var(--outline-offset-default);--menu-item-popover-target-outline-style-default:var(--border-style-default);--menu-item-popover-target-outline-width-default-enabled:var(--outline-width-none);--menu-item-popover-target-outline-width-default-focus-visible:var(--outline-width-default);--menu-item-popover-target-padding-inline-start:var(--padding-medium);--menu-item-popover-target-padding-inline-end:var(--padding-medium);--menu-item-popover-target-padding-block-start:var(--padding-small);--menu-item-popover-target-padding-block-end:var(--padding-small);--menu-item-popover-target-text-align-default:start;--menu-item-popover-target-text-transform-default:var(--text-transform-default);--menu-item-popover-target-user-select-default:none;--menu-item-root-element-border-block-end-color-default:var(--transparent);--menu-item-root-element-border-block-end-color-divider:var(--border-color-default);--menu-item-root-element-border-block-end-style-default:var(--border-style-default);--menu-item-root-element-border-block-end-width-default:var(--border-width-small);--menu-item-root-element-border-block-start-color-default:var(--transparent);--menu-item-root-element-border-block-start-color-divider:var(--border-color-default);--menu-item-root-element-border-block-start-style-default:var(--border-style-default);--menu-item-root-element-border-block-start-width-default:var(--border-width-small);--menu-item-root-element-display-default:grid;--menu-item-slot-display-default:grid;--menu-item-slot-grid-column-end-default:main-end;--menu-item-slot-grid-column-end-sub-menu:icon-start;--menu-item-slot-grid-column-start-default:main-start;--menu-item-slot-grid-row-end-default:main-end;--menu-item-slot-grid-row-start-default:main-start}:scope,notification-action{--notification-action-background-color-default-enabled:var(--background-color-button-tertiary-enabled);--notification-action-background-color-default-active:var(--background-color-button-tertiary-active);--notification-action-background-color-default-disabled:var(--background-color-button-tertiary-disabled);--notification-action-background-color-default-focus:var(--background-color-button-tertiary-enabled);--notification-action-background-color-default-hover:var(--background-color-button-tertiary-hover);--notification-action-border-inline-start-color-default-enabled:var(--border-color-button-tertiary-enabled);--notification-action-border-inline-start-color-default-hover:var(--border-color-button-tertiary-hover);--notification-action-border-inline-start-color-default-active:var(--border-color-button-tertiary-active);--notification-action-border-inline-start-color-default-disabled:var(--border-color-button-tertiary-disabled);--notification-action-border-block-start-color-default-enabled:var(--border-color-button-tertiary-enabled);--notification-action-border-block-start-color-default-hover:var(--border-color-button-tertiary-hover);--notification-action-border-block-start-color-default-active:var(--border-color-button-tertiary-active);--notification-action-border-block-start-color-default-disabled:var(--border-color-button-tertiary-disabled);--notification-action-border-inline-start-style-default-enabled:var(--border-style-default);--notification-action-border-inline-start-width-default-enabled:var(--border-width-small);--notification-action-border-inline-end-color-default-enabled:var(--border-color-button-tertiary-enabled);--notification-action-border-inline-end-color-default-hover:var(--border-color-button-tertiary-hover);--notification-action-border-inline-end-color-default-active:var(--border-color-button-tertiary-active);--notification-action-border-inline-end-color-default-disabled:var(--border-color-button-tertiary-disabled);--notification-action-border-inline-end-style-default-enabled:var(--border-style-default);--notification-action-border-inline-end-width-default-enabled:var(--border-width-small);--notification-action-border-block-start-style-default-enabled:var(--border-style-default);--notification-action-border-block-start-width-default-enabled:var(--border-width-small);--notification-action-border-block-end-color-default-enabled:var(--border-color-button-tertiary-enabled);--notification-action-border-block-end-color-default-hover:var(--border-color-button-tertiary-hover);--notification-action-border-block-end-color-default-active:var(--border-color-button-tertiary-active);--notification-action-border-block-end-color-default-disabled:var(--border-color-button-tertiary-disabled);--notification-action-border-block-end-style-default-enabled:var(--border-style-default);--notification-action-border-block-end-width-default-enabled:var(--border-width-small);--notification-action-border-start-start-radius-default-enabled:var(--border-radius-small);--notification-action-border-start-end-radius-default-enabled:var(--border-radius-small);--notification-action-border-end-start-radius-default-enabled:var(--border-radius-small);--notification-action-border-end-end-radius-default-enabled:var(--border-radius-small);--notification-action-font-size-default:var(--font-size-medium);--notification-action-outline-color-default:var(--outline-color-default);--notification-action-outline-offset-default:var(--outline-offset-default);--notification-action-outline-style-default:var(--border-style-default);--notification-action-outline-width-default-enabled:var(--outline-width-none);--notification-action-outline-width-default-focus-visible:var(--outline-width-default);--notification-action-padding-inline-start:var(--padding-x-small);--notification-action-padding-inline-end:var(--padding-x-small);--notification-action-padding-block-start:var(--padding-xx-small);--notification-action-padding-block-end:var(--padding-xx-small)}:scope,notification-element{--notification-align-items-horizontal:center;--notification-align-items-vertical:start;--notification-background-color-default:var(--background-color-info);--notification-background-color-success:var(--background-color-success);--notification-background-color-warning:var(--background-color-warning);--notification-background-color-error:var(--background-color-error);--notification-background-color-button:#0000;--notification-border-block-end-color-default:var(--border-color-info);--notification-border-block-end-color-success:var(--border-color-success);--notification-border-block-end-color-warning:var(--border-color-warning);--notification-border-block-end-color-error:var(--border-color-error);--notification-border-block-end-style-default:var(--border-style-default);--notification-border-block-end-width-default:var(--border-width-small);--notification-border-block-start-color-default:var(--border-color-info);--notification-border-block-start-color-success:var(--border-color-success);--notification-border-block-start-color-warning:var(--border-color-warning);--notification-border-block-start-color-error:var(--border-color-error);--notification-border-block-start-style-default:var(--border-style-default);--notification-border-block-start-width-default:var(--border-width-small);--notification-border-end-end-radius-default:var(--border-radius-medium);--notification-border-end-start-radius-default:var(--border-radius-medium);--notification-border-inline-start-color-default:var(--border-color-info);--notification-border-inline-start-color-success:var(--border-color-success);--notification-border-inline-start-color-warning:var(--border-color-warning);--notification-border-inline-start-color-error:var(--border-color-error);--notification-border-inline-start-style-default:var(--border-style-default);--notification-border-inline-start-width-default:var(--border-width-small);--notification-border-inline-end-color-default:var(--border-color-info);--notification-border-inline-end-color-success:var(--border-color-success);--notification-border-inline-end-color-warning:var(--border-color-warning);--notification-border-inline-end-color-error:var(--border-color-error);--notification-border-inline-end-style-default:var(--border-style-default);--notification-border-inline-end-width-default:var(--border-width-small);--notification-border-start-end-radius-default:var(--border-radius-medium);--notification-border-start-start-radius-default:var(--border-radius-medium);--notification-close-border-block-end-color-default:#0000;--notification-close-border-block-end-style-default:var(--border-style-default);--notification-close-border-block-end-width-default:var(--border-width-small);--notification-close-border-block-start-color-default:#0000;--notification-close-border-block-start-style-default:var(--border-style-default);--notification-close-border-block-start-width-default:var(--border-width-small);--notification-close-border-end-end-radius-default:var(--border-radius-medium);--notification-close-border-end-start-radius-default:var(--border-radius-medium);--notification-close-border-start-start-radius-default:var(--border-radius-medium);--notification-close-border-start-end-radius-default:var(--border-radius-medium);--notification-close-border-inline-start-color-default:#0000;--notification-close-border-inline-start-style-default:var(--border-style-default);--notification-close-border-inline-start-width-default:var(--border-width-small);--notification-close-border-inline-end-color-default:#0000;--notification-close-border-inline-end-style-default:var(--border-style-default);--notification-close-border-inline-end-width-default:var(--border-width-small);--notification-close-display-default:grid;--notification-close-font-size-default:var(--size-16);--notification-close-outline-color-default:var(--outline-color-default);--notification-close-outline-offset-default:var(--outline-offset-default);--notification-close-outline-style-default:var(--border-style-default);--notification-close-outline-width-default-enabled:var(--outline-width-none);--notification-close-outline-width-default-focus-visible:var(--outline-width-default);--notification-column-gap-default:var(--gap-x-small);--notification-display-default:grid;--notification-display-slot:grid;--notification-grid-column-start-icon:icon-start;--notification-grid-column-start-header:header-start;--notification-grid-column-start-message:message-start;--notification-grid-column-start-close:close-start;--notification-grid-column-start-action:action-start;--notification-grid-column-end-icon:icon-end;--notification-grid-column-end-header:header-end;--notification-grid-column-end-message:message-end;--notification-grid-column-end-close:close-end;--notification-grid-column-end-action:action-end;--notification-grid-row-start-icon:icon-start;--notification-grid-row-start-header:header-start;--notification-grid-row-start-message:message-start;--notification-grid-row-start-close:close-start;--notification-grid-row-start-action:action-start;--notification-grid-row-end-icon:icon-end;--notification-grid-row-end-header:header-end;--notification-grid-row-end-message:message-end;--notification-grid-row-end-close:close-end;--notification-grid-row-end-action:action-end;--notification-grid-template-columns-default:[root-start icon-start] max-content [icon-end header-start] max-content [header-end message-start] 3fr [message-end action-start] max-content [action-end close-start] max-content [close-end root-end];--notification-grid-template-columns-vertical:[root-start icon-start] max-content [icon-end header-start message-start] 1fr [header-end message-end action-start] max-content [action-end close-start] max-content [close-end root-end];--notification-grid-template-rows-default:[root-start icon-start header-start message-start action-start close-start] max-content [root-end icon-end header-end message-end action-end close-end];--notification-grid-template-rows-vertical:[root-start icon-start header-start action-start close-start] max-content [header-end message-start] max-content [icon-end message-end action-end close-end root-end];--notification-color-default:var(--color-text-default);--notification-color-error:var(--color-icon-error);--notification-color-info:var(--color-icon-info);--notification-color-success:var(--color-icon-success);--notification-color-warning:var(--color-icon-warning);--notification-padding-inline-start:var(--padding-medium);--notification-padding-inline-end:var(--padding-medium);--notification-padding-block-start:var(--padding-small);--notification-padding-block-end:var(--padding-small);--notification-row-gap-default:var(--gap-x-small);--notification-icon-color-info:var(--color-icon-info);--notification-icon-color-success:var(--color-icon-success);--notification-icon-color-warning:var(--color-icon-warning);--notification-icon-color-error:var(--color-icon-error)}:scope,notification-header{--notification-header-font-family-default:var(--font-family-default);--notification-header-font-size-default:var(--font-size-medium);--notification-header-font-weight-default:var(--font-weight-strong);--notification-header-line-height-default:var(--line-height-default)}:scope,notification-message{--notification-message-font-family-default:var(--font-family-default);--notification-message-font-size-default:var(--font-size-medium);--notification-message-line-height-default:var(--line-height-default)}:scope,progress{--progress-appearance-default:none;--progress-border-inline-start-color-default:var(--border-color-form-input-read-only);--progress-border-inline-start-style-default:var(--border-style-default);--progress-border-inline-start-width-default:var(--border-width-none);--progress-border-inline-end-color-default:var(--border-color-form-input-read-only);--progress-border-inline-end-style-default:var(--border-style-default);--progress-border-inline-end-width-default:var(--border-width-none);--progress-border-block-start-color-default:var(--border-color-form-input-read-only);--progress-border-block-start-style-default:var(--border-style-default);--progress-border-block-start-width-default:var(--border-width-none);--progress-border-block-end-color-default:var(--border-color-form-input-read-only);--progress-border-block-end-style-default:var(--border-style-default);--progress-border-block-end-width-default:var(--border-width-none);--progress-height-default:var(--size-4);--progress-progress-bar-background-color-default:var(--border-color-form-input-weak);--progress-progress-value-background-color-default:var(--border-color-selected);--progress-row-gap-default:var(--gap-x-small);--progress-width-default:initial}:scope,select{--select-appearance-default:none;--select-background-color-default-enabled:var(--background-color-form-input-enabled);--select-background-color-default-disabled:var(--background-color-form-input-disabled);--select-background-image-default-enabled:url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCI+PHBhdGggZD0iTTE2LjU5IDguNTkgMTIgMTMuMTcgNy40MSA4LjU5IDYgMTBsNiA2IDYtNnoiLz48L3N2Zz4=);--select-background-position-x-default-enabled:var(--padding-medium);--select-background-position-y-default-enabled:center;--select-background-repeat-default:no-repeat;--select-border-inline-start-color-default-enabled:var(--border-color-form-input-enabled);--select-border-inline-start-color-default-hover:var(--border-color-selected);--select-border-inline-start-color-default-valid:var(--border-color-form-input-enabled);--select-border-inline-start-color-default-invalid:var(--border-color-error);--select-border-inline-start-color-default-disabled:var(--border-color-form-input-disabled);--select-border-inline-start-style-default-enabled:var(--border-style-default);--select-border-inline-start-width-default-enabled:var(--border-width-small);--select-border-inline-end-color-default-enabled:var(--border-color-form-input-enabled);--select-border-inline-end-color-default-hover:var(--border-color-selected);--select-border-inline-end-color-default-valid:var(--border-color-form-input-enabled);--select-border-inline-end-color-default-invalid:var(--border-color-error);--select-border-inline-end-color-default-disabled:var(--border-color-form-input-disabled);--select-border-inline-end-style-default-enabled:var(--border-style-default);--select-border-inline-end-width-default-enabled:var(--border-width-small);--select-border-block-start-color-default-enabled:var(--border-color-form-input-enabled);--select-border-block-start-color-default-hover:var(--border-color-selected);--select-border-block-start-color-default-valid:var(--border-color-form-input-enabled);--select-border-block-start-color-default-invalid:var(--border-color-error);--select-border-block-start-color-default-disabled:var(--border-color-form-input-disabled);--select-border-block-start-style-default-enabled:var(--border-style-default);--select-border-block-start-width-default-enabled:var(--border-width-small);--select-border-block-end-color-default-enabled:var(--border-color-form-input-enabled);--select-border-block-end-color-default-hover:var(--border-color-selected);--select-border-block-end-color-default-valid:var(--border-color-form-input-enabled);--select-border-block-end-color-default-invalid:var(--border-color-error);--select-border-block-end-color-default-disabled:var(--border-color-form-input-disabled);--select-border-block-end-style-default-enabled:var(--border-style-default);--select-border-block-end-width-default-enabled:var(--border-width-small);--select-border-start-start-radius-default-enabled:var(--border-radius-medium);--select-border-start-end-radius-default-enabled:var(--border-radius-medium);--select-border-end-start-radius-default-enabled:var(--border-radius-medium);--select-border-end-end-radius-default-enabled:var(--border-radius-medium);--select-box-shadow-default-enabled:var(--box-shadow-default);--select-color-default-enabled:var(--color-text-default);--select-color-default-active:var(--color-text-default);--select-color-default-valid:var(--color-text-default);--select-color-default-invalid:var(--color-text-default);--select-color-default-disabled:var(--color-text-disabled);--select-cursor-default-enabled:var(--cursor-default);--select-display-default-enabled:var(--display-initial);--select-font-style-default-enabled:var(--font-style-default);--select-font-size-default-enabled:var(--font-size-default);--select-font-family-default-enabled:var(--font-family-default);--select-font-weight-default-enabled:var(--font-weight-default);--select-line-height-default-enabled:var(--line-height-default);--select-outline-color-default:var(--border-color-selected);--select-outline-offset-default:var(--outline-offset-default);--select-outline-style-default:var(--border-style-default);--select-outline-width-default-enabled:var(--outline-width-none);--select-outline-width-default-focus-visible:var(--outline-width-default);--select-padding-inline-start:var(--padding-medium);--select-padding-inline-end:var(--padding-medium);--select-padding-block-start:var(--padding-small);--select-padding-block-end:var(--padding-small);--select-text-align-default-enabled:var(--text-align-start);--select-width-default-enabled:initial}:scope,standard-icon{--standard-icon-root-element-align-items-default:center;--standard-icon-root-element-background-color-default:var(--transparent);--standard-icon-root-element-color-default:var(--color-icon-default);--standard-icon-root-element-font-family-default:var(--font-family-icons);--standard-icon-root-element-font-optical-sizing-default:auto;--standard-icon-root-element-font-size-default:var(--font-size-default);--standard-icon-root-element-font-size-small:var(--font-size-small);--standard-icon-root-element-font-size-medium:var(--font-size-medium);--standard-icon-root-element-font-size-large:var(--font-size-large);--standard-icon-root-element-font-style-default:normal;--standard-icon-root-element-font-feature-settings-default:liga;--standard-icon-root-element-font-variation-settings-default:"FILL" 1;--standard-icon-root-element-font-weight-default:normal;--standard-icon-root-element-justify-items-default:center;--standard-icon-root-element-letter-spacing-default:normal;--standard-icon-root-element-line-height-default:1;--standard-icon-root-element-padding-block-end-default:var(--padding-none);--standard-icon-root-element-padding-block-start-default:var(--padding-none);--standard-icon-root-element-padding-inline-start-default:var(--padding-none);--standard-icon-root-element-padding-inline-end-default:var(--padding-none);--standard-icon-root-element-text-rendering-default:optimizeLegibility;--standard-icon-root-element-text-transform-default:none;--standard-icon-root-element-white-space-default:nowrap;--standard-icon-root-element-word-wrap-default:normal;--standard-icon-root-element-display-default:grid;--standard-icon-root-element-height-default:var(--size-16);--standard-icon-root-element-width-default:var(--size-16);--standard-icon-allowable-content-default:normal;--standard-icon-display-default:inline grid;--standard-icon-images-display-default:initial;--standard-icon-images-display-hidden:none;--standard-icon-user-select-default:none}:scope,tab-bar{--tab-bar-align-items-default:center;--tab-bar-background-color-default-enabled:var(--background-color-navigation-enabled);--tab-bar-background-image-default-enabled:initial;--tab-bar-background-position-x-default-enabled:initial;--tab-bar-background-position-y-default-enabled:initial;--tab-bar-border-inline-start-color-default-enabled:var(--border-color-form-input-read-only);--tab-bar-border-inline-start-style-default-enabled:var(--border-style-default);--tab-bar-border-inline-start-width-default-enabled:var(--border-width-small);--tab-bar-border-inline-end-color-default-enabled:var(--border-color-form-input-read-only);--tab-bar-border-inline-end-style-default-enabled:var(--border-style-default);--tab-bar-border-inline-end-width-default-enabled:var(--border-width-small);--tab-bar-border-block-start-color-default-enabled:var(--border-color-form-input-read-only);--tab-bar-border-block-start-style-default-enabled:var(--border-style-default);--tab-bar-border-block-start-width-default-enabled:var(--border-width-small);--tab-bar-border-block-end-color-default-enabled:var(--border-color-form-input-read-only);--tab-bar-border-block-end-style-default-enabled:var(--border-style-default);--tab-bar-border-block-end-width-default-enabled:var(--border-width-medium);--tab-bar-border-start-start-radius-default-enabled:initial;--tab-bar-border-start-end-radius-default-enabled:initial;--tab-bar-border-end-start-radius-default-enabled:initial;--tab-bar-border-end-end-radius-default-enabled:initial;--tab-bar-box-shadow:initial;--tab-bar-column-gap-default:var(--gap-xx-small);--tab-bar-display-default:grid;--tab-bar-grid-auto-flow-default:column;--tab-bar-grid-auto-flow-vertical:row;--tab-bar-justify-content-default:start;--tab-bar-padding-inline-start:initial;--tab-bar-padding-inline-end:initial;--tab-bar-padding-block-start:initial;--tab-bar-padding-block-end:initial;--tab-bar-row-gap-default:var(--gap-xx-small)}:scope,tab-item{--tab-item-align-items-default:center;--tab-item-background-color-default-enabled:var(--background-color-navigation-enabled);--tab-item-background-color-default-hover:var(--background-color-navigation-hover);--tab-item-background-color-default-focus-visible:var(--background-color-navigation-enabled);--tab-item-background-color-default-disabled:var(--background-color-navigation-disabled);--tab-item-background-color-selected-enabled:var(--background-color-navigation-enabled);--tab-item-background-color-selected-hover:var(--background-color-navigation-hover);--tab-item-background-color-selected-focus-visible:var(--background-color-navigation-enabled);--tab-item-background-color-selected-disabled:var(--background-color-navigation-disabled);--tab-item-background-image-default-enabled:initial;--tab-item-background-position-x-default-enabled:initial;--tab-item-background-position-y-default-enabled:initial;--tab-item-border-inline-start-color-default-enabled:var(--border-color-button-tertiary-enabled);--tab-item-border-inline-start-color-default-hover:var(--border-color-button-tertiary-hover);--tab-item-border-inline-start-color-default-disabled:var(--border-color-button-tertiary-disabled);--tab-item-border-inline-start-color-selected-enabled:var(--border-color-button-tertiary-enabled);--tab-item-border-inline-start-color-selected-hover:var(--border-color-button-tertiary-hover);--tab-item-border-inline-start-color-selected-disabled:var(--border-color-button-tertiary-disabled);--tab-item-border-inline-start-color-selected-focus-visible:var(--border-color-button-tertiary-enabled);--tab-item-border-start-start-radius-default-enabled:var(--border-radius-medium);--tab-item-border-start-end-radius-default-enabled:var(--border-radius-medium);--tab-item-border-end-start-radius-default-enabled:var(--border-radius-medium);--tab-item-border-end-end-radius-default-enabled:var(--border-radius-medium);--tab-item-border-inline-start-style-default-enabled:var(--border-style-default);--tab-item-border-inline-start-width-default-enabled:var(--border-width-medium);--tab-item-border-inline-start-width-active-focus-visible:var(--border-width-none);--tab-item-border-inline-end-color-default-enabled:var(--border-color-button-tertiary-enabled);--tab-item-border-inline-end-color-default-hover:var(--border-color-button-tertiary-hover);--tab-item-border-inline-end-color-default-disabled:var(--border-color-button-tertiary-disabled);--tab-item-border-inline-end-color-selected-enabled:var(--border-color-button-tertiary-enabled);--tab-item-border-inline-end-color-selected-hover:var(--border-color-button-tertiary-hover);--tab-item-border-inline-end-color-selected-disabled:var(--border-color-button-tertiary-disabled);--tab-item-border-inline-end-color-selected-focus-visible:var(--border-color-button-tertiary-enabled);--tab-item-border-inline-end-style-default-enabled:var(--border-style-default);--tab-item-border-inline-end-width-default-enabled:var(--border-width-small);--tab-item-border-inline-end-width-active-focus-visible:var(--border-width-none);--tab-item-border-block-start-color-default-enabled:var(--border-color-button-tertiary-enabled);--tab-item-border-block-start-color-default-hover:var(--border-color-button-tertiary-hover);--tab-item-border-block-start-color-default-disabled:var(--border-color-button-tertiary-disabled);--tab-item-border-block-start-color-selected-enabled:var(--border-color-button-tertiary-enabled);--tab-item-border-block-start-color-selected-hover:var(--border-color-button-tertiary-hover);--tab-item-border-block-start-color-selected-disabled:var(--border-color-button-tertiary-disabled);--tab-item-border-block-start-color-selected-focus-visible:var(--border-color-button-tertiary-enabled);--tab-item-border-block-start-style-default-enabled:var(--border-style-default);--tab-item-border-block-start-width-default-enabled:var(--border-width-small);--tab-item-border-block-start-width-active-focus-visible:var(--border-width-none);--tab-item-border-block-end-color-default-enabled:var(--border-color-button-tertiary-enabled);--tab-item-border-block-end-color-default-hover:var(--border-color-button-tertiary-hover);--tab-item-border-block-end-color-default-disabled:var(--border-color-button-tertiary-disabled);--tab-item-border-block-end-color-selected-enabled:var(--border-color-selected);--tab-item-border-block-end-color-selected-hover:var(--border-color-selected);--tab-item-border-block-end-color-selected-disabled:var(--border-color-button-secondary-disabled);--tab-item-border-block-end-color-selected-focus-visible:var(--border-color-button-tertiary-enabled);--tab-item-border-block-end-style-default-enabled:var(--border-style-default);--tab-item-border-block-end-width-default-enabled:var(--border-width-medium);--tab-item-border-block-end-width-active-focus-visible:var(--border-width-none);--tab-item-box-shadow:initial;--tab-item-color-default-enabled:var(--color-text-default);--tab-item-color-default-disabled:var(--color-text-disabled);--tab-item-color-selected-enabled:var(--color-text-selected);--tab-item-color-selected-disabled:var(--color-text-disabled);--tab-item-cursor-default:var(--cursor-pointer);--tab-item-cursor-hover:var(--cursor-pointer);--tab-item-cursor-disabled:var(--cursor-not-allowed);--tab-item-display-default:inline grid;--tab-item-font-style-default-enabled:var(--font-style-default);--tab-item-font-size-default-enabled:var(--font-size-default);--tab-item-font-family-default-enabled:var(--font-family-default);--tab-item-font-weight-default-enabled:var(--font-weight-default);--tab-item-grid-auto-flow-default:column;--tab-item-line-height-default-enabled:var(--line-height-default);--tab-item-outline-color-default:var(--outline-color-default);--tab-item-outline-offset-default:var(--outline-offset-default);--tab-item-outline-style-default:var(--border-style-default);--tab-item-outline-width-default-enabled:var(--outline-width-none);--tab-item-outline-width-default-focus-visible:var(--outline-width-default);--tab-item-padding-inline-start:var(--padding-medium);--tab-item-padding-inline-end:var(--padding-medium);--tab-item-padding-block-start:var(--padding-small);--tab-item-padding-block-end:var(--padding-small);--tab-item-text-align-default-enabled:var(--text-align-center);--tab-item-text-transform-default-enabled:var(--text-transform-default);--tab-item-user-select-default:none}:scope,textarea{--textarea-background-color-default-enabled:var(--background-color-form-input-enabled);--textarea-background-color-default-disabled:var(--background-color-form-input-disabled);--textarea-background-color-default-read-only:var(--background-color-form-input-read-only);--textarea-background-image-default-enabled:var(--background-image-default);--textarea-background-position-x-default-enabled:var(--background-position-x-default);--textarea-background-position-y-default-enabled:var(--background-position-y-default);--textarea-border-inline-start-color-default-enabled:var(--border-color-form-input-enabled);--textarea-border-inline-start-color-default-hover:var(--border-color-selected);--textarea-border-inline-start-color-default-valid:var(--border-color-form-input-enabled);--textarea-border-inline-start-color-default-invalid:var(--border-color-error);--textarea-border-inline-start-color-default-disabled:var(--border-color-form-input-disabled);--textarea-border-inline-start-color-default-read-only:var(--border-color-form-input-read-only);--textarea-border-inline-start-style-default-enabled:var(--border-style-default);--textarea-border-inline-start-width-default-enabled:var(--border-width-small);--textarea-border-inline-end-color-default-enabled:var(--border-color-form-input-enabled);--textarea-border-inline-end-color-default-hover:var(--border-color-selected);--textarea-border-inline-end-color-default-valid:var(--border-color-form-input-enabled);--textarea-border-inline-end-color-default-invalid:var(--border-color-error);--textarea-border-inline-end-color-default-disabled:var(--border-color-form-input-disabled);--textarea-border-inline-end-color-default-read-only:var(--border-color-form-input-read-only);--textarea-border-inline-end-style-default-enabled:var(--border-style-default);--textarea-border-inline-end-width-default-enabled:var(--border-width-small);--textarea-border-block-start-color-default-enabled:var(--border-color-form-input-enabled);--textarea-border-block-start-color-default-hover:var(--border-color-selected);--textarea-border-block-start-color-default-valid:var(--border-color-form-input-enabled);--textarea-border-block-start-color-default-invalid:var(--border-color-error);--textarea-border-block-start-color-default-disabled:var(--border-color-form-input-disabled);--textarea-border-block-start-color-default-read-only:var(--border-color-form-input-read-only);--textarea-border-block-start-style-default-enabled:var(--border-style-default);--textarea-border-block-start-width-default-enabled:var(--border-width-small);--textarea-border-block-end-color-default-enabled:var(--border-color-form-input-enabled);--textarea-border-block-end-color-default-hover:var(--border-color-selected);--textarea-border-block-end-color-default-valid:var(--border-color-form-input-enabled);--textarea-border-block-end-color-default-invalid:var(--border-color-error);--textarea-border-block-end-color-default-disabled:var(--border-color-form-input-disabled);--textarea-border-block-end-color-default-read-only:var(--border-color-form-input-read-only);--textarea-border-block-end-style-default-enabled:var(--border-style-default);--textarea-border-block-end-width-default-enabled:var(--border-width-small);--textarea-border-start-start-radius-default-enabled:var(--border-radius-medium);--textarea-border-start-end-radius-default-enabled:var(--border-radius-medium);--textarea-border-end-start-radius-default-enabled:var(--border-radius-medium);--textarea-border-end-end-radius-default-enabled:var(--border-radius-medium);--textarea-box-shadow-default-enabled:var(--box-shadow-default);--textarea-color-default-enabled:var(--color-text-default);--textarea-color-default-active:var(--color-text-default);--textarea-color-default-valid:var(--color-text-default);--textarea-color-default-invalid:var(--color-text-default);--textarea-color-default-disabled:var(--color-text-disabled);--textarea-color-default-read-only:var(--color-text-default);--textarea-cursor-default-enabled:var(--cursor-default);--textarea-display-default-enabled:var(--display-initial);--textarea-font-style-default-enabled:var(--font-style-default);--textarea-font-size-default-enabled:var(--font-size-default);--textarea-font-family-default-enabled:var(--font-family-default);--textarea-font-weight-default-enabled:var(--font-weight-default);--textarea-line-height-default-enabled:var(--line-height-default);--textarea-outline-color-default:var(--border-color-selected);--textarea-outline-offset-default:var(--outline-offset-default);--textarea-outline-style-default:var(--border-style-default);--textarea-outline-width-default-enabled:var(--outline-width-none);--textarea-outline-width-default-focus-visible:var(--outline-width-default);--textarea-padding-inline-start:var(--padding-medium);--textarea-padding-inline-end:var(--padding-medium);--textarea-padding-block-start:var(--padding-small);--textarea-padding-block-end:var(--padding-small);--textarea-text-align-default-enabled:var(--text-align-start);--textarea-width-default-enabled:initial}:scope,toggle-element{--toggle-root-element-background-color-default-enabled:var(--background-color-button-secondary-enabled);--toggle-root-element-background-color-default-checked:var(--background-color-button-primary-enabled);--toggle-root-element-background-color-default-focus:var(--background-color-button-secondary-enabled);--toggle-root-element-background-color-default-disabled:var(--background-color-form-input-disabled);--toggle-root-element-border-inline-start-color-default-enabled:var(--border-color-form-input-enabled);--toggle-root-element-border-inline-start-color-default-checked:var(--border-color-form-input-read-only);--toggle-root-element-border-inline-start-color-default-disabled:var(--border-color-form-input-read-only);--toggle-root-element-border-block-start-color-default-enabled:var(--border-color-form-input-enabled);--toggle-root-element-border-block-start-color-default-checked:var(--border-color-form-input-read-only);--toggle-root-element-border-block-start-color-default-disabled:var(--border-color-form-input-read-only);--toggle-root-element-border-inline-end-color-default-enabled:var(--border-color-form-input-enabled);--toggle-root-element-border-inline-end-color-default-checked:var(--border-color-form-input-read-only);--toggle-root-element-border-inline-end-color-default-disabled:var(--border-color-form-input-read-only);--toggle-root-element-border-block-end-color-default-enabled:var(--border-color-form-input-enabled);--toggle-root-element-border-block-end-color-default-checked:var(--border-color-form-input-read-only);--toggle-root-element-border-block-end-color-default-disabled:var(--border-color-form-input-read-only);--toggle-root-element-border-inline-start-style-default-enabled:var(--border-style-default);--toggle-root-element-border-inline-start-width-default-enabled:var(--border-width-small);--toggle-root-element-border-inline-end-style-default-enabled:var(--border-style-default);--toggle-root-element-border-inline-end-width-default-enabled:var(--border-width-small);--toggle-root-element-border-block-start-style-default-enabled:var(--border-style-default);--toggle-root-element-border-block-start-width-default-enabled:var(--border-width-small);--toggle-root-element-border-block-end-style-default-enabled:var(--border-style-default);--toggle-root-element-border-block-end-width-default-enabled:var(--border-width-small);--toggle-root-element-border-start-start-radius-default-enabled:var(--size-12);--toggle-root-element-border-start-end-radius-default-enabled:var(--size-12);--toggle-root-element-border-end-start-radius-default-enabled:var(--size-12);--toggle-root-element-border-end-end-radius-default-enabled:var(--size-12);--toggle-root-element-box-sizing-default-enabled:border-box;--toggle-root-element-display-default-enabled:grid;--toggle-root-element-grid-template-columns-default-enabled:[root-start switcher-default-start] 1em [switcher-default-end switcher-selected-start] 1em [switcher-selected-end root-end];--toggle-root-element-grid-template-rows-default-enabled:[root-start switcher-start] 1em [switcher-end root-end];--toggle-root-element-padding-inline-start:var(--padding-x-small);--toggle-root-element-padding-inline-end:var(--padding-x-small);--toggle-root-element-padding-block-start:var(--padding-x-small);--toggle-root-element-padding-block-end:var(--padding-x-small);--toggle-root-element-width:max-content;--toggle-switch-background-color-default-enabled:var(--color-icon-secondary);--toggle-switch-background-color-default-disabled:var(--color-icon-disabled);--toggle-switch-background-color-default-checked:var(--color-icon-inverse);--toggle-switch-border-start-end-radius-default-enabled:var(--size-8);--toggle-switch-border-end-start-radius-default-enabled:var(--size-8);--toggle-switch-border-end-end-radius-default-enabled:var(--size-8);--toggle-switch-border-start-start-radius-default-enabled:var(--size-8);--toggle-switch-box-sizing-default-enabled:border-box;--toggle-switch-display-default-enabled:block;--toggle-switch-grid-column-start-default:switcher-default-start;--toggle-switch-grid-column-start-checked:switcher-selected-start;--toggle-switch-grid-column-end-default:switcher-default-end;--toggle-switch-grid-column-end-checked:switcher-selected-end;--toggle-switch-grid-row-start-default:switcher-start;--toggle-switch-grid-row-end-default:switcher-end;--toggle-switch-height:var(--size-16);--toggle-switch-width:var(--size-16);--toggle-cursor-default:var(--cursor-auto);--toggle-cursor-hover:var(--cursor-auto);--toggle-cursor-disabled:var(--cursor-auto);--toggle-outline-color-default:var(--outline-color-default);--toggle-outline-offset-default:var(--outline-offset-default);--toggle-outline-style-default:var(--border-style-default);--toggle-outline-width-default-enabled:var(--outline-width-none);--toggle-outline-width-default-focus-visible:var(--outline-width-default);--toggle-user-select-default:none}:scope,tooltip-element{--tooltip-background-color-inverse-enabled:var(--background-color-inverse);--tooltip-color-inverse-enabled:var(--color-text-inverse);--tooltip-padding-block-start-default:var(--padding-small);--tooltip-padding-block-end-default:var(--padding-small);--tooltip-padding-inline-end-default:var(--padding-medium);--tooltip-padding-inline-start-default:var(--padding-medium);--tooltip-border-start-start-radius-default:var(--border-radius-medium);--tooltip-border-start-end-radius-default:var(--border-radius-medium);--tooltip-border-end-start-radius-default:var(--border-radius-medium);--tooltip-border-end-end-radius-default:var(--border-radius-medium);--tooltip-box-shadow-default:var(--box-shadow-low);--tooltip-font-weight-default:var(--font-weight-default)}:scope,checkbox-field,progress-bar,radio-field,select-field,text-field,textarea-field,toggle-field{--form-field-group-root-element-border-inline-start-color-default:var(--transparent);--form-field-group-root-element-border-inline-start-style-default:var(--border-style-default);--form-field-group-root-element-border-inline-start-width-default:var(--border-width-small);--form-field-group-root-element-border-inline-end-color-default:var(--transparent);--form-field-group-root-element-border-inline-end-style-default:var(--border-style-default);--form-field-group-root-element-border-inline-end-width-default:var(--border-width-small);--form-field-group-root-element-border-block-start-color-default:var(--transparent);--form-field-group-root-element-border-block-start-style-default:var(--border-style-default);--form-field-group-root-element-border-block-start-width-default:var(--border-width-small);--form-field-group-root-element-border-block-end-color-default:var(--transparent);--form-field-group-root-element-border-block-end-style-default:var(--border-style-default);--form-field-group-root-element-border-block-end-width-default:var(--border-width-small);--form-field-group-root-element-column-gap-default:var(--gap-x-small);--form-field-group-root-element-display-default:grid;--form-field-group-root-element-margin-default:var(--margin-none);--form-field-group-root-element-padding-block-end:var(--padding-none);--form-field-group-root-element-padding-block-start:var(--padding-none);--form-field-group-root-element-padding-inline-end:var(--padding-none);--form-field-group-root-element-padding-inline-start:var(--padding-none);--form-field-group-root-element-row-gap-default:var(--gap-x-small);--form-field-group-field-group-grid-auto-flow-default:row;--form-field-group-field-group-justify-content-default:start;--form-field-group-field-group-row-gap-default:var(--gap-x-small);--form-field-group-label-font-weight:var(--font-weight-default);--form-field-column-gap-default:var(--gap-x-small);--form-field-column-gap-check-and-radio-default:var(--gap-x-small);--form-field-display-default:grid;--form-field-grid-auto-flow-default:row;--form-field-grid-auto-flow-horizontal:column;--form-field-grid-template-columns-default:inherit;--form-field-grid-template-columns-horizontal:1fr 1fr;--form-field-grid-template-columns-check-and-radio:max-content 1fr;--form-field-row-gap-default:var(--gap-x-small);--form-field-slot-grid-template-columns-default:subgrid;--form-field-slot-grid-template-rows-default:subgrid;--form-field-span-color-required-default:var(--color-text-error)}:scope,menu-button,menu-element,menu-item,modal-element,overlay-element{--popover-element-backdrop-background-color-default:#000;--popover-element-background-color-default:var(--background-color-container);--popover-element-border-inline-start-color-default-enabled:var(--transparent);--popover-element-border-inline-start-style-default:var(--border-style-default);--popover-element-border-inline-start-width-default:var(--border-width-small);--popover-element-border-inline-end-color-default:var(--transparent);--popover-element-border-inline-end-style-default:var(--border-style-default);--popover-element-border-inline-end-width-default:var(--border-width-small);--popover-element-border-block-start-color-default:var(--transparent);--popover-element-border-block-start-style-default:var(--border-style-default);--popover-element-border-block-start-width-default:var(--border-width-small);--popover-element-border-block-end-color-default:var(--transparent);--popover-element-border-block-end-style-default:var(--border-style-default);--popover-element-border-block-end-width-default:var(--border-width-small);--popover-element-border-start-start-radius-default:var(--border-radius-large);--popover-element-border-start-end-radius-default:var(--border-radius-large);--popover-element-border-end-start-radius-default:var(--border-radius-large);--popover-element-border-end-end-radius-default:var(--border-radius-large);--popover-element-bottom-default:0em;--popover-element-box-shadow-default:var(--box-shadow-low);--popover-element-button-align-content-default:center;--popover-element-button-align-self-default:center;--popover-element-button-background-color-default-enabled:var(--background-color-button-tertiary-enabled);--popover-element-button-background-color-default-active:var(--background-color-button-tertiary-active);--popover-element-button-background-color-default-disabled:var(--background-color-button-tertiary-enabled);--popover-element-button-background-color-default-focus:var(--background-color-button-tertiary-enabled);--popover-element-button-background-color-default-hover:var(--background-color-button-tertiary-hover);--popover-element-button-background-image-default:initial;--popover-element-button-background-position-x-default:initial;--popover-element-button-background-position-y-default:initial;--popover-element-button-border-inline-start-color-default-enabled:var(--transparent);--popover-element-button-border-inline-start-color-default-hover:var(--border-color-button-tertiary-enabled);--popover-element-button-border-inline-start-style-default-enabled:var(--border-style-default);--popover-element-button-border-inline-start-width-default-enabled:var(--border-width-small);--popover-element-button-border-inline-end-color-default-enabled:var(--transparent);--popover-element-button-border-inline-end-color-default-hover:var(--border-color-button-tertiary-enabled);--popover-element-button-border-inline-end-style-default-enabled:var(--border-style-default);--popover-element-button-border-inline-end-width-default-enabled:var(--border-width-small);--popover-element-button-border-block-start-color-default-enabled:var(--transparent);--popover-element-button-border-block-start-color-default-hover:var(--border-color-button-tertiary-enabled);--popover-element-button-border-block-start-style-default-enabled:var(--border-style-default);--popover-element-button-border-block-start-width-default-enabled:var(--border-width-small);--popover-element-button-border-block-end-color-default-enabled:var(--border-color-button-tertiary-enabled);--popover-element-button-border-block-end-color-default-hover:var(--border-color-button-tertiary-hover);--popover-element-button-border-block-end-style-default-enabled:var(--border-style-default);--popover-element-button-border-block-end-width-default-enabled:var(--border-width-small);--popover-element-button-border-start-start-radius-default-enabled:var(--border-radius-medium);--popover-element-button-border-start-end-radius-default-enabled:var(--border-radius-medium);--popover-element-button-border-end-start-radius-default-enabled:var(--border-radius-medium);--popover-element-button-border-end-end-radius-default-enabled:var(--border-radius-medium);--popover-element-button-box-shadow:initial;--popover-element-button-color-default-enabled:var(--color-text-default);--popover-element-button-color-default-active:var(--color-text-default);--popover-element-button-color-default-disabled:var(--color-text-disabled);--popover-element-button-cursor-default:initial;--popover-element-button-cursor-hover:pointer;--popover-element-button-cursor-disabled:not-allowed;--popover-element-button-display-default:grid;--popover-element-button-font-style-default-enabled:var(--font-style-default);--popover-element-button-font-size-default-enabled:var(--font-size-default);--popover-element-button-font-family-default-enabled:var(--font-family-default);--popover-element-button-font-weight-default-enabled:var(--font-weight-default);--popover-element-button-grid-column-end-default:close-button-end;--popover-element-button-grid-column-start-default:close-button-start;--popover-element-button-grid-row-start-default:header-start;--popover-element-button-grid-row-end-default:header-end;--popover-element-button-height-default:var(--size-24);--popover-element-button-justify-content-default:center;--popover-element-button-line-height-default-enabled:var(--line-height-default);--popover-element-button-outline-color-default:var(--transparent);--popover-element-button-outline-offset-default:var(--outline-offset-default);--popover-element-button-outline-style-default:var(--border-style-default);--popover-element-button-outline-width-default-enabled:var(--outline-width-none);--popover-element-button-outline-width-default-focus-visible:var(--outline-width-default);--popover-element-button-padding-inline-start:var(--padding-none);--popover-element-button-padding-inline-end:var(--padding-none);--popover-element-button-padding-block-start:var(--padding-none);--popover-element-button-padding-block-end:var(--padding-none);--popover-element-button-text-align-default-enabled:center;--popover-element-button-text-transform-default-enabled:var(--text-transform-default);--popover-element-button-user-select-default:none;--popover-element-button-width-default:var(--size-24);--popover-element-button-z-index-default:1;--popover-element-column-gap-default:var(--gap-small);--popover-element-display-default:none;--popover-element-display-popover-open:grid;--popover-element-grid-template-columns-default:[main-start header-start] 1fr [header-end close-button-start] max-content [close-button-end main-end];--popover-element-grid-template-rows-default:[main-start header-start] max-content [header-end content-start] 1fr [content-end footer-start] max-content [footer-end main-end];--popover-element-grid-template-rows-overlay:[main-start header-start] max-content [header-end content-start] 1fr [content-end main-end];--popover-element-left-default:0em;--popover-element-position-default:absolute;--popover-element-padding-block-end-default:var(--padding-medium);--popover-element-padding-block-start-default:var(--padding-medium);--popover-element-padding-inline-end-default:var(--padding-medium);--popover-element-padding-inline-start-default:var(--padding-medium);--popover-element-right-default:0em;--popover-element-row-gap-default:var(--gap-xx-large);--popover-element-slot-display-modal:grid;--popover-element-slot-grid-template-columns-modal:subgrid;--popover-element-slot-grid-template-rows-modal:subgrid;--popover-element-slot-grid-column-end-modal:main-end;--popover-element-slot-grid-column-start-modal:main-start;--popover-element-slot-grid-row-end-modal:main-end;--popover-element-slot-grid-row-start-modal:main-start;--popover-element-top-default:0em;--popover-header-align-content-default:center;--popover-header-color-default:var(--color-text-default);--popover-header-display-default:grid;--popover-header-font-family-default:var(--font-family-default);--popover-header-font-size-default:var(--font-size-x-large);--popover-header-font-weight-default:var(--font-weight-strong);--popover-header-grid-column-end-default:header-end;--popover-header-grid-column-start-default:header-start;--popover-header-grid-row-end-default:header-end;--popover-header-grid-row-start-default:header-start;--popover-header-margin-block-end-default:var(--margin-none);--popover-header-margin-block-start-default:var(--margin-none);--popover-header-margin-inline-end-default:var(--margin-none);--popover-header-margin-inline-start-default:var(--margin-none);--popover-header-text-transform-default:initial;--popover-menu-background-color-default:var(--background-color-container);--popover-menu-border-inline-start-color-default-enabled:var(--transparent);--popover-menu-border-inline-start-style-default:var(--border-style-default);--popover-menu-border-inline-start-width-default:var(--border-width-none);--popover-menu-border-inline-end-color-default:var(--transparent);--popover-menu-border-inline-end-style-default:var(--border-style-default);--popover-menu-border-inline-end-width-default:var(--border-width-none);--popover-menu-border-block-start-color-default:var(--transparent);--popover-menu-border-block-start-style-default:var(--border-style-default);--popover-menu-border-block-start-width-default:var(--border-width-none);--popover-menu-border-block-end-color-default:var(--transparent);--popover-menu-border-block-end-style-default:var(--border-style-default);--popover-menu-border-block-end-width-default:var(--border-width-none);--popover-menu-border-start-start-radius-default:var(--border-radius-medium);--popover-menu-border-start-end-radius-default:var(--border-radius-medium);--popover-menu-border-end-start-radius-default:var(--border-radius-medium);--popover-menu-border-end-end-radius-default:var(--border-radius-medium);--popover-menu-box-shadow-default:var(--box-shadow-low);--popover-menu-display-default:grid}}@layer theme.light{:root{--background-color-default: var(--white);--background-color-canvas: var(--slate-10);--background-color-container: var(--white);--background-color-error: var(--red-10);--background-color-selected: var(--color-interactive-weak);--background-color-warning: var(--orange-10);--background-color-success: var(--green-10);--background-color-info: var(--slate-10);--background-color-inverse: var(--slate-90);--background-color-scrim: var(--opacity-slate);--border-color-default: var(--slate-15);--border-color-weak: var(--slate-10);--border-color-selected: var(--color-interactive-default);--border-color-error: var(--red-70);--border-color-warning: var(--orange-60);--border-color-success: var(--green-60);--border-color-info: var(--slate-60);--border-color-container: var(--slate-15);--border-radius-none: var(--size-0);--border-radius-large: var(--size-8);--border-radius-medium: var(--size-4);--border-radius-small: var(--size-2);--border-width-none: var(--size-0);--border-width-small: var(--size-1);--border-width-medium: var(--size-2);--border-width-large: var(--size-4);--background-color-button-primary-enabled: var(--blue-60);--background-color-button-primary-hover: var(--blue-70);--background-color-button-primary-active: var(--blue-70);--background-color-button-primary-disabled: var(--slate-20);--background-color-button-secondary-enabled: var(--white);--background-color-button-secondary-hover: var(--white);--background-color-button-secondary-active: var(--white);--background-color-button-secondary-disabled: var(--white);--background-color-button-tertiary-enabled: var(--transparent);--background-color-button-tertiary-hover: var(--slate-10);--background-color-button-tertiary-active: var(--slate-10);--background-color-button-tertiary-disabled: var(--transparent);--border-color-button-primary-enabled: var(--transparent);--border-color-button-primary-hover: var(--transparent);--border-color-button-primary-active: var(--transparent);--border-color-button-primary-disabled: var(--transparent);--border-color-button-secondary-enabled: var(--slate-15);--border-color-button-secondary-hover: var(--color-interactive-default);--border-color-button-secondary-active: var(--color-interactive-default);--border-color-button-secondary-disabled: var(--slate-10);--border-color-button-tertiary-enabled: var(--transparent);--border-color-button-tertiary-hover: var(--transparent);--border-color-button-tertiary-active: var(--transparent);--border-color-button-tertiary-disabled: var(--transparent);--color-icon-button-primary-enabled: var( --color-text-button-primary-enabled );--color-icon-button-primary-hover: var(--color-text-button-primary-hover);--color-icon-button-primary-active: var( --color-text-button-primary-active );--color-icon-button-primary-disabled: var(--color-text-disabled);--color-icon-button-secondary-enabled: var(--color-icon-default);--color-icon-button-secondary-hover: var(--color-interactive-strong);--color-icon-button-secondary-active: var(--color-interactive-strong);--color-icon-button-secondary-disabled: var(--color-text-disabled);--color-icon-button-tertiary-enabled: var(--color-interactive-strong);--color-icon-button-tertiary-hover: var(--color-interactive-strong);--color-icon-button-tertiary-active: var(--color-interactive-strong);--color-icon-button-tertiary-disabled: var(--color-text-disabled);--color-text-default: var(--slate-90);--color-text-secondary: var(--slate-50);--color-text-disabled: var(--slate-40);--color-text-inverse: var(--white);--color-text-error: var(--red-70);--color-text-warning: var(--orange-70);--color-text-success: var(--green-70);--color-text-info: var(--slate-70);--color-text-selected: var(--color-interactive-strong);--color-text-link-enabled: var(--color-interactive-strong);--color-text-link-hover: var(--color-interactive-strong);--color-text-link-disabled: var(--color-text-disabled);--color-text-button-primary-enabled: var(--color-text-inverse);--color-text-button-primary-disabled: var(--color-text-disabled);--color-text-button-primary-hover: var(--color-text-inverse);--color-text-button-primary-active: var(--color-text-inverse);--color-text-button-secondary-enabled: var(--color-text-default);--color-text-button-secondary-disabled: var(--color-text-disabled);--color-text-button-secondary-hover: var(--color-interactive-strong);--color-text-button-secondary-active: var(--color-interactive-strong);--color-text-button-tertiary-enabled: var(--color-text-link-enabled);--color-text-button-tertiary-disabled: var(--color-text-disabled);--color-text-button-tertiary-hover: var(--color-text-link-enabled);--color-text-button-tertiary-active: var(--color-text-link-enabled);--font-color-default: var(--gray-05);--font-color-disabled: var(--gray-50);--font-family-heading: var(--font-family-default);--font-size-default: var(--font-size-small);--font-size-xx-small: var(--size-10);--font-size-x-small: var(--size-12);--font-size-small: var(--size-14);--font-size-medium: var(--size-16);--font-size-large: var(--size-18);--font-size-x-large: var(--size-20);--font-size-xx-large: var(--size-24);--font-size-xxx-large: var(--size-32);--line-height-default: var(--line-height-medium);--background-color-form-input-enabled: var(--white);--background-color-form-input-disabled: var(--slate-10);--background-color-form-input-read-only: var(--transparent);--border-color-form-input-enabled: var(--slate-40);--border-color-form-input-active: var(--slate-50);--border-color-form-input-hover: var(--slate-40);--border-color-form-input-disabled: var(--slate-15);--border-color-form-input-weak: var(--slate-15);--border-color-form-input-read-only: var(--transparent);--gap-none: var(--size-0);--gap-xx-small: var(--size-4);--gap-x-small: var(--size-6);--gap-small: var(--size-10);--gap-medium: var(--size-12);--gap-large: var(--size-16);--gap-x-large: var(--size-20);--gap-xx-large: var(--size-24);--color-icon-default: var(--slate-90);--color-icon-error: var(--red-70);--color-icon-info: var(--slate-70);--color-icon-inverse: var(--white);--color-icon-secondary: var(--slate-50);--color-icon-selected: var(--color-interactive-strong);--color-icon-success: var(--green-70);--color-icon-warning: var(--orange-70);--color-icon-disabled: var(--slate-40);--color-interactive-default: var(--blue-60);--color-interactive-default-string: var(--blue-60-string);--color-interactive-weak: var(--blue-10);--color-interactive-weak-string: var(--blue-10);--color-interactive-strong: var(--blue-70);--margin-none: var(--size-0);--aspen-palette-action-active: var(--slate-90-string);--aspen-palette-action-hover: var(--slate-10-string);--aspen-palette-action-hoverOpacity: var(--aspen-palette-opacity-04);--aspen-palette-action-selected: var(--black-string);--aspen-palette-action-selectedOpacity: var(--aspen-palette-opacity-08);--aspen-palette-action-disabled: var(--black-string);--aspen-palette-action-disabledOpacity: var(--aspen-palette-opacity-38);--aspen-palette-action-focus: var(--white-string);--aspen-palette-action-focusOpacity: var(--aspen-palette-opacity-12);--aspen-palette-action-activatedOpacity: var(--aspen-palette-opacity-12);--aspen-palette-action-activeChannel: var(--slate-10-string);--aspen-palette-action-selectedChannel: var(--black-string);--aspen-palette-alert-errorColor: var(--red-70);--aspen-palette-alert-infoColor: var(--slate-70);--aspen-palette-alert-successColor: var(--green-70);--aspen-palette-alert-warningColor: var(--orange-70);--aspen-palette-alert-errorFilledColor: var(--white);--aspen-palette-alert-infoFilledColor: var(--white);--aspen-palette-alert-successFilledColor: var(--black);--aspen-palette-alert-warningFilledColor: var(--black);--aspen-palette-alert-errorStandardBg: var(--red-10);--aspen-palette-alert-infoStandardBg: var(--slate-10);--aspen-palette-alert-successStandardBg: var(--green-10);--aspen-palette-alert-warningStandardBg: var(--orange-10);--aspen-palette-background-paper: var(--white);--aspen-palette-background-default: var(--white);--aspen-palette-background-defaultChannel: var(--white-string);--aspen-palette-background-paperChannel: var(--white-string);--aspen-palette-common-black: var(--black);--aspen-palette-common-white: var(--white);--aspen-palette-common-background: var(--white);--aspen-palette-common-backgroundChannel: var(--white-string);--aspen-palette-common-onBackground: var(--black);--aspen-palette-common-onBackgroundChannel: var(--black-string);--aspen-palette-divider-defaultChannel: var(--slate-15-string);--aspen-palette-divider-defaultOpacity: var(--aspen-palette-opacity-1);--aspen-palette-error-main: var(--red-60);--aspen-palette-error-mainChannel: var(--red-60-string);--aspen-palette-error-light: var(--red-40);--aspen-palette-error-lightChannel: var(--red-40-string);--aspen-palette-error-dark: var(--red-70);--aspen-palette-error-darkChannel: var(--red-70-string);--aspen-palette-error-contrastText: var(--white);--aspen-palette-error-contrastTextChannel: var(--white-string);--aspen-palette-FilledInput-bg: var(--aspen-palette-FilledInput-light-bg);--aspen-palette-FilledInput-hoverBg: var( --aspen-palette-FilledInput-light-hoverBg );--aspen-palette-FilledInput-disabledBg: var( --aspen-palette-FilledInput-light-disabledBg );--aspen-palette-info-main: var(--slate-50);--aspen-palette-info-mainChannel: var(--slate-50-string);--aspen-palette-info-light: var(--slate-40);--aspen-palette-info-lightChannel: var(--slate-40-string);--aspen-palette-info-dark: var(--slate-60);--aspen-palette-info-darkChannel: var(--slate-60-string);--aspen-palette-info-contrastText: var(--white);--aspen-palette-info-contrastTextChannel: var(--white-string);--aspen-palette-primary-main: var(--blue-60);--aspen-palette-primary-mainChannel: var(--blue-60-string);--aspen-palette-primary-light: var(--blue-10);--aspen-palette-primary-lightChannel: var(--blue-10-string);--aspen-palette-primary-dark: var(--blue-70);--aspen-palette-primary-darkChannel: var(--blue-70-string);--aspen-palette-primary-contrastText: var(--white);--aspen-palette-primary-contrastTextChannel: var(--white-string);--aspen-palette-secondary-main: var(--blue-40);--aspen-palette-secondary-mainChannel: var(--blue-40-string);--aspen-palette-secondary-light: var(--blue-20);--aspen-palette-secondary-lightChannel: var(--blue-20-string);--aspen-palette-secondary-dark: var(--blue-50);--aspen-palette-secondary-darkChannel: var(--blue-50-string);--aspen-palette-secondary-contrastText: var(--white);--aspen-palette-secondary-contrastTextChannel: var(--white-string);--aspen-palette-SnackbarContent-bg: var(--slate-90);--aspen-palette-SnackbarContent-color: var(--white);--aspen-palette-SpeedDialAction-fabHoverBg: var(--slate-10);--aspen-palette-StepConnector-border: var(--slate-15);--aspen-palette-StepContent-border: var(--slate-15);--aspen-palette-success-main: var(--green-50);--aspen-palette-success-mainChannel: var(--green-50-string);--aspen-palette-success-light: var(--green-40);--aspen-palette-success-lightChannel: var(--green-40-string);--aspen-palette-success-dark: var(--green-60);--aspen-palette-success-darkChannel: var(--green-60-string);--aspen-palette-success-contrastText: var(--white);--aspen-palette-success-contrastTextChannel: var(--white-string);--aspen-palette-switch-defaultColor: var(--slate-50);--aspen-palette-switch-defaultDisabledColor: var(--slate-40);--aspen-palette-switch-primaryDisabledColor: var(--slate-40);--aspen-palette-switch-secondaryDisabledColor: var(--slate-20);--aspen-palette-TableCellBorder-color: var(--white-string);--aspen-palette-TableCellBorder-opacity: var(--black-string);--aspen-palette-text-primary: var(--slate-90);--aspen-palette-text-primaryOpacity: var(--aspen-palette-opacity-87);--aspen-palette-text-primaryChannel: var(--slate-90-string);--aspen-palette-text-secondary: var(--black);--aspen-palette-text-secondaryOpacity: var(--aspen-palette-opacity-60);--aspen-palette-text-secondaryChannel: var(--black-string);--aspen-palette-text-disabled: var(--black);--aspen-palette-text-disabledOpacity: var(--aspen-palette-opacity-12);--aspen-palette-Tooltip-bg: var(--slate-90);--aspen-palette-warning-main: var(--orange-50);--aspen-palette-warning-mainChannel: var(--orange-50-string);--aspen-palette-warning-light: var(--orange-40);--aspen-palette-warning-lightChannel: var(--orange-40-string);--aspen-palette-warning-dark: var(--orange-60);--aspen-palette-warning-darkChannel: var(--orange-60-string);--aspen-palette-warning-contrastText: var(--white);--aspen-palette-warning-contrastTextChannel: var(--white-string);--aspen-palette-zindexComponent-mobileStepper: var(--zindex-1000);--aspen-palette-zindexComponent-fab: var(--zindex-1050);--aspen-palette-zindexComponent-speedDial: var(--zindex-1050);--aspen-palette-zindexComponent-appBar: var(--zindex-1100);--aspen-palette-zindexComponent-drawer: var(--zindex-1200);--aspen-palette-zindexComponent-modal: var(--zindex-1300);--aspen-palette-zindexComponent-snackbar: var(--zindex-1400);--aspen-palette-zindexComponent-tooltip: var(--zindex-1500);--background-color-navigation-hover: var(--slate-10);--background-color-navigation-enabled: var(--transparent);--background-color-navigation-disabled: var(--transparent);--border-color-navigation-enabled: var(--transparent);--border-color-navigation-hover: var(--transparent);--border-color-navigation-disabled: var(--transparent);--outline-offset-default: var(--size-2);--outline-offset-none: var(--size-0);--outline-width-default: var(--border-width-medium);--outline-width-none: var(--border-width-none);--outline-color-default: var(--border-color-selected);--padding-none: var(--size-0);--padding-large: var(--size-16);--padding-medium: var(--size-12);--padding-small: var(--size-8);--padding-x-large: var(--size-20);--padding-xx-large: var(--size-24);--padding-x-small: var(--size-6);--padding-xx-small: var(--size-4);--padding-xxx-small: var(--size-2);--background-color-shell-default: var(--slate-05);--background-color-shell-weak: var(--slate-02);--background-color-shell-strong: var(--slate-10);--background-color-topbar-primary: var(--white);--background-color-topbar-secondary: var(--gray-05);--text-align-left: var(--text-align-start);--text-align-right: var(--text-align-end);--text-align-default: var(--text-align-start)}@scope (*[data-mui-color-scheme="light"]){:scope{--background-color-default: var(--white);--background-color-canvas: var(--slate-10);--background-color-container: var(--white);--background-color-error: var(--red-10);--background-color-selected: var(--color-interactive-weak);--background-color-warning: var(--orange-10);--background-color-success: var(--green-10);--background-color-info: var(--slate-10);--background-color-inverse: var(--slate-90);--background-color-scrim: var(--opacity-slate);--border-color-default: var(--slate-15);--border-color-weak: var(--slate-10);--border-color-selected: var(--color-interactive-default);--border-color-error: var(--red-70);--border-color-warning: var(--orange-60);--border-color-success: var(--green-60);--border-color-info: var(--slate-60);--border-color-container: var(--slate-15);--border-radius-none: var(--size-0);--border-radius-large: var(--size-8);--border-radius-medium: var(--size-4);--border-radius-small: var(--size-2);--border-width-none: var(--size-0);--border-width-small: var(--size-1);--border-width-medium: var(--size-2);--border-width-large: var(--size-4);--background-color-button-primary-enabled: var(--blue-60);--background-color-button-primary-hover: var(--blue-70);--background-color-button-primary-active: var(--blue-70);--background-color-button-primary-disabled: var(--slate-20);--background-color-button-secondary-enabled: var(--white);--background-color-button-secondary-hover: var(--white);--background-color-button-secondary-active: var(--white);--background-color-button-secondary-disabled: var(--white);--background-color-button-tertiary-enabled: var(--transparent);--background-color-button-tertiary-hover: var(--slate-10);--background-color-button-tertiary-active: var(--slate-10);--background-color-button-tertiary-disabled: var(--transparent);--border-color-button-primary-enabled: var(--transparent);--border-color-button-primary-hover: var(--transparent);--border-color-button-primary-active: var(--transparent);--border-color-button-primary-disabled: var(--transparent);--border-color-button-secondary-enabled: var(--slate-15);--border-color-button-secondary-hover: var(--color-interactive-default);--border-color-button-secondary-active: var(--color-interactive-default);--border-color-button-secondary-disabled: var(--slate-10);--border-color-button-tertiary-enabled: var(--transparent);--border-color-button-tertiary-hover: var(--transparent);--border-color-button-tertiary-active: var(--transparent);--border-color-button-tertiary-disabled: var(--transparent);--color-icon-button-primary-enabled: var( --color-text-button-primary-enabled );--color-icon-button-primary-hover: var(--color-text-button-primary-hover);--color-icon-button-primary-active: var( --color-text-button-primary-active );--color-icon-button-primary-disabled: var(--color-text-disabled);--color-icon-button-secondary-enabled: var(--color-icon-default);--color-icon-button-secondary-hover: var(--color-interactive-strong);--color-icon-button-secondary-active: var(--color-interactive-strong);--color-icon-button-secondary-disabled: var(--color-text-disabled);--color-icon-button-tertiary-enabled: var(--color-interactive-strong);--color-icon-button-tertiary-hover: var(--color-interactive-strong);--color-icon-button-tertiary-active: var(--color-interactive-strong);--color-icon-button-tertiary-disabled: var(--color-text-disabled);--color-text-default: var(--slate-90);--color-text-secondary: var(--slate-50);--color-text-disabled: var(--slate-40);--color-text-inverse: var(--white);--color-text-error: var(--red-70);--color-text-warning: var(--orange-70);--color-text-success: var(--green-70);--color-text-info: var(--slate-70);--color-text-selected: var(--color-interactive-strong);--color-text-link-enabled: var(--color-interactive-strong);--color-text-link-hover: var(--color-interactive-strong);--color-text-link-disabled: var(--color-text-disabled);--color-text-button-primary-enabled: var(--color-text-inverse);--color-text-button-primary-disabled: var(--color-text-disabled);--color-text-button-primary-hover: var(--color-text-inverse);--color-text-button-primary-active: var(--color-text-inverse);--color-text-button-secondary-enabled: var(--color-text-default);--color-text-button-secondary-disabled: var(--color-text-disabled);--color-text-button-secondary-hover: var(--color-interactive-strong);--color-text-button-secondary-active: var(--color-interactive-strong);--color-text-button-tertiary-enabled: var(--color-text-link-enabled);--color-text-button-tertiary-disabled: var(--color-text-disabled);--color-text-button-tertiary-hover: var(--color-text-link-enabled);--color-text-button-tertiary-active: var(--color-text-link-enabled);--font-color-default: var(--gray-05);--font-color-disabled: var(--gray-50);--font-family-heading: var(--font-family-default);--font-size-default: var(--font-size-large);--font-size-small: var(--size-10);--font-size-medium: var(--size-12);--font-size-large: var(--size-14);--font-size-x-large: var(--size-20);--line-height-default: var(--line-height-medium);--background-color-form-input-enabled: var(--white);--background-color-form-input-disabled: var(--slate-10);--background-color-form-input-read-only: var(--transparent);--border-color-form-input-enabled: var(--slate-40);--border-color-form-input-active: var(--slate-50);--border-color-form-input-hover: var(--slate-40);--border-color-form-input-disabled: var(--slate-15);--border-color-form-input-weak: var(--slate-15);--border-color-form-input-read-only: var(--transparent);--gap-none: var(--size-0);--gap-xx-small: var(--size-4);--gap-x-small: var(--size-6);--gap-small: var(--size-10);--gap-medium: var(--size-12);--gap-large: var(--size-16);--gap-x-large: var(--size-20);--gap-xx-large: var(--size-24);--color-icon-default: var(--slate-90);--color-icon-error: var(--red-70);--color-icon-info: var(--slate-70);--color-icon-inverse: var(--white);--color-icon-secondary: var(--slate-50);--color-icon-selected: var(--color-interactive-strong);--color-icon-success: var(--green-70);--color-icon-warning: var(--orange-70);--color-icon-disabled: var(--slate-40);--color-interactive-default: var(--blue-60);--color-interactive-default-string: var(--blue-60-string);--color-interactive-weak: var(--blue-10);--color-interactive-weak-string: var(--blue-10);--color-interactive-strong: var(--blue-70);--margin-none: var(--size-0);--aspen-palette-action-active: var(--slate-90-string);--aspen-palette-action-hover: var(--slate-10-string);--aspen-palette-action-hoverOpacity: var(--aspen-palette-opacity-04);--aspen-palette-action-selected: var(--black-string);--aspen-palette-action-selectedOpacity: var(--aspen-palette-opacity-08);--aspen-palette-action-disabled: var(--black-string);--aspen-palette-action-disabledOpacity: var(--aspen-palette-opacity-38);--aspen-palette-action-focus: var(--white-string);--aspen-palette-action-focusOpacity: var(--aspen-palette-opacity-12);--aspen-palette-action-activatedOpacity: var(--aspen-palette-opacity-12);--aspen-palette-action-activeChannel: var(--slate-10-string);--aspen-palette-action-selectedChannel: var(--black-string);--aspen-palette-alert-errorColor: var(--red-70);--aspen-palette-alert-infoColor: var(--slate-70);--aspen-palette-alert-successColor: var(--green-70);--aspen-palette-alert-warningColor: var(--orange-70);--aspen-palette-alert-errorFilledColor: var(--white);--aspen-palette-alert-infoFilledColor: var(--white);--aspen-palette-alert-successFilledColor: var(--black);--aspen-palette-alert-warningFilledColor: var(--black);--aspen-palette-alert-errorStandardBg: var(--red-10);--aspen-palette-alert-infoStandardBg: var(--slate-10);--aspen-palette-alert-successStandardBg: var(--green-10);--aspen-palette-alert-warningStandardBg: var(--orange-10);--aspen-palette-background-paper: var(--white);--aspen-palette-background-default: var(--white);--aspen-palette-background-defaultChannel: var(--white-string);--aspen-palette-background-paperChannel: var(--white-string);--aspen-palette-common-black: var(--black);--aspen-palette-common-white: var(--white);--aspen-palette-common-background: var(--white);--aspen-palette-common-backgroundChannel: var(--white-string);--aspen-palette-common-onBackground: var(--black);--aspen-palette-common-onBackgroundChannel: var(--black-string);--aspen-palette-divider-defaultChannel: var(--slate-15-string);--aspen-palette-divider-defaultOpacity: var(--aspen-palette-opacity-1);--aspen-palette-error-main: var(--red-60);--aspen-palette-error-mainChannel: var(--red-60-string);--aspen-palette-error-light: var(--red-40);--aspen-palette-error-lightChannel: var(--red-40-string);--aspen-palette-error-dark: var(--red-70);--aspen-palette-error-darkChannel: var(--red-70-string);--aspen-palette-error-contrastText: var(--white);--aspen-palette-error-contrastTextChannel: var(--white-string);--aspen-palette-FilledInput-bg: var(--aspen-palette-FilledInput-light-bg);--aspen-palette-FilledInput-hoverBg: var( --aspen-palette-FilledInput-light-hoverBg );--aspen-palette-FilledInput-disabledBg: var( --aspen-palette-FilledInput-light-disabledBg );--aspen-palette-info-main: var(--slate-50);--aspen-palette-info-mainChannel: var(--slate-50-string);--aspen-palette-info-light: var(--slate-40);--aspen-palette-info-lightChannel: var(--slate-40-string);--aspen-palette-info-dark: var(--slate-60);--aspen-palette-info-darkChannel: var(--slate-60-string);--aspen-palette-info-contrastText: var(--white);--aspen-palette-info-contrastTextChannel: var(--white-string);--aspen-palette-primary-main: var(--blue-60);--aspen-palette-primary-mainChannel: var(--blue-60-string);--aspen-palette-primary-light: var(--blue-10);--aspen-palette-primary-lightChannel: var(--blue-10-string);--aspen-palette-primary-dark: var(--blue-70);--aspen-palette-primary-darkChannel: var(--blue-70-string);--aspen-palette-primary-contrastText: var(--white);--aspen-palette-primary-contrastTextChannel: var(--white-string);--aspen-palette-secondary-main: var(--blue-40);--aspen-palette-secondary-mainChannel: var(--blue-40-string);--aspen-palette-secondary-light: var(--blue-20);--aspen-palette-secondary-lightChannel: var(--blue-20-string);--aspen-palette-secondary-dark: var(--blue-50);--aspen-palette-secondary-darkChannel: var(--blue-50-string);--aspen-palette-secondary-contrastText: var(--white);--aspen-palette-secondary-contrastTextChannel: var(--white-string);--aspen-palette-SnackbarContent-bg: var(--slate-90);--aspen-palette-SnackbarContent-color: var(--white);--aspen-palette-SpeedDialAction-fabHoverBg: var(--slate-10);--aspen-palette-StepConnector-border: var(--slate-15);--aspen-palette-StepContent-border: var(--slate-15);--aspen-palette-success-main: var(--green-50);--aspen-palette-success-mainChannel: var(--green-50-string);--aspen-palette-success-light: var(--green-40);--aspen-palette-success-lightChannel: var(--green-40-string);--aspen-palette-success-dark: var(--green-60);--aspen-palette-success-darkChannel: var(--green-60-string);--aspen-palette-success-contrastText: var(--white);--aspen-palette-success-contrastTextChannel: var(--white-string);--aspen-palette-switch-defaultColor: var(--slate-50);--aspen-palette-switch-defaultDisabledColor: var(--slate-40);--aspen-palette-switch-primaryDisabledColor: var(--slate-40);--aspen-palette-switch-secondaryDisabledColor: var(--slate-20);--aspen-palette-TableCellBorder-color: var(--white-string);--aspen-palette-TableCellBorder-opacity: var(--black-string);--aspen-palette-text-primary: var(--slate-90);--aspen-palette-text-primaryOpacity: var(--aspen-palette-opacity-87);--aspen-palette-text-primaryChannel: var(--slate-90-string);--aspen-palette-text-secondary: var(--black);--aspen-palette-text-secondaryOpacity: var(--aspen-palette-opacity-60);--aspen-palette-text-secondaryChannel: var(--black-string);--aspen-palette-text-disabled: var(--black);--aspen-palette-text-disabledOpacity: var(--aspen-palette-opacity-12);--aspen-palette-Tooltip-bg: var(--slate-90);--aspen-palette-warning-main: var(--orange-50);--aspen-palette-warning-mainChannel: var(--orange-50-string);--aspen-palette-warning-light: var(--orange-40);--aspen-palette-warning-lightChannel: var(--orange-40-string);--aspen-palette-warning-dark: var(--orange-60);--aspen-palette-warning-darkChannel: var(--orange-60-string);--aspen-palette-warning-contrastText: var(--white);--aspen-palette-warning-contrastTextChannel: var(--white-string);--aspen-palette-zindexComponent-mobileStepper: var(--zindex-1000);--aspen-palette-zindexComponent-fab: var(--zindex-1050);--aspen-palette-zindexComponent-speedDial: var(--zindex-1050);--aspen-palette-zindexComponent-appBar: var(--zindex-1100);--aspen-palette-zindexComponent-drawer: var(--zindex-1200);--aspen-palette-zindexComponent-modal: var(--zindex-1300);--aspen-palette-zindexComponent-snackbar: var(--zindex-1400);--aspen-palette-zindexComponent-tooltip: var(--zindex-1500);--background-color-navigation-hover: var(--slate-10);--background-color-navigation-enabled: var(--transparent);--background-color-navigation-disabled: var(--transparent);--border-color-navigation-enabled: var(--transparent);--border-color-navigation-hover: var(--transparent);--border-color-navigation-disabled: var(--transparent);--outline-offset-default: var(--size-2);--outline-offset-none: var(--size-0);--outline-width-default: var(--border-width-medium);--outline-width-none: var(--border-width-none);--outline-color-default: var(--border-color-selected);--padding-none: var(--size-0);--padding-large: var(--size-16);--padding-medium: var(--size-12);--padding-small: var(--size-8);--padding-x-large: var(--size-20);--padding-xx-large: var(--size-24);--padding-x-small: var(--size-6);--padding-xx-small: var(--size-4);--padding-xxx-small: var(--size-2);--background-color-shell-default: var(--slate-05);--background-color-shell-weak: var(--slate-02);--background-color-shell-strong: var(--slate-10);--background-color-topbar-primary: var(--white);--background-color-topbar-secondary: var(--gray-05);--text-align-left: var(--text-align-start);--text-align-right: var(--text-align-end);--text-align-default: var(--text-align-start)}}}@layer global{:root{--black:#000;--black-string:0 0 0;--blue-10:#ebf4ff;--blue-20:#cce4ff;--blue-30:#99cdff;--blue-40:#66b3ff;--blue-50:#008cff;--blue-60:#006ee3;--blue-70:#0067d4;--blue-80:#0054ad;--blue-90:#1d2b40;--blue-20-string:204 228 255;--blue-90-string:29 43 64;--blue-10-string:235 244 255;--blue-40-string:102 179 255;--blue-70-string:0 103 212;--blue-80-string:0 84 173;--blue-50-string:0 140 255;--blue-60-string:0 110 227;--blue-05:#f4f9ff;--font-family-default:"Default UI Font";--font-family-icons:"Default Icons";--font-style-default:normal;--font-weight-default:normal;--font-weight-strong:600;--inter-font-family:Inter;--lato-font-family:Lato;--line-height-xx-small:.6;--line-height-small:1;--line-height-medium:normal;--line-height-large:1.4;--line-height-x-large:1.6;--line-height-xx-large:1.8;--line-height-xxx-large:2;--line-height-x-small:.8;--sans-serif-font-family:sans-serif;--serif-font-family:serif;--system-ui-font-family:system-ui;--times-new-roman-font-family:"Times New Roman";--gray-10:#e6e6e6;--gray-20:#ccc;--gray-30:#b3b3b3;--gray-40:#999;--gray-50:grey;--gray-60:#666;--gray-70:#4c4c4c;--gray-80:#333;--gray-90:#191919;--gray-05:#f2f2f2;--green-10:#edfdf3;--green-20:#d1fae0;--green-30:#a2f6c3;--green-40:#7beaa5;--green-50:#36d576;--green-60:#14b053;--green-70:#0e7c3a;--green-80:#0b602d;--green-90:#09321a;--green-10-string:237 253 243;--green-30-string:162 246 195;--green-70-string:14 124 58;--green-90-string:9 50 26;--green-05:#f5fdf8;--green-40-string:123 234 165;--green-50-string:54 213 118;--green-60-string:20 176 83;--icon-add:"add";--icon-close:"close";--icon-delete:"delete";--icon-edit:"edit";--icon-error:"error";--icon-info:"info";--icon-remove:"remove";--icon-success:"check_circle";--icon-warning:"warning";--aspen-palette-FilledInput-light-bg:#0000000f;--aspen-palette-FilledInput-light-hoverBg:#00000017;--aspen-palette-FilledInput-light-disabledBg:#0000001f;--aspen-palette-FilledInput-dark-bg:#ffffff0f;--aspen-palette-FilledInput-dark-hoverBg:#ffffff17;--aspen-palette-FilledInput-dark-disabledBg:#ffffff1f;--aspen-palette-opacity-1:1;--aspen-palette-opacity-12:.12;--aspen-palette-opacity-14:.14;--aspen-palette-opacity-20:.2;--aspen-palette-opacity-26:.26;--aspen-palette-opacity-38:.38;--aspen-palette-opacity-54:.54;--aspen-palette-opacity-60:.6;--aspen-palette-opacity-87:.87;--aspen-palette-opacity-04:.04;--aspen-palette-opacity-08:.08;--aspen-palette-opacity-inputPlaceholder:.42;--aspen-palette-opacity-inputUnderline:.42;--aspen-palette-opacity-switchTrackDisabled:.12;--aspen-palette-opacity-switchTrack:.38;--aspen-shadows-1:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-2) var(--aspen-shadows-offset-1) var(--aspen-shadows-offset-negative-1) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-1) var(--aspen-shadows-offset-1) var(--aspen-shadows-offset-0) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-1) var(--aspen-shadows-offset-3) var(--aspen-shadows-offset-0) var(--aspen-shadows-color-3);--aspen-shadows-2:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-3) var(--aspen-shadows-offset-1) var(--aspen-shadows-offset-negative-2) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-2) var(--aspen-shadows-offset-2) var(--aspen-shadows-offset-0) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-1) var(--aspen-shadows-offset-5) var(--aspen-shadows-offset-0) var(--aspen-shadows-color-3);--aspen-shadows-3:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-3) var(--aspen-shadows-offset-3) var(--aspen-shadows-offset-negative-2) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-3) var(--aspen-shadows-offset-4) var(--aspen-shadows-offset-0) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-1) var(--aspen-shadows-offset-8) var(--aspen-shadows-offset-0) var(--aspen-shadows-color-3);--aspen-shadows-4:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-2) var(--aspen-shadows-offset-4) var(--aspen-shadows-offset-negative-1) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-4) var(--aspen-shadows-offset-5) var(--aspen-shadows-offset-0) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-1) var(--aspen-shadows-offset-10) var(--aspen-shadows-offset-0) var(--aspen-shadows-color-3);--aspen-shadows-5:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-3) var(--aspen-shadows-offset-5) var(--aspen-shadows-offset-negative-1) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-5) var(--aspen-shadows-offset-8) var(--aspen-shadows-offset-0) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-1) var(--aspen-shadows-offset-14) var(--aspen-shadows-offset-0) var(--aspen-shadows-color-3);--aspen-shadows-6:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-3) var(--aspen-shadows-offset-5) var(--aspen-shadows-offset-negative-1) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-6) var(--aspen-shadows-offset-10) var(--aspen-shadows-offset-0) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-1) var(--aspen-shadows-offset-18) var(--aspen-shadows-offset-0) var(--aspen-shadows-color-3);--aspen-shadows-7:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-4) var(--aspen-shadows-offset-5) var(--aspen-shadows-offset-negative-2) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-7) var(--aspen-shadows-offset-10) var(--aspen-shadows-offset-1) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-2) var(--aspen-shadows-offset-16) var(--aspen-shadows-offset-1) var(--aspen-shadows-color-3);--aspen-shadows-8:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-5) var(--aspen-shadows-offset-5) var(--aspen-shadows-offset-negative-3) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-8) var(--aspen-shadows-offset-10) var(--aspen-shadows-offset-1) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-3) var(--aspen-shadows-offset-14) var(--aspen-shadows-offset-2) var(--aspen-shadows-color-3);--aspen-shadows-9:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-5) var(--aspen-shadows-offset-6) var(--aspen-shadows-offset-negative-3) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-9) var(--aspen-shadows-offset-12) var(--aspen-shadows-offset-1) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-3) var(--aspen-shadows-offset-16) var(--aspen-shadows-offset-2) var(--aspen-shadows-color-3);--aspen-shadows-10:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-6) var(--aspen-shadows-offset-6) var(--aspen-shadows-offset-negative-3) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-10) var(--aspen-shadows-offset-14) var(--aspen-shadows-offset-1) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-4) var(--aspen-shadows-offset-18) var(--aspen-shadows-offset-3) var(--aspen-shadows-color-3);--aspen-shadows-11:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-6) var(--aspen-shadows-offset-7) var(--aspen-shadows-offset-negative-4) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-11) var(--aspen-shadows-offset-15) var(--aspen-shadows-offset-1) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-4) var(--aspen-shadows-offset-20) var(--aspen-shadows-offset-3) var(--aspen-shadows-color-3);--aspen-shadows-12:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-7) var(--aspen-shadows-offset-8) var(--aspen-shadows-offset-negative-4) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-12) var(--aspen-shadows-offset-17) var(--aspen-shadows-offset-2) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-5) var(--aspen-shadows-offset-22) var(--aspen-shadows-offset-4) var(--aspen-shadows-color-3);--aspen-shadows-13:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-7) var(--aspen-shadows-offset-8) var(--aspen-shadows-offset-negative-4) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-13) var(--aspen-shadows-offset-19) var(--aspen-shadows-offset-2) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-5) var(--aspen-shadows-offset-24) var(--aspen-shadows-offset-4) var(--aspen-shadows-color-3);--aspen-shadows-14:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-7) var(--aspen-shadows-offset-9) var(--aspen-shadows-offset-negative-4) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-14) var(--aspen-shadows-offset-21) var(--aspen-shadows-offset-2) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-5) var(--aspen-shadows-offset-26) var(--aspen-shadows-offset-4) var(--aspen-shadows-color-3);--aspen-shadows-15:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-8) var(--aspen-shadows-offset-9) var(--aspen-shadows-offset-negative-5) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-15) var(--aspen-shadows-offset-22) var(--aspen-shadows-offset-2) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-6) var(--aspen-shadows-offset-28) var(--aspen-shadows-offset-5) var(--aspen-shadows-color-3);--aspen-shadows-16:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-8) var(--aspen-shadows-offset-10) var(--aspen-shadows-offset-negative-5) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-16) var(--aspen-shadows-offset-24) var(--aspen-shadows-offset-2) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-6) var(--aspen-shadows-offset-30) var(--aspen-shadows-offset-5) var(--aspen-shadows-color-3);--aspen-shadows-17:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-8) var(--aspen-shadows-offset-11) var(--aspen-shadows-offset-negative-5) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-17) var(--aspen-shadows-offset-26) var(--aspen-shadows-offset-2) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-6) var(--aspen-shadows-offset-32) var(--aspen-shadows-offset-5) var(--aspen-shadows-color-3);--aspen-shadows-18:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-9) var(--aspen-shadows-offset-11) var(--aspen-shadows-offset-negative-5) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-18) var(--aspen-shadows-offset-28) var(--aspen-shadows-offset-2) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-7) var(--aspen-shadows-offset-34) var(--aspen-shadows-offset-6) var(--aspen-shadows-color-3);--aspen-shadows-19:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-9) var(--aspen-shadows-offset-12) var(--aspen-shadows-offset-negative-6) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-19) var(--aspen-shadows-offset-29) var(--aspen-shadows-offset-2) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-7) var(--aspen-shadows-offset-36) var(--aspen-shadows-offset-6) var(--aspen-shadows-color-3);--aspen-shadows-20:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-10) var(--aspen-shadows-offset-13) var(--aspen-shadows-offset-negative-6) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-20) var(--aspen-shadows-offset-31) var(--aspen-shadows-offset-3) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-8) var(--aspen-shadows-offset-38) var(--aspen-shadows-offset-7) var(--aspen-shadows-color-3);--aspen-shadows-21:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-10) var(--aspen-shadows-offset-13) var(--aspen-shadows-offset-negative-6) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-21) var(--aspen-shadows-offset-33) var(--aspen-shadows-offset-3) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-8) var(--aspen-shadows-offset-40) var(--aspen-shadows-offset-7) var(--aspen-shadows-color-3);--aspen-shadows-22:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-10) var(--aspen-shadows-offset-14) var(--aspen-shadows-offset-negative-6) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-22) var(--aspen-shadows-offset-35) var(--aspen-shadows-offset-3) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-8) var(--aspen-shadows-offset-42) var(--aspen-shadows-offset-7) var(--aspen-shadows-color-3);--aspen-shadows-23:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-11) var(--aspen-shadows-offset-14) var(--aspen-shadows-offset-negative-7) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-23) var(--aspen-shadows-offset-36) var(--aspen-shadows-offset-3) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-9) var(--aspen-shadows-offset-44) var(--aspen-shadows-offset-8) var(--aspen-shadows-color-3);--aspen-shadows-24:var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-11) var(--aspen-shadows-offset-15) var(--aspen-shadows-offset-negative-7) var(--aspen-shadows-color-1),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-24) var(--aspen-shadows-offset-38) var(--aspen-shadows-offset-3) var(--aspen-shadows-color-2),var(--aspen-shadows-offset-0) var(--aspen-shadows-offset-9) var(--aspen-shadows-offset-46) var(--aspen-shadows-offset-8) var(--aspen-shadows-color-3);--aspen-shadows-offset-0:0rem;--aspen-shadows-offset-1:.0625rem;--aspen-shadows-offset-2:.125rem;--aspen-shadows-offset-3:.1875rem;--aspen-shadows-offset-4:.25rem;--aspen-shadows-offset-5:.3125rem;--aspen-shadows-offset-6:.375rem;--aspen-shadows-offset-7:.4375rem;--aspen-shadows-offset-8:.5rem;--aspen-shadows-offset-9:.5625rem;--aspen-shadows-offset-10:.625rem;--aspen-shadows-offset-11:.6875rem;--aspen-shadows-offset-12:.75rem;--aspen-shadows-offset-13:.3125rem;--aspen-shadows-offset-14:.875rem;--aspen-shadows-offset-15:.9375rem;--aspen-shadows-offset-16:1rem;--aspen-shadows-offset-17:1.0625rem;--aspen-shadows-offset-18:1.125rem;--aspen-shadows-offset-19:1.1875rem;--aspen-shadows-offset-20:1.25rem;--aspen-shadows-offset-21:1.3125rem;--aspen-shadows-offset-22:1.375rem;--aspen-shadows-offset-23:1.4375rem;--aspen-shadows-offset-24:1.5rem;--aspen-shadows-offset-26:1.625rem;--aspen-shadows-offset-28:1.75rem;--aspen-shadows-offset-29:1.8125rem;--aspen-shadows-offset-30:1.875rem;--aspen-shadows-offset-31:1.9375rem;--aspen-shadows-offset-32:2rem;--aspen-shadows-offset-33:2.0625rem;--aspen-shadows-offset-34:2.125rem;--aspen-shadows-offset-35:2.1875rem;--aspen-shadows-offset-36:2.25rem;--aspen-shadows-offset-38:2.375rem;--aspen-shadows-offset-40:2.5rem;--aspen-shadows-offset-42:2.625rem;--aspen-shadows-offset-44:2.75rem;--aspen-shadows-offset-46:2.875rem;--aspen-shadows-offset-negative-0:0rem;--aspen-shadows-offset-negative-1:-.0625rem;--aspen-shadows-offset-negative-2:-.125rem;--aspen-shadows-offset-negative-3:-.1875rem;--aspen-shadows-offset-negative-4:-.25rem;--aspen-shadows-offset-negative-5:-.3125rem;--aspen-shadows-offset-negative-6:-.375rem;--aspen-shadows-offset-negative-7:-.4375rem;--aspen-shadows-color-1:rgba(var(--aspen-palette-common-onBackgroundChannel)/var(--aspen-palette-opacity-20));--aspen-shadows-color-2:rgba(var(--aspen-palette-common-onBackgroundChannel)/var(--aspen-palette-opacity-14));--aspen-shadows-color-3:rgba(var(--aspen-palette-common-onBackgroundChannel)/var(--aspen-palette-opacity-12));--zindex-1000:1000;--zindex-1050:1050;--zindex-1100:1100;--zindex-1200:1200;--zindex-1300:1300;--zindex-1400:1400;--zindex-1500:1500;--opacity-slate:#4c5b70;--opacity-0:0;--opacity-10:10;--opacity-20:20;--opacity-30:30;--opacity-40:40;--opacity-50:50;--opacity-60:60;--opacity-70:70;--opacity-80:80;--opacity-90:90;--opacity-100:100;--orange-10:#fef5ee;--orange-20:#fddcc4;--orange-30:#fac2a0;--orange-40:#ffb37a;--orange-50:#fa9950;--orange-60:#df763a;--orange-70:#c04d1b;--orange-80:#903e1b;--orange-90:#482a0e;--orange-10-string:254 245 238;--orange-30-string:250 194 160;--orange-70-string:192 77 27;--orange-90-string:72 42 14;--orange-40-string:255 179 122;--orange-50-string:250 153 80;--orange-60-string:223 118 58;--orange-05:#fdf7f4;--purple-10:#f5f0fc;--purple-20:#e7dcfa;--purple-30:#c8aff0;--purple-40:#a67fe3;--purple-50:#8c5bd8;--purple-60:#6d2ed1;--purple-70:#5817bd;--purple-80:#380e78;--purple-90:#22094a;--purple-05:#faf7fd;--red-10:#feecec;--red-20:#fccfcf;--red-30:#f6b1b1;--red-40:#f58a8a;--red-50:#eb5656;--red-60:#d61f1f;--red-70:#ad1111;--red-80:#750c0c;--red-90:#400707;--red-10-string:254 236 236;--red-30-string:246 177 177;--red-90-string:64 7 7;--red-40-string:245 138 138;--red-50-string:235 86 86;--red-60-string:214 31 31;--red-70-string:173 17 17;--red-80-string:117 12 12;--red-05:#fef5f5;--size-0:0rem;--size-1:.0625rem;--size-2:.125rem;--size-3:.1875rem;--size-4:.25rem;--size-6:.375rem;--size-8:.5rem;--size-9:.5625rem;--size-10:.625rem;--size-11:.6875rem;--size-12:.75rem;--size-13:.8125rem;--size-14:.875rem;--size-15:.9375rem;--size-16:1rem;--size-18:1.125rem;--size-20:1.25rem;--size-24:1.5rem;--size-32:2rem;--size-40:2.5rem;--size-48:3rem;--size-64:4rem;--size-72:4.5rem;--size-100:6.25rem;--size-128:8rem;--size-160:10rem;--size-200:12.5rem;--slate-10:#f1f5f9;--slate-15:#e1e7ef;--slate-20:#cbd5e1;--slate-30:#a9b5c6;--slate-40:#8596ad;--slate-50:#5e6d82;--slate-60:#4c5b70;--slate-70:#3c4b5f;--slate-80:#2a3547;--slate-90:#172336;--slate-95:#121c2d;--slate-100:#0d1323;--slate-15-string:225 231 239;--slate-20-string:203 213 225;--slate-30-string:169 181 198;--slate-90-string:23 35 54;--slate-10-string:241 245 249;--slate-70-string:60 75 95;--slate-80-string:42 53 71;--slate-05:#f7f9fc;--slate-05-string:247 249 252;--slate-02:#fafbfc;--slate-40-string:133 150 173;--slate-50-string:94 109 130;--slate-60-string:76 91 112;--transparent:#0000;--background-image-default:initial;--background-position-x-default:initial;--background-position-y-default:initial;--border-style-default:solid;--box-shadow-default:initial;--box-shadow-low:0px 4px 8px #0003;--cursor-auto:auto;--cursor-default:default;--cursor-none:none;--cursor-context-menu:context-menu;--cursor-help:help;--cursor-pointer:pointer;--cursor-progress:progress;--cursor-wait:wait;--cursor-cell:cell;--cursor-crosshair:crosshair;--cursor-text:text;--cursor-vertical-text:vertical-text;--cursor-alias:alias;--cursor-copy:copy;--cursor-move:move;--cursor-no-drop:no-drop;--cursor-not-allowed:not-allowed;--cursor-grab:grab;--cursor-grabbing:grabbing;--cursor-all-scroll:all-scroll;--cursor-col-resize:col-resize;--cursor-row-resize:row-resize;--cursor-n-resize:n-resize;--cursor-s-resize:s-resize;--cursor-e-resize:e-resize;--cursor-w-resize:w-resize;--cursor-ne-resize:ne-resize;--cursor-nw-resize:nw-resize;--cursor-se-resize:se-resize;--cursor-sw-resize:sw-resize;--cursor-ew-resize:ew-resize;--cursor-ns-resize:ns-resize;--cursor-nesw-resize:nesw-resize;--cursor-nwse-resize:nwse-resize;--cursor-zoom-in:zoom-in;--cursor-zoom-out:zoom-out;--display-initial:initial;--text-align-start:start;--text-align-end:end;--text-align-center:center;--text-align-justify:justify;--text-align-justify-all:justify-all;--text-transform-capitalize:capitalize;--text-transform-uppercase:uppercase;--text-transform-lowercase:lowercase;--text-transform-none:none;--text-transform-default:initial;--white:#fff;--white-string:255 255 255;--yellow-10:#fffbea;--yellow-20:#fff1b3;--yellow-30:#ffe980;--yellow-40:#ffdd35;--yellow-50:#fad100;--yellow-60:#e8b407;--yellow-70:#c28e00;--yellow-80:#855c15;--yellow-90:#543308;--yellow-05:#fffdf4}}@font-face{font-family:AspenDesignFont;src:url(./InterVariable.woff2) format("woff2"),url(./InterVariable.woff2) format("woff2");font-weight:400;font-style:normal}body{margin:0;font-family:AspenDesignFont,-apple-system,BlinkMacSystemFont,Segoe UI,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}code{font-family:source-code-pro,Menlo,Monaco,Consolas,Courier New,monospace}.App-logo{height:20px;pointer-events:none}.App-fixed{position:fixed;top:0;-webkit-box-sizing:border-box;box-sizing:border-box;width:100%;z-index:10001;margin-top:0;max-height:100%;background-color:#fff}@media (prefers-reduced-motion: no-preference){.App-logo{animation:App-logo-spin infinite 20s linear}}.App-link{color:#61dafb}.fade.modal.show{z-index:10005}.headerPanel{top:30px;position:relative}.appBarPanel{top:0;position:absolute}.customListGroup{min-width:180px;filter:drop-shadow(0px 4px 10px rgba(0,0,0,.2));display:block;font-size:75%;height:0;z-index:10;background-color:#fff;border-radius:8px;padding:0;opacity:0;transform:translateY(-20px);transition:height .3s ease-in-out,transform .3s ease-in-out,opacity .3s ease-in-out;overflow:hidden;max-height:300px}.customListGroup.show{height:auto;padding:8px 0;opacity:1;text-align:left;transform:translateY(0);white-space:nowrap;width:max-content}.userInfoPanel{text-align:center}.customListGroup .custom-list-item,.customListGroup .custom-list-item-disabled{border:none;padding:10px 16px;border-radius:6px;transition:background-color .3s ease,color .3s ease}.customListGroup .custom-list-item:hover{cursor:pointer;background-color:#2699fb1a;color:#2699fb}.customListGroup .custom-list-item-disabled{opacity:.6;color:#666}.customListGroup .custom-list-item-disabled:hover{cursor:not-allowed;background-color:transparent}img.saveImage,img.postComment{position:relative;margin-top:-4px;margin-right:8px;width:16px;height:16px}img.saveImageHidden{position:relative;margin-top:-4px;visibility:hidden;margin-right:8px}.sharePanel{position:relative;right:25%}.isButton{color:#fff;background-color:#2699fb;border:none;border-radius:4px;width:max-content;height:30px;font-size:14px;font-weight:500;box-shadow:0 4px 6px #0000001a;transition:background-color .3s ease}.isButton:hover{background-color:#1976d2}.isButton:active{background-color:#1565c0;box-shadow:none}.phoenix-header img:not(.divider){cursor:pointer}.phoenix-header a,.phoenix-header a:active,.phoenix-header a:focus,.phoenix-header a:hover{color:#f0ab00}.phoenix-header>.navbar.navbar-default{margin-top:10px;background:#0078c9;border:none;border-radius:0;margin-bottom:0;color:#fff}.phoenix-header>.navbar.navbar-default .navbar-brand [class^=icon-]{width:180px;height:50px}.phoenix-header>.navbar.navbar-default .navbar-nav>li{font-size:90%}.phoenix-header>.navbar.navbar-default .navbar-nav>li.active>a,.phoenix-header>.navbar.navbar-default .navbar-nav>li.open>a{color:#f0ab00}@media (min-width:1025px){.phoenix-header>.navbar.navbar-default .navbar-nav>li>a{padding-left:10px;padding-right:10px}.phoenix-header>.navbar.navbar-default .navbar-nav>li:hover>ul{visibility:visible;display:block}}.phoenix-header>.navbar.navbar-default .dropdown-menu{background:#666;padding:0;border:none;z-index:1005}.phoenix-header>.navbar.navbar-default .dropdown-menu>li.active,.phoenix-header>.navbar.navbar-default .dropdown-menu>li.open{background:#383838}.phoenix-header>.navbar.navbar-default .dropdown-menu>li>a{color:inherit;outline:0;padding:1rem 1.5rem}.phoenix-header>.navbar.navbar-default .dropdown-menu>li>a:active,.phoenix-header>.navbar.navbar-default .dropdown-menu>li>a:focus,.phoenix-header>.navbar.navbar-default .dropdown-menu>li>a:hover{background:#383838}.phoenix-header>.navbar.navbar-default .dropdown-menu>li+li{border-top:1px solid #383838}.phoenix-header>.navbar.navbar-default .dropdown-menu>li>a,.phoenix-header>.navbar.navbar-default .navbar-nav>li>a{color:inherit;outline:0;background:0 0}.phoenix-header>.navbar.navbar-default .dropdown-menu>li:not(.disabled).active>a,.phoenix-header>.navbar.navbar-default .dropdown-menu>li:not(.disabled).open>a,.phoenix-header>.navbar.navbar-default .dropdown-menu>li:not(.disabled):active>a,.phoenix-header>.navbar.navbar-default .dropdown-menu>li:not(.disabled):focus>a,.phoenix-header>.navbar.navbar-default .dropdown-menu>li:not(.disabled):hover>a,.phoenix-header>.navbar.navbar-default .navbar-nav>li:not(.disabled).active>a,.phoenix-header>.navbar.navbar-default .navbar-nav>li:not(.disabled).open>a,.phoenix-header>.navbar.navbar-default .navbar-nav>li:not(.disabled):active>a,.phoenix-header>.navbar.navbar-default .navbar-nav>li:not(.disabled):focus>a,.phoenix-header>.navbar.navbar-default .navbar-nav>li:not(.disabled):hover>a{color:#f0ab00!important}.phoenix-header>.navbar.navbar-default .navbar-dropdown{margin-top:8px;margin-bottom:8px}.phoenix-header>.navbar.navbar-default .navbar-dropdown>.dropdown{width:auto;min-width:15rem;padding-left:8px;padding-right:8px}.phoenix-header>.navbar.navbar-default .navbar-dropdown>.dropdown>.btn{width:100%}.phoenix-header>.navbar.navbar-default .navbar-dropdown>.dropdown>.btn .content-name{overflow:hidden}.phoenix-header>.navbar.navbar-default .navbar-dropdown .site-description{font-size:80%}.phoenix-header>.navbar.navbar-default .navbar-dropdown .site-description:before{content:" - ";font-size:80%}@media (max-width:767px){.phoenix-header>.navbar.navbar-default .navbar-dropdown{padding-left:15px;padding-right:15px}.phoenix-header>.navbar.navbar-default .navbar-dropdown>.dropdown{width:100%;padding-left:0;padding-right:0}}.phoenix-header+.loading{margin-top:3rem;flex-grow:1}.headerItemDivider{margin-left:-1px;margin-right:-1px;position:relative;bottom:5px}.headerItemAspenTech{margin-left:-7px;margin-right:5px;position:relative;bottom:3.5px}.headerItemText{margin-left:5px;margin-right:5px;font-size:15px;position:relative;bottom:2px}.bold-text{font-weight:700}.headerItem{margin-left:11px;margin-right:11px;width:20px;height:20px;position:relative;bottom:5px;transition:color .3s ease,transform .3s ease}.headerItem.home{width:24px;height:24px}.headerItem.divider{width:30px;height:24px}.headerItem.pattern{width:16px;height:16px}.headerItem.search{margin-top:6px}.headerItem.ReturnToSearch_btn3{margin-top:7px;height:17px;width:17px;padding-top:0!important}.headerItem:hover{transform:scale(1.15)}.headerItem:active{transform:scale(.95)}.header-left-container{display:flex;height:24px}.header-search-container{position:relative;display:inline-flex;height:20px;margin-left:10px;top:-5px;align-items:center}.search-container{display:flex;align-items:center;background-color:#fff;border-radius:18px;box-shadow:0 2px 5px #0000001a;padding:0 9px;transition:box-shadow .3s ease;margin-top:-5px}.search-container:hover{box-shadow:0 4px 10px #0000004d}.global_search_input{border:none;outline:none;font-size:14px;padding:3px 5px;border-radius:20px;width:120%;box-sizing:border-box}.search-button{background:none;border:none;cursor:pointer;margin-left:6px}.search-button:hover{box-shadow:0 4px 10px #0000004d}.search-icon{width:16px;height:16px}@media (max-width: 500px){.global_search_input{width:80px;font-size:10px;height:28px}}#root>.container-fluid{height:100%;display:flex;flex-direction:column;overflow:hidden}#root>.container-fluid>.authenticated-root,#root>.container-fluid>.authenticated-root>div:not(.alert):not(.loading){display:flex;flex-direction:column;flex-grow:1;min-height:0}#root>.container-fluid>div.popup-conductor{flex-grow:0;flex-shrink:1}.landing{text-align:center;position:fixed;height:calc(100% - 45px);width:100%;overflow:auto}.file-dropdown{filter:drop-shadow(0 3px 6px rgba(0,0,0,.24))}.file-menu{text-align:right;visibility:hidden;display:flex;align-items:center;justify-content:flex-end}.list-item{cursor:pointer}.list-item:hover{background-color:#2699fb29}.list-item:active{background-color:#fff}.list-item:hover .file-menu{visibility:visible}.userFavesFileRow{height:40px;padding-top:6px;padding-left:13px;align-items:center}.userFavesFileImage{width:24px}.fileNameLabel{vertical-align:middle;text-decoration:none;color:#2699fb;font-size:18px;display:block;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}@media screen and (max-width: 768px){.fileNameLabel{font-size:11px}}.file-item{display:flex;gap:10px;overflow:hidden}.file-name{overflow:hidden}.file-row{display:flex;justify-content:space-between;width:100%}.hideFileMenu{display:none}.file-table{max-height:calc(100vh - 468px);text-align:left;padding:13px;width:50%}header .no-files-message{text-align:center;color:#888;padding:10px}.file-items{overflow:auto}.header-name{font-size:20px;margin-top:8px;font-weight:700}.landing-files-icon{vertical-align:-webkit-baseline-middle}@media screen and (max-width: 780px){.file-table{max-height:100%}.header-name{font-size:14px}.file-items{overflow:visible}}.file-table-title{display:flex;gap:6px;align-items:center;margin-top:-2px}.border-bottom-0 .col-2{width:8%}.card-header{height:50px;background-color:#faf9f6}#bootstrap-override .card{border-radius:4px}.list-group{border-radius:initial;height:100%}.m-0{padding:0 52.5px 32px}.banner{background-image:url(./Banner.png);background-size:auto;background-color:#061d3c;background-repeat:no-repeat;background-position:right;height:120px;display:flex;box-shadow:0 4px 10px #00000080}.greeting{position:relative;color:#fff;font-size:200%;padding-left:5%;text-align:left;top:30%}@media screen and (max-width: 768px){.greeting{font-size:120%}}.progress-modal .modal-dialog{width:auto;max-width:800px;max-width:80vw}.progress-modal .modal-dialog .modal-body{display:flex}.progress-modal .modal-dialog .modal-body>.progress-modal-message{white-space:nowrap;min-width:30rem;margin-right:3rem;flex-grow:1}.progress-modal .modal-dialog .modal-body>.progress-modal-spinner{display:flex;align-items:center;overflow:hidden}.progress-modal .modal-dialog .modal-body>*{margin:0!important}
