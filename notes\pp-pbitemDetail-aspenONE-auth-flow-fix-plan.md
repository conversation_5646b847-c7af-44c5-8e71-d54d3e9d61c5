# PBItemDetails & aspenONE Authentication Flow Fix Plan

## Executive Summary
This document outlines the plan to implement a unified 8-scenario authentication flow for both `PBItemDetails.asp` and `aspenONE.html` using reusable components and shared logic.

## Current State Analysis

### PBItemDetails.asp (IMPLEMENTED) ✅
- **Status**: Complete 8-scenario authentication flow implemented
- **Key Features**:
  - JSON configuration validation (`isJSONConfigValid()`)
  - localStorage authentication state checking (`hasValidLocalStorage()`)
  - Authentication data clearing (`clearAllAuthData()`)
  - 8-scenario flow logic (`determineAuthFlow()`)
  - MSAL configuration validation (`checkMSALConfiguration()`)
  - Configuration change detection
  - Custom overlay management
  - JWT username extraction with 10-priority system

### aspenONE.html (NEEDS IMPLEMENTATION) ❌
- **Status**: Uses existing `auth.js` and `ProcessExplorerAuth.initialize()`
- **Limitations**:
  - No 8-scenario flow logic
  - No configuration change detection
  - No localStorage clearing on invalid JSON
  - Inconsistent behavior compared to PBItemDetails.asp

## 8-Scenario Requirements

| Scenario | JSON Config | localStorage | Expected Behavior |
|----------|-------------|--------------|-------------------|
| 1 | Valid | None | Show overlay + login panel |
| 2 | Valid | Valid | No overlay, no login panel |
| 3 | Invalid | Any | Clear localStorage + no overlay |
| 4 | Invalid | None (after clear) | No overlay, no login panel |
| 5 | Valid (after fix) | None | Show overlay + login panel |
| 6 | Valid | Valid (after login) | No overlay, no login panel |
| 7 | Invalid (after valid) | Valid | Clear localStorage + no overlay |
| 8 | Invalid | None (after clear) | No overlay, no login panel |

## Implementation Plan

### Phase 1: Create Shared Authentication Library

#### 1.1 New File: `ASP/msal/authenticationFlowManager.js`
**Purpose**: Centralized authentication logic shared between both pages

**Core Functions**:
```javascript
window.AuthFlowManager = {
    // Configuration Management
    isJSONConfigValid: function() { /* Validate MSAL config */ },
    checkMSALConfiguration: function() { /* Enhanced validation */ },
    generateConfigHash: function() { /* For change detection */ },
    hasConfigurationChanged: function() { /* Detect config changes */ },
    
    // Storage Management  
    hasValidLocalStorage: function() { /* Check auth data */ },
    clearAllAuthData: function() { /* Clear all storage */ },
    validateTokenExpiration: function() { /* Check token validity */ },
    
    // Flow Logic
    determineAuthFlow: function() { /* Main 8-scenario logic */ },
    apply8ScenarioFlow: function(callbacks) { /* Apply with page-specific callbacks */ },
    
    // Utility Functions
    extractUsernameFromJWTToken: function(token) { /* JWT parsing */ },
    getUserProfileFromREST: function(email, callback) { /* REST conversion */ }
};

#### 1.2 Configuration Validation Logic
isJSONConfigValid: function() {
    try {
        if (!window.msalConfig || !window.msalConfig.auth) return false;
        
        var clientId = window.msalConfig.auth.clientId;
        var authority = window.msalConfig.auth.authority;
        
        // Check for placeholder values
        var isValidClientId = clientId && 
            clientId !== "Enter_the_Application_Id_Here" && 
            /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId);
        
        var isValidAuthority = authority && 
            authority !== "https://login.microsoftonline.com/Enter_the_Tenant_ID_Here" && 
            authority.includes('login.microsoftonline.com');
        
        return isValidClientId && isValidAuthority;
    } catch (error) {
        return false;
    }
}

#### 1.3 8-Scenario Flow Logic
determineAuthFlow: function() {
    var jsonValid = this.isJSONConfigValid();
    var hasLocalStorage = this.hasValidLocalStorage();
    var configChanged = this.hasConfigurationChanged();
    
    if (!jsonValid) {
        // Scenarios 3, 4, 7, 8: Invalid JSON
        return { showOverlay: false, showLoginPanel: false, clearData: true };
    } else {
        // Valid JSON
        if (hasLocalStorage && !configChanged) {
            // Scenarios 2, 6: Valid JSON + valid localStorage
            return { showOverlay: false, showLoginPanel: false, clearData: false };
        } else {
            // Scenarios 1, 5: Valid JSON + (no localStorage OR config changed)
            return { showOverlay: true, showLoginPanel: true, clearData: configChanged };
        }
    }
}

