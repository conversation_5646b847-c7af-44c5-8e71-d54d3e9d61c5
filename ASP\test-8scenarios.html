<!DOCTYPE html>
<html>
<head>
    <title>8-Scenario Authentication Tester</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .scenario { border: 1px solid #ccc; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .scenario h3 { margin-top: 0; color: #333; }
        .status { padding: 5px 10px; border-radius: 3px; font-weight: bold; }
        .status.pass { background: #d4edda; color: #155724; }
        .status.fail { background: #f8d7da; color: #721c24; }
        .status.pending { background: #fff3cd; color: #856404; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        .info { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; }
        .debug { font-family: monospace; background: #f5f5f5; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>8-Scenario Authentication Tester</h1>
    
    <div class="info">
        <strong>Instructions:</strong>
        <ol>
            <li>Open browser developer tools (F12) to monitor console</li>
            <li>Use the buttons below to test each scenario</li>
            <li>Check the status indicators for results</li>
            <li>Test both aspenONE.html and PBItemDetails.asp</li>
        </ol>
    </div>

    <div class="scenario">
        <h3>Current State</h3>
        <button onclick="checkCurrentState()">Check Current State</button>
        <div id="currentState" class="debug">Click "Check Current State" to see current authentication status</div>
    </div>

    <div class="scenario">
        <h3>Scenario 1: First Visit + Valid JSON</h3>
        <p><strong>Expected:</strong> Show overlay + login panel</p>
        <button onclick="setupScenario1()">Setup Scenario 1</button>
        <button onclick="testAspenONE()">Test aspenONE.html</button>
        <button onclick="testPBItemDetails()">Test PBItemDetails.asp</button>
        <div id="scenario1Status" class="status pending">Not tested</div>
    </div>

    <div class="scenario">
        <h3>Scenario 2: Revisit + Valid JSON</h3>
        <p><strong>Expected:</strong> No overlay, no login panel</p>
        <button onclick="setupScenario2()">Setup Scenario 2</button>
        <button onclick="testAspenONE()">Test aspenONE.html</button>
        <button onclick="testPBItemDetails()">Test PBItemDetails.asp</button>
        <div id="scenario2Status" class="status pending">Not tested</div>
    </div>

    <div class="scenario">
        <h3>Scenario 3: Invalid JSON + Any localStorage</h3>
        <p><strong>Expected:</strong> Clear localStorage, no overlay, no login panel</p>
        <button onclick="setupScenario3()">Setup Scenario 3</button>
        <button onclick="testAspenONE()">Test aspenONE.html</button>
        <button onclick="testPBItemDetails()">Test PBItemDetails.asp</button>
        <div id="scenario3Status" class="status pending">Not tested</div>
    </div>

    <div class="scenario">
        <h3>Scenario 7 Test: Valid→Invalid JSON + localStorage</h3>
        <p><strong>Expected:</strong> Clear localStorage, no overlay, no login panel</p>
        <button onclick="testScenario7()">Test Scenario 7</button>
        <div id="scenario7Status" class="status pending">Not tested</div>
        <div id="scenario7Details" class="debug" style="display: none;"></div>
    </div>

    <div class="scenario">
        <h3>Quick Actions</h3>
        <button onclick="clearAllData()">Clear All Authentication Data</button>
        <button onclick="simulateLogin()">Simulate Successful Login</button>
        <button onclick="switchToValidJSON()">Switch to Valid JSON</button>
        <button onclick="switchToInvalidJSON()">Switch to Invalid JSON</button>
        <button onclick="forceReloadConfig()">Force Reload JSON Config</button>
    </div>

    <script>
        function checkCurrentState() {
            const state = {
                hasSharedAuth: typeof window.SharedAuth !== 'undefined',
                jsonValid: window.SharedAuth ? window.SharedAuth.isJSONConfigValid() : 'SharedAuth not available',
                hasLocalStorage: window.SharedAuth ? window.SharedAuth.hasValidLocalStorage() : 'SharedAuth not available',
                authFlow: window.SharedAuth ? window.SharedAuth.determineAuthFlow() : 'SharedAuth not available',
                userProfile: localStorage.getItem('UserProfile'),
                msalUsername: localStorage.getItem('msal.username'),
                accessToken: !!localStorage.getItem('msal.accessToken'),
                msalConfig: window.msalConfig ? {
                    clientId: window.msalConfig.auth.clientId,
                    authority: window.msalConfig.auth.authority
                } : 'Not available'
            };

            document.getElementById('currentState').innerHTML =
                '<strong>Current Authentication State:</strong><br>' +
                JSON.stringify(state, null, 2);
        }

        function setupScenario1() {
            // Clear all data and set valid JSON
            clearAllData();
            alert('Scenario 1 setup:\n1. Cleared all authentication data\n2. You need to manually copy entraConfig.json.valid-test to entraConfig.json\n3. Then test the pages');
        }

        function setupScenario2() {
            // Simulate having localStorage with valid JSON
            simulateLogin();
            alert('Scenario 2 setup:\n1. Simulated successful login (localStorage populated)\n2. Ensure entraConfig.json has valid values\n3. Then test the pages');
        }

        function setupScenario3() {
            // Have localStorage but switch to invalid JSON
            simulateLogin();
            alert('Scenario 3 setup:\n1. Simulated existing authentication\n2. You need to manually copy entraConfig.json.invalid-test to entraConfig.json\n3. Then test the pages');
        }

        function clearAllData() {
            if (window.SharedAuth) {
                window.SharedAuth.clearAllAuthData();
            } else {
                // Manual clear if SharedAuth not available
                localStorage.clear();
                sessionStorage.clear();
            }
            console.log('All authentication data cleared');
            checkCurrentState();
        }

        function simulateLogin() {
            // Simulate successful login by setting localStorage
            const userProfile = {
                account: 'CORP/TestUser',
                userName: 'Test User',
                email: '<EMAIL>',
                version: '1.0'
            };
            
            localStorage.setItem('UserProfile', JSON.stringify(userProfile));
            localStorage.setItem('msal.username', '<EMAIL>');
            localStorage.setItem('msal.accessToken', 'fake-access-token-for-testing');
            localStorage.setItem('msal.idToken', 'fake-id-token-for-testing');
            
            console.log('Simulated successful login');
            checkCurrentState();
        }

        function switchToValidJSON() {
            alert('Manual action required:\nCopy entraConfig.json.valid-test to entraConfig.json\nThen refresh the page you want to test');
        }

        function switchToInvalidJSON() {
            alert('Manual action required:\nCopy entraConfig.json.invalid-test to entraConfig.json\nThen refresh the page you want to test');
        }

        function testAspenONE() {
            window.open('aspenONE.html', '_blank');
        }

        function testPBItemDetails() {
            window.open('WebControls/PBItemDetails.asp', '_blank');
        }

        function testScenario7() {
            // Test the specific scenario you mentioned
            const details = document.getElementById('scenario7Details');
            const status = document.getElementById('scenario7Status');

            details.style.display = 'block';
            details.innerHTML = '<strong>Testing Scenario 7:</strong><br>';

            // Step 1: Setup valid JSON + localStorage
            simulateLogin();
            details.innerHTML += '1. Simulated login (localStorage populated)<br>';

            // Step 2: Check current state
            if (window.SharedAuth) {
                const beforeState = {
                    jsonValid: window.SharedAuth.isJSONConfigValid(),
                    hasLocalStorage: window.SharedAuth.hasValidLocalStorage(),
                    authFlow: window.SharedAuth.determineAuthFlow()
                };
                details.innerHTML += '2. Before state: ' + JSON.stringify(beforeState) + '<br>';

                // Step 3: Simulate JSON change to invalid
                details.innerHTML += '3. <strong>Now manually copy entraConfig.json.invalid-test to entraConfig.json and click "Force Reload Config"</strong><br>';

                status.className = 'status pending';
                status.textContent = 'Waiting for manual JSON change...';
            } else {
                details.innerHTML += 'ERROR: SharedAuth not available<br>';
                status.className = 'status fail';
                status.textContent = 'Failed - SharedAuth not loaded';
            }
        }

        function forceReloadConfig() {
            if (window.SharedAuth && window.SharedAuth.reloadJSONConfig) {
                const reloaded = window.SharedAuth.reloadJSONConfig();
                console.log('Force reload result:', reloaded);

                // Check the new state
                const afterState = {
                    jsonValid: window.SharedAuth.isJSONConfigValid(),
                    hasLocalStorage: window.SharedAuth.hasValidLocalStorage(),
                    authFlow: window.SharedAuth.determineAuthFlow()
                };

                console.log('After reload state:', afterState);

                // Update scenario 7 test if it's running
                const details = document.getElementById('scenario7Details');
                const status = document.getElementById('scenario7Status');

                if (details.style.display === 'block') {
                    details.innerHTML += '4. After JSON change: ' + JSON.stringify(afterState) + '<br>';

                    if (afterState.authFlow.clearData) {
                        details.innerHTML += '5. ✅ SharedAuth correctly detected invalid JSON and wants to clear data<br>';

                        // Test if localStorage gets cleared
                        const beforeClear = {
                            userProfile: !!localStorage.getItem('UserProfile'),
                            msalUsername: !!localStorage.getItem('msal.username')
                        };

                        window.SharedAuth.clearAllAuthData();

                        const afterClear = {
                            userProfile: !!localStorage.getItem('UserProfile'),
                            msalUsername: !!localStorage.getItem('msal.username')
                        };

                        details.innerHTML += '6. Before clear: ' + JSON.stringify(beforeClear) + '<br>';
                        details.innerHTML += '7. After clear: ' + JSON.stringify(afterClear) + '<br>';

                        if (!afterClear.userProfile && !afterClear.msalUsername) {
                            details.innerHTML += '8. ✅ SUCCESS: localStorage was cleared correctly!<br>';
                            details.innerHTML += '9. Now change JSON back to valid and test Scenario 5...<br>';
                            status.className = 'status pass';
                            status.textContent = 'PASSED - Scenario 7 works correctly';
                        } else {
                            details.innerHTML += '8. ❌ FAILED: localStorage was not cleared<br>';
                            status.className = 'status fail';
                            status.textContent = 'FAILED - localStorage not cleared';
                        }
                    } else if (afterState.authFlow.showOverlay && afterState.authFlow.showLoginPanel) {
                        details.innerHTML += '5. ✅ SharedAuth correctly detected valid JSON + no localStorage (Scenario 5)<br>';
                        details.innerHTML += '6. Expected: showOverlay=true, showLoginPanel=true<br>';
                        details.innerHTML += '7. ✅ SUCCESS: Should show overlay and login panel!<br>';
                        status.className = 'status pass';
                        status.textContent = 'PASSED - Scenario 5 works correctly';
                    } else {
                        details.innerHTML += '5. Current flow: ' + JSON.stringify(afterState.authFlow) + '<br>';
                        details.innerHTML += '6. ❌ Unexpected flow detected<br>';
                        status.className = 'status fail';
                        status.textContent = 'FAILED - Unexpected flow';
                    }
                }

                // Update current state display
                checkCurrentState();
            } else {
                alert('SharedAuth.reloadJSONConfig not available');
            }
        }

        function testAspenONE() {
            window.open('aspenONE.html', '_blank');
        }

        function testPBItemDetails() {
            window.open('WebControls/PBItemDetails.asp', '_blank');
        }

        // Initialize
        window.onload = function() {
            checkCurrentState();
        };
    </script>
</body>
</html>
