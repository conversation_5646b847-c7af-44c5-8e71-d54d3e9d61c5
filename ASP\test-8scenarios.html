<!DOCTYPE html>
<html>
<head>
    <title>8-Scenario Authentication Tester</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .scenario { border: 1px solid #ccc; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .scenario h3 { margin-top: 0; color: #333; }
        .status { padding: 5px 10px; border-radius: 3px; font-weight: bold; }
        .status.pass { background: #d4edda; color: #155724; }
        .status.fail { background: #f8d7da; color: #721c24; }
        .status.pending { background: #fff3cd; color: #856404; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        .info { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; }
        .debug { font-family: monospace; background: #f5f5f5; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>8-Scenario Authentication Tester</h1>
    
    <div class="info">
        <strong>Instructions:</strong>
        <ol>
            <li>Open browser developer tools (F12) to monitor console</li>
            <li>Use the buttons below to test each scenario</li>
            <li>Check the status indicators for results</li>
            <li>Test both aspenONE.html and PBItemDetails.asp</li>
        </ol>
    </div>

    <div class="scenario">
        <h3>Current State</h3>
        <button onclick="checkCurrentState()">Check Current State</button>
        <div id="currentState" class="debug">Click "Check Current State" to see current authentication status</div>
    </div>

    <div class="scenario">
        <h3>Scenario 1: First Visit + Valid JSON</h3>
        <p><strong>Expected:</strong> Show overlay + login panel</p>
        <button onclick="setupScenario1()">Setup Scenario 1</button>
        <button onclick="testAspenONE()">Test aspenONE.html</button>
        <button onclick="testPBItemDetails()">Test PBItemDetails.asp</button>
        <div id="scenario1Status" class="status pending">Not tested</div>
    </div>

    <div class="scenario">
        <h3>Scenario 2: Revisit + Valid JSON</h3>
        <p><strong>Expected:</strong> No overlay, no login panel</p>
        <button onclick="setupScenario2()">Setup Scenario 2</button>
        <button onclick="testAspenONE()">Test aspenONE.html</button>
        <button onclick="testPBItemDetails()">Test PBItemDetails.asp</button>
        <div id="scenario2Status" class="status pending">Not tested</div>
    </div>

    <div class="scenario">
        <h3>Scenario 3: Invalid JSON + Any localStorage</h3>
        <p><strong>Expected:</strong> Clear localStorage, no overlay, no login panel</p>
        <button onclick="setupScenario3()">Setup Scenario 3</button>
        <button onclick="testAspenONE()">Test aspenONE.html</button>
        <button onclick="testPBItemDetails()">Test PBItemDetails.asp</button>
        <div id="scenario3Status" class="status pending">Not tested</div>
    </div>

    <div class="scenario">
        <h3>Quick Actions</h3>
        <button onclick="clearAllData()">Clear All Authentication Data</button>
        <button onclick="simulateLogin()">Simulate Successful Login</button>
        <button onclick="switchToValidJSON()">Switch to Valid JSON</button>
        <button onclick="switchToInvalidJSON()">Switch to Invalid JSON</button>
    </div>

    <script>
        function checkCurrentState() {
            const state = {
                hasSharedAuth: typeof window.SharedAuth !== 'undefined',
                jsonValid: window.SharedAuth ? window.SharedAuth.isJSONConfigValid() : 'SharedAuth not available',
                hasLocalStorage: window.SharedAuth ? window.SharedAuth.hasValidLocalStorage() : 'SharedAuth not available',
                authFlow: window.SharedAuth ? window.SharedAuth.determineAuthFlow() : 'SharedAuth not available',
                userProfile: localStorage.getItem('UserProfile'),
                msalUsername: localStorage.getItem('msal.username'),
                accessToken: !!localStorage.getItem('msal.accessToken')
            };
            
            document.getElementById('currentState').innerHTML = 
                '<strong>Current Authentication State:</strong><br>' +
                JSON.stringify(state, null, 2);
        }

        function setupScenario1() {
            // Clear all data and set valid JSON
            clearAllData();
            alert('Scenario 1 setup:\n1. Cleared all authentication data\n2. You need to manually copy entraConfig.json.valid-test to entraConfig.json\n3. Then test the pages');
        }

        function setupScenario2() {
            // Simulate having localStorage with valid JSON
            simulateLogin();
            alert('Scenario 2 setup:\n1. Simulated successful login (localStorage populated)\n2. Ensure entraConfig.json has valid values\n3. Then test the pages');
        }

        function setupScenario3() {
            // Have localStorage but switch to invalid JSON
            simulateLogin();
            alert('Scenario 3 setup:\n1. Simulated existing authentication\n2. You need to manually copy entraConfig.json.invalid-test to entraConfig.json\n3. Then test the pages');
        }

        function clearAllData() {
            if (window.SharedAuth) {
                window.SharedAuth.clearAllAuthData();
            } else {
                // Manual clear if SharedAuth not available
                localStorage.clear();
                sessionStorage.clear();
            }
            console.log('All authentication data cleared');
            checkCurrentState();
        }

        function simulateLogin() {
            // Simulate successful login by setting localStorage
            const userProfile = {
                account: 'CORP/TestUser',
                userName: 'Test User',
                email: '<EMAIL>',
                version: '1.0'
            };
            
            localStorage.setItem('UserProfile', JSON.stringify(userProfile));
            localStorage.setItem('msal.username', '<EMAIL>');
            localStorage.setItem('msal.accessToken', 'fake-access-token-for-testing');
            localStorage.setItem('msal.idToken', 'fake-id-token-for-testing');
            
            console.log('Simulated successful login');
            checkCurrentState();
        }

        function switchToValidJSON() {
            alert('Manual action required:\nCopy entraConfig.json.valid-test to entraConfig.json\nThen refresh the page you want to test');
        }

        function switchToInvalidJSON() {
            alert('Manual action required:\nCopy entraConfig.json.invalid-test to entraConfig.json\nThen refresh the page you want to test');
        }

        function testAspenONE() {
            window.open('aspenONE.html', '_blank');
        }

        function testPBItemDetails() {
            window.open('WebControls/PBItemDetails.asp', '_blank');
        }

        // Initialize
        window.onload = function() {
            checkCurrentState();
        };
    </script>
</body>
</html>
