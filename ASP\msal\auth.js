/**
 * Security Constants for ProcessExplorer Authentication
 * Centralized constants for URLs, paths, and security configuration
 */
window.SecurityConstants = {
    // URL Paths
    PATHS: {
        LOGIN_PAGE: 'aspenONE.html',
        HOME_PAGE: 'aspenONE.html',
        PROCESS_EXPLORER_BASE: '/ProcessExplorer',
    },

    // Cache Duration (in milliseconds)
    CACHE_DURATION: {
        MSAL_CONFIG: 5 * 60 * 1000,        // 5 minutes
        AUTH_STATUS: 30 * 1000,            // 30 seconds
        REDIRECT_CHECK: 30 * 1000,         // 30 seconds to prevent loops
    },

    // Session Storage Keys
    SESSION_KEYS: {
        ACCESS_TOKEN: 'msal.accessToken',
        ID_TOKEN: 'msal.idToken',
        ACCOUNT: 'msal.account',
        INTERACTION_STATUS: 'msal.interaction.status',
        LAST_AUTH_CHECK: 'authManager.lastCheck',
        PREVENT_AUTO_AUTH: 'preventAutoAuth',
    },

    // Local Storage Keys  
    LOCAL_KEYS: {
        ACCESS_TOKEN: 'msal.accessToken',
        ID_TOKEN: 'msal.idToken',
        ACCOUNT: 'msal.account',
    },

    // MSAL Configuration Validation
    VALIDATION: {
        GUID_REGEX: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
        MICROSOFT_DOMAINS: [
            'login.microsoftonline.com',
            'login.microsoft.com'
        ],
        REQUIRED_SCOPES: ['User.Read'],
    },

    // Authentication Flow States
    AUTH_STATES: {
        AUTHENTICATED: 'authenticated',
        UNAUTHENTICATED: 'unauthenticated',
        IN_PROGRESS: 'in_progress',
        ERROR: 'error',
        UNKNOWN: 'unknown'
    },

    // Redirect Reasons
    REDIRECT_REASONS: {
        ALREADY_ON_LOGIN: 'Already on login page',
        AUTH_IN_PROGRESS: 'Authentication in progress',
        RECENT_CHECK: 'Recent check performed',
        MSAL_NOT_CONFIGURED: 'MSAL not configured',
        USER_AUTHENTICATED: 'User authenticated',
        AUTH_REQUIRED: 'Authentication required',
        ERROR_OCCURRED: 'Error occurred'
    },

    // API Endpoints (if needed for testing)
    API_ENDPOINTS: {
        PUBLIC: '/api/public',
        PROTECTED: '/api/protected',
        USER_INFO: '/api/user'
    },

    // Helper functions
    helpers: {
        /**
         * Get the login URL with return URL parameter
         */
        getLoginUrl(returnUrl) {
            const baseUrl = `${window.location.origin}${window.location.pathname.replace(/[^/]*$/, '')}`;
            const encodedReturnUrl = encodeURIComponent(returnUrl || window.location.href);
            return `${baseUrl}${SecurityConstants.PATHS.LOGIN_PAGE}?returnUrl=${encodedReturnUrl}`;
        },

        /**
         * Get the home page URL
         */
        getHomeUrl() {
            const baseUrl = `${window.location.origin}${window.location.pathname.replace(/[^/]*$/, '')}`;
            return `${baseUrl}${SecurityConstants.PATHS.HOME_PAGE}`;
        },

        /**
         * Check if current page is the login page
         */
        isLoginPage(url) {
            const targetUrl = url || window.location.pathname;
            return targetUrl.includes(SecurityConstants.PATHS.LOGIN_PAGE);
        },

        /**
         * Check if current page is the home page
         */
        isHomePage(url) {
            const targetUrl = url || window.location.pathname;
            return targetUrl.includes(SecurityConstants.PATHS.HOME_PAGE);
        },

        /**
         * Get return URL from query parameters
         */
        getReturnUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('returnUrl');
        },

        /**
         * Remove query parameters and authentication-related hashes from current URL
         * Preserves application route hashes like #/ProcessExplorer/plots
         */
        cleanUrl() {
             let newUrl = `${window.location.protocol}//${window.location.host}${window.location.pathname}`;
            
            // If there's a hash, check if it's an application route that should be preserved
            if (window.location.hash) {
                const hash = window.location.hash;
                // Only remove hash if it contains MSAL authentication fragments
                const isAuthHash = hash.indexOf('id_token') !== -1 || 
                                  hash.indexOf('code=') !== -1 || 
                                  hash.indexOf('error=') !== -1 ||
                                  hash.indexOf('access_token') !== -1;
                
                if (!isAuthHash) {
                    // Preserve application route hashes (including /landing)
                    newUrl += hash;
                }
            }
            
            if (window.history && window.history.replaceState) {
                window.history.replaceState({}, document.title, newUrl);
            }
        },

        /**
         * Check if a string is a valid GUID
         */
        isValidGuid(guid) {
            if (!guid) return false;
            return SecurityConstants.VALIDATION.GUID_REGEX.test(guid);
        },

        /**
         * Check if URL is a valid Microsoft authority
         */
        isValidMicrosoftAuthority(url) {
            try {
                const urlObj = new URL(url);
                return SecurityConstants.VALIDATION.MICROSOFT_DOMAINS.some(domain => 
                    urlObj.hostname.includes(domain)
                );
            } catch (e) {
                return false;
            }
        }
    }
};

/**
 * ConfigChecker - Utility for checking MSAL configuration
 * Can be used independently to validate if MSAL is properly configured
 */
window.ConfigChecker = {
    
    /**
     * Check if MSAL configuration exists and is valid
     * @returns {Promise<object>} Result object with isConfigured boolean and details
     */
    async checkConfiguration() {
        const result = {
            isConfigured: false,
            details: {
                msalLibraryLoaded: false,
                configObjectExists: false,
                clientIdValid: false,
                authorityValid: false,
                redirectUriValid: false,
                errors: []
            }
        };

        try {
            // Check if MSAL library is loaded
            if (typeof msal === 'undefined') {
                result.details.errors.push('MSAL library not loaded');
            } else {
                result.details.msalLibraryLoaded = true;
            }

            // Check if msalConfig object exists
            if (typeof msalConfig === 'undefined') {
                result.details.errors.push('msalConfig object not found');
            } else {
                result.details.configObjectExists = true;

                // Validate auth configuration
                if (!msalConfig.auth) {
                    result.details.errors.push('msalConfig.auth not defined');
                } else {
                    // Check clientId
                    if (!msalConfig.auth.clientId) {
                        result.details.errors.push('clientId not defined');
                    } else if (!SecurityConstants.helpers.isValidGuid(msalConfig.auth.clientId)) {
                        result.details.errors.push('clientId is not a valid GUID format');
                    } else {
                        result.details.clientIdValid = true;
                    }

                    // Check authority
                    if (!msalConfig.auth.authority) {
                        result.details.errors.push('authority not defined');
                    } else if (!SecurityConstants.helpers.isValidMicrosoftAuthority(msalConfig.auth.authority)) {
                        result.details.errors.push('authority is not a valid Microsoft login endpoint');
                    } else {
                        result.details.authorityValid = true;
                    }

                    // Check redirectUri (optional but recommended)
                    if (msalConfig.auth.redirectUri) {
                        try {
                            new URL(msalConfig.auth.redirectUri);
                            result.details.redirectUriValid = true;
                        } catch (e) {
                            result.details.errors.push('redirectUri is not a valid URL');
                        }
                    } else {
                        result.details.redirectUriValid = true; // Optional field
                    }
                }
            }

            // Determine if fully configured
            result.isConfigured = (
                result.details.msalLibraryLoaded &&
                result.details.configObjectExists &&
                result.details.clientIdValid &&
                result.details.authorityValid &&
                result.details.redirectUriValid &&
                result.details.errors.length === 0
            );

            return result;

        } catch (error) {
            result.details.errors.push(`Unexpected error: ${error.message}`);
            return result;
        }
    },

    /**
     * Get a summary of the configuration status
     * @returns {Promise<string>} Human-readable status message
     */
    async getConfigurationSummary() {
        const result = await this.checkConfiguration();
        
        if (result.isConfigured) {
            return 'MSAL is properly configured and ready for authentication';
        } else {
            const errorCount = result.details.errors.length;
            const errorList = result.details.errors.join(', ');
            return `MSAL configuration has ${errorCount} issue(s): ${errorList}`;
        }
    },

    /**
     * Log detailed configuration status to console
     */
    async logConfigurationStatus() {
        const result = await this.checkConfiguration();
        
        console.group('MSAL Configuration Check');
        console.log('Overall Status:', result.isConfigured ? '[OK] CONFIGURED' : '[ERROR] NOT CONFIGURED');
        
        console.group('Details:');
        console.log('MSAL Library Loaded:', result.details.msalLibraryLoaded ? '[OK]' : '[ERROR]');
        console.log('Config Object Exists:', result.details.configObjectExists ? '[OK]' : '[ERROR]');
        console.log('ClientId Valid:', result.details.clientIdValid ? '[OK]' : '[ERROR]');
        console.log('Authority Valid:', result.details.authorityValid ? '[OK]' : '[ERROR]');
        console.log('RedirectUri Valid:', result.details.redirectUriValid ? '[OK]' : '[ERROR]');
        console.groupEnd();

        if (result.details.errors.length > 0) {
            console.group('Errors:');
            result.details.errors.forEach(error => console.log('[ERROR]', error));
            console.groupEnd();
        }

        console.groupEnd();
        
        return result;
    },

    /**
     * Quick boolean check if MSAL is configured
     * @returns {Promise<boolean>} True if configured, false otherwise
     */
    async isConfigured() {
        const result = await this.checkConfiguration();
        return result.isConfigured;
    },

    /**
     * Validate specific configuration values
     * @param {object} config - Configuration object to validate
     * @returns {object} Validation result
     */
    validateConfig(config) {
        const validation = {
            isValid: false,
            errors: []
        };

        if (!config) {
            validation.errors.push('Configuration object is null or undefined');
            return validation;
        }

        if (!config.auth) {
            validation.errors.push('auth section missing');
            return validation;
        }

        // Validate clientId
        if (!config.auth.clientId) {
            validation.errors.push('clientId is required');
        } else if (!SecurityConstants.helpers.isValidGuid(config.auth.clientId)) {
            validation.errors.push('clientId must be a valid GUID');
        }

        // Validate authority
        if (!config.auth.authority) {
            validation.errors.push('authority is required');
        } else if (!SecurityConstants.helpers.isValidMicrosoftAuthority(config.auth.authority)) {
            validation.errors.push('authority must be a valid Microsoft endpoint');
        }

        // Validate redirectUri if present
        if (config.auth.redirectUri) {
            try {
                new URL(config.auth.redirectUri);
            } catch (e) {
                validation.errors.push('redirectUri must be a valid URL');
            }
        }

        // Check cache configuration if present
        if (config.cache) {
            if (config.cache.cacheLocation && !['localStorage', 'sessionStorage'].includes(config.cache.cacheLocation)) {
                validation.errors.push('cacheLocation must be either "localStorage" or "sessionStorage"');
            }
        }

        validation.isValid = validation.errors.length === 0;
        return validation;
    },

    /**
     * Get current configuration object (if available)
     * @returns {object|null} Current MSAL configuration or null
     */
    getCurrentConfig() {
        if (typeof msalConfig !== 'undefined') {
            return msalConfig;
        }
        return null;
    },

    /**
     * Check if configuration file (authConfig.js) is loaded
     * @returns {boolean} True if loaded, false otherwise
     */
    isConfigFileLoaded() {
        return typeof msalConfig !== 'undefined';
    },

    /**
     * Wait for configuration to be loaded (with timeout)
     * @param {number} timeoutMs - Timeout in milliseconds (default: 5000)
     * @returns {Promise<boolean>} True if loaded within timeout, false otherwise
     */
    async waitForConfig(timeoutMs = 5000) {
        const startTime = Date.now();
        
        return new Promise((resolve) => {
            const checkConfig = () => {
                if (this.isConfigFileLoaded()) {
                    resolve(true);
                } else if (Date.now() - startTime >= timeoutMs) {
                    resolve(false);
                } else {
                    setTimeout(checkConfig, 100);
                }
            };
            
            checkConfig();
        });
    }
};

/**
 * AuthManager - Centralized authentication logic for ProcessExplorer
 * Can be used by any page to handle MSAL authentication consistently
 */
class AuthManager {
    constructor() {
        this.cache = {
            msalConfigured: null,
            msalConfiguredExpiry: 0,
            authStatus: null,
            authStatusExpiry: 0,
            redirectPrevention: new Map(), // Track recent redirects to prevent loops
            performanceMetrics: {
                authChecks: 0,
                redirectsPrevented: 0,
                cacheHits: 0
            }
        };
        this.msalInstance = null;
        this.isInitialized = false;
        this.redirectCooldown = 5000; // 5 seconds between redirects from same page
        this.maxRedirectsPerSession = 10; // Maximum redirects per session
        this.sessionRedirectCount = parseInt(sessionStorage.getItem('authRedirectCount') || '0', 10);
    }

    /**
     * Initialize the AuthManager and MSAL instance
     */
    async initialize() {
        if (this.isInitialized) return;
        
        try {
            // Check if MSAL config is available
            if (typeof msalConfig !== 'undefined' && typeof msal !== 'undefined') {
                this.msalInstance = new msal.PublicClientApplication(msalConfig);
                await this.msalInstance.initialize();
                console.log('AuthManager: MSAL instance initialized');
            }
            this.isInitialized = true;
        } catch (error) {
            console.error('AuthManager: Failed to initialize MSAL:', error);
        }
    }

    /**
     * Check if MSAL is properly configured by validating entraConfig.json
     * Uses caching to avoid repeated checks
     */
    async checkMSALConfiguration() {
        const now = Date.now();
        
        // Return cached result if still valid (5 minutes)
        if (this.cache.msalConfigured !== null && now < this.cache.msalConfiguredExpiry) {
            this.cache.performanceMetrics.cacheHits++;
            return this.cache.msalConfigured;
        }

        try {
            // Check if MSAL config objects exist
            if (typeof msalConfig === 'undefined' || typeof msal === 'undefined') {
                console.log('AuthManager: MSAL libraries not loaded');
                this.cache.msalConfigured = false;
                this.cache.msalConfiguredExpiry = now + (5 * 60 * 1000); // 5 minutes
                return false;
            }

            // Validate MSAL configuration
            if (!msalConfig.auth || !msalConfig.auth.clientId || !msalConfig.auth.authority) {
                console.log('AuthManager: MSAL configuration incomplete');
                this.cache.msalConfigured = false;
                this.cache.msalConfiguredExpiry = now + (5 * 60 * 1000);
                return false;
            }

            // Check for placeholder clientId values
            if (msalConfig.auth.clientId === "Enter_the_Application_Id_Here" || 
                msalConfig.auth.clientId.includes("Enter_the_Application_Id")) {
                console.log('AuthManager: clientId contains placeholder value - configuration not complete');
                this.cache.msalConfigured = false;
                this.cache.msalConfiguredExpiry = now + (5 * 60 * 1000);
                return false;
            }

            // Validate clientId format (should be a GUID)
            const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            if (!guidRegex.test(msalConfig.auth.clientId)) {
                console.log('AuthManager: Invalid clientId format - must be a valid GUID');
                this.cache.msalConfigured = false;
                this.cache.msalConfiguredExpiry = now + (5 * 60 * 1000);
                return false;
            }

            // Validate authority URL
            try {
                const authorityUrl = new URL(msalConfig.auth.authority);
                
                // Check for Microsoft domains
                if (!authorityUrl.hostname.includes('login.microsoftonline.com') && 
                    !authorityUrl.hostname.includes('login.microsoft.com')) {
                    console.log('AuthManager: Invalid authority URL - must use Microsoft login domain');
                    this.cache.msalConfigured = false;
                    this.cache.msalConfiguredExpiry = now + (5 * 60 * 1000);
                    return false;
                }

                // Extract tenant ID from authority URL path
                const pathSegments = authorityUrl.pathname.split('/').filter(segment => segment.length > 0);
                if (pathSegments.length > 0) {
                    const tenantId = pathSegments[0];
                    
                    // Check for placeholder tenant ID
                    if (tenantId === "Enter_the_Tenant_ID_Here" || tenantId.includes("Enter_the_Tenant_ID")) {
                        console.log('AuthManager: authority contains placeholder tenant ID - configuration not complete');
                        this.cache.msalConfigured = false;
                        this.cache.msalConfiguredExpiry = now + (5 * 60 * 1000);
                        return false;
                    }
                    
                    // Check if tenant ID is a valid GUID (unless it's a special tenant like 'common', 'organizations', 'consumers')
                    const specialTenants = ['common', 'organizations', 'consumers'];
                    if (!specialTenants.includes(tenantId.toLowerCase()) && !guidRegex.test(tenantId)) {
                        console.log('AuthManager: Invalid tenant ID format in authority URL - must be a valid GUID or special tenant');
                        this.cache.msalConfigured = false;
                        this.cache.msalConfiguredExpiry = now + (5 * 60 * 1000);
                        return false;
                    }
                }
            } catch (e) {
                console.log('AuthManager: Malformed authority URL');
                this.cache.msalConfigured = false;
                this.cache.msalConfiguredExpiry = now + (5 * 60 * 1000);
                return false;
            }

            console.log('AuthManager: MSAL properly configured');
            this.cache.msalConfigured = true;
            this.cache.msalConfiguredExpiry = now + (5 * 60 * 1000);
            return true;

        } catch (error) {
            console.error('AuthManager: Error checking MSAL configuration:', error);
            this.cache.msalConfigured = false;
            this.cache.msalConfiguredExpiry = now + (5 * 60 * 1000);
            return false;
        }
    }

    /**
     * Check if user is currently authenticated with valid tokens
     * Uses caching to avoid repeated checks
     * @returns {Promise<{isAuthenticated: boolean, status: string}>}
     */
    async isUserAuthenticated() {
        const now = Date.now();
        this.cache.performanceMetrics.authChecks++;
        
        // Return cached result if still valid (30 seconds)
        if (this.cache.authStatus !== null && now < this.cache.authStatusExpiry) {
            this.cache.performanceMetrics.cacheHits++;
            return this.cache.authStatus;
        }

        try {
            await this.initialize();

            let result = { isAuthenticated: false, status: 'unauthenticated' };
            let accessToken = null;
            let idToken = null;

            // Check if we are in test mode to bypass silent token acquisition
            const isTestMode = window.TokenExpirationTester && window.TokenExpirationTester.testMode;

            // First, try to get active account from MSAL
            if (this.msalInstance && !isTestMode) {
                const currentAccount = this.msalInstance.getActiveAccount();
                if (currentAccount) {
                    try {
                        // Try to get tokens silently
                        const silentRequest = {
                            account: currentAccount,
                            scopes: ['User.Read']
                        };
                        
                        const response = await this.msalInstance.acquireTokenSilent(silentRequest);
                        if (response && response.accessToken) {
                            accessToken = response.accessToken;
                            idToken = response.idToken;
                            result = { isAuthenticated: true, status: 'authenticated' };
                            
                            // Update storage
                            sessionStorage.setItem('msal.accessToken', response.accessToken);
                            sessionStorage.setItem('msal.idToken', response.idToken);
                            localStorage.setItem('msal.accessToken', response.accessToken);
                            localStorage.setItem('msal.idToken', response.idToken);
                        }
                    } catch (tokenError) {
                        console.warn('AuthManager: Could not acquire token silently:', tokenError);
                    }
                }
            }

            // Fallback: check tokens in storage
            if (!result.isAuthenticated) {
                accessToken = sessionStorage.getItem('msal.accessToken') || localStorage.getItem('msal.accessToken');
                idToken = sessionStorage.getItem('msal.idToken') || localStorage.getItem('msal.idToken');
                
                if (accessToken && idToken) {
                    // Validate token expiration
                    try {
                        const tokenParts = accessToken.split('.');
                        if (tokenParts.length === 3) {
                            const payload = JSON.parse(atob(tokenParts[1]));
                            const currentTime = Math.floor(Date.now() / 1000);
                            
                            if (currentTime < payload.exp) {
                                result = { isAuthenticated: true, status: 'authenticated' };
                            } else {
                                console.log('AuthManager: Token expired');
                                result = { isAuthenticated: false, status: 'expired' };
                                // Do not clear tokens here, let the caller decide
                            }
                        }
                    } catch (e) {
                        console.warn('AuthManager: Error validating token:', e);
                        result = { isAuthenticated: false, status: 'error' };
                        this.clearTokens();
                    }
                }
            }

            console.log('AuthManager: Authentication status object:', result);
            this.cache.authStatus = result;
            this.cache.authStatusExpiry = now + (30 * 1000); // 30 seconds
            return result;

        } catch (error) {
            console.error('AuthManager: Error checking authentication:', error);
            const result = { isAuthenticated: false, status: 'error' };
            this.cache.authStatus = result;
            this.cache.authStatusExpiry = now + (30 * 1000);
            return result;
        }
    }

    /**
     * Main method to determine if authentication is required for current URL
     * Returns object with redirect information if needed
     */
    async requiresAuthentication(currentUrl) {
        try {
            // Avoid redirecting from login page itself
            if (currentUrl.includes('login.html')) {
                return { requiresRedirect: false, reason: 'Already on login page' };
            }

            // Check if we're in the middle of an authentication flow
            if (sessionStorage.getItem('msal.interaction.status') === 'in_progress') {
                return { requiresRedirect: false, reason: 'Authentication in progress' };
            }

            // Check if we recently did this check (avoid loops)
            const lastCheck = sessionStorage.getItem('authManager.lastCheck');
            const now = Date.now();
            if (lastCheck && (now - parseInt(lastCheck)) < 30000) { // 30 seconds
                return { requiresRedirect: false, reason: 'Recent check performed' };
            }

            // Mark that we're doing a check
            sessionStorage.setItem('authManager.lastCheck', now.toString());

            // First check if MSAL is configured
            const msalConfigured = await this.checkMSALConfiguration();
            if (!msalConfigured) {
                console.log('AuthManager: MSAL not configured, allowing access without authentication');
                return { requiresRedirect: false, reason: 'MSAL not configured' };
            }

            // MSAL is configured, check authentication
            const authState = await this.isUserAuthenticated();
            if (authState.isAuthenticated) {
                console.log('AuthManager: User authenticated, allowing access');
                return { requiresRedirect: false, reason: 'User authenticated' };
            }

            // User not authenticated but MSAL is configured
            // If we're already on aspenONE.html, show overlay instead of redirecting
            if (currentUrl.includes('aspenONE.html')) {
                console.log('AuthManager: Authentication required on aspenONE.html, will show overlay');
                return { 
                    requiresRedirect: true, 
                    redirectUrl: null, // No redirect needed, use overlay
                    reason: 'Authentication required - show overlay'
                };
            }
            
            // Redirect to aspenONE.html (which has integrated auth overlay)
            const encodedReturnUrl = encodeURIComponent(currentUrl);
            const loginUrl = `${window.location.origin}${window.location.pathname.replace(/[^/]*$/, 'aspenONE.html')}?returnUrl=${encodedReturnUrl}`;
            
            console.log('AuthManager: Authentication required, redirecting to aspenONE.html');
            return { 
                requiresRedirect: true, 
                redirectUrl: loginUrl,
                reason: 'Authentication required'
            };

        } catch (error) {
            console.error('AuthManager: Error in requiresAuthentication:', error);
            return { requiresRedirect: false, reason: 'Error occurred' };
        }
    }

    /**
     * Smart redirect prevention logic with token expiration considerations
     */
    canRedirect(currentUrl, context = 'default') {
        const now = Date.now();
        const pageKey = window.location.pathname;
        
        // For token expiration scenarios, be more lenient with redirect prevention
        if (context === 'token_expiration') {
            console.log('AuthManager: Token expiration context - allowing redirect with relaxed limits');
            
            // Still check for excessive redirects, but with higher limits
            if (this.sessionRedirectCount >= (this.maxRedirectsPerSession * 2)) {
                console.warn('AuthManager: Excessive redirects even for token expiration, preventing redirect');
                this.cache.performanceMetrics.redirectsPrevented++;
                return false;
            }
            
            // Shorter cooldown for token expiration (1 second instead of 5)
            const lastRedirect = this.cache.redirectPrevention.get(pageKey);
            if (lastRedirect && (now - lastRedirect) < 1000) {
                console.warn('AuthManager: Very recent redirect for token expiration, preventing redirect');
                this.cache.performanceMetrics.redirectsPrevented++;
                return false;
            }
            
            return true;
        }
        
        // Normal redirect prevention logic for other scenarios
        // Check session redirect count limit
        if (this.sessionRedirectCount >= this.maxRedirectsPerSession) {
            console.warn('AuthManager: Maximum redirects per session reached, preventing redirect');
            this.cache.performanceMetrics.redirectsPrevented++;
            return false;
        }
        
        // Check if we recently redirected from this page
        const lastRedirect = this.cache.redirectPrevention.get(pageKey);
        if (lastRedirect && (now - lastRedirect) < this.redirectCooldown) {
            console.warn('AuthManager: Redirect cooldown active for this page, preventing redirect');
            this.cache.performanceMetrics.redirectsPrevented++;
            return false;
        }
        
        // Check for redirect loops (multiple redirects to same URL)
        const recentRedirects = JSON.parse(sessionStorage.getItem('authRecentRedirects') || '[]');
        const duplicateRedirects = recentRedirects.filter(r => r.url === currentUrl).length;
        if (duplicateRedirects >= 3) {
            console.warn('AuthManager: Potential redirect loop detected, preventing redirect');
            this.cache.performanceMetrics.redirectsPrevented++;
            return false;
        }
        
        return true;
    }

    /**
     * Track redirect for prevention logic
     */
    trackRedirect(currentUrl) {
        const now = Date.now();
        const pageKey = window.location.pathname;
        
        // Update redirect tracking
        this.cache.redirectPrevention.set(pageKey, now);
        this.sessionRedirectCount++;
        sessionStorage.setItem('authRedirectCount', this.sessionRedirectCount.toString());
        
        // Track recent redirects for loop detection
        const recentRedirects = JSON.parse(sessionStorage.getItem('authRecentRedirects') || '[]');
        recentRedirects.push({ url: currentUrl, timestamp: now });
        
        // Keep only last 10 redirects and recent ones (last 5 minutes)
        const fiveMinutesAgo = now - (5 * 60 * 1000);
        const filteredRedirects = recentRedirects
            .filter(r => r.timestamp > fiveMinutesAgo)
            .slice(-10);
        
        sessionStorage.setItem('authRecentRedirects', JSON.stringify(filteredRedirects));
    }

    /**
     * Show auth overlay instead of redirecting to login page
     */
    redirectToLogin(returnUrl, context = 'default') {
        const targetUrl = returnUrl || window.location.href;
        
        // Check if redirect is allowed with context awareness
        if (!this.canRedirect(targetUrl, context)) {
            console.warn('AuthManager: Redirect prevented by smart prevention logic');
            return false;
        }
        
        // Track this redirect
        this.trackRedirect(targetUrl);
        
        // Instead of redirecting, show auth overlay if we're on aspenONE.html
        if (window.location.pathname.includes('aspenONE.html') || window.location.pathname.endsWith('/ProcessExplorer/')) {
            console.log('AuthManager: Showing auth overlay instead of redirecting');
            
            // Check if overlay functions are available
            if (typeof window.showAuthOverlay === 'function') {
                window.showAuthOverlay();
                return true;
            } else {
                console.warn('AuthManager: Auth overlay functions not available, falling back to redirect');
            }
        }
        
        // Fallback: redirect to aspenONE.html (which has the integrated auth)
        const encodedReturnUrl = encodeURIComponent(targetUrl);
        const loginUrl = `${window.location.origin}${window.location.pathname.replace(/[^/]*$/, 'aspenONE.html')}?returnUrl=${encodedReturnUrl}`;
        console.log('AuthManager: Redirecting to aspenONE.html with auth overlay:', loginUrl);
        window.location.href = loginUrl;
        return true;
    }

    /**
     * Handle authentication result and redirect back to original destination
     */
    handleAuthenticationResult(returnUrl) {
        if (returnUrl) {
            try {
                const decodedUrl = decodeURIComponent(returnUrl);
                console.log('AuthManager: Processing return URL:', decodedUrl);
                
                // Check if the return URL is just the ProcessExplorer directory without a file
                if (decodedUrl.endsWith('/ProcessExplorer') || decodedUrl.endsWith('/ProcessExplorer/')) {
                    // Redirect to aspenONE.html instead
                    // const fixedUrl = decodedUrl.replace(/\/?$/, '/aspenONE.html#/landing');
                    const fixedUrl = decodedUrl.replace(/\/?$/, '/aspenONE.html');
                    console.log('AuthManager: Fixed directory URL to include file:', fixedUrl);
                    window.location.href = fixedUrl;
                    return;
                }
                
                // For other URLs, redirect as-is
                console.log('AuthManager: Redirecting back to:', decodedUrl);
                window.location.href = decodedUrl;
                return;
            } catch (e) {
                console.warn('AuthManager: Invalid return URL:', e);
            }
        }
        
        // Default fallback
        const defaultUrl = `${window.location.origin}${window.location.pathname.replace(/[^/]*$/, 'aspenONE.html')}#/landing`;
        console.log('AuthManager: Redirecting to default:', defaultUrl);
        window.location.href = defaultUrl;
    }

    /**
     * Clear all authentication tokens and data
     */
    clearTokens() {
        sessionStorage.removeItem('msal.accessToken');
        sessionStorage.removeItem('msal.idToken');
        sessionStorage.removeItem('msal.account');
        localStorage.removeItem('msal.accessToken');
        localStorage.removeItem('msal.idToken');
        localStorage.removeItem('msal.account');
        
        if (this.msalInstance) {
            this.msalInstance.setActiveAccount(null);
        }
        
        // Clear auth status cache
        this.cache.authStatus = null;
        this.cache.authStatusExpiry = 0;
        
        console.log('AuthManager: Tokens cleared');
    }

    /**
     * Perform logout
     */
    async logout() {
        try {
            this.clearTokens();
            
            // Clear redirect prevention data
            this.cache.redirectPrevention.clear();
            sessionStorage.removeItem('authRedirectCount');
            sessionStorage.removeItem('authRecentRedirects');
            sessionStorage.removeItem('authManager.lastCheck');
            
            if (this.msalInstance) {
                await this.msalInstance.initialize();
                const accounts = this.msalInstance.getAllAccounts();
                
                if (accounts.length > 0) {
                    // Use logout redirect to Microsoft
                    await this.msalInstance.logoutRedirect({
                        account: accounts[0]
                    });
                } else {
                    // No accounts, just reload the page
                    window.location.reload();
                }
            } else {
                window.location.reload();
            }
        } catch (error) {
            console.error('AuthManager: Logout error:', error);
            window.location.reload();
        }
    }

    /**
     * Get performance metrics for monitoring and debugging
     */
    getPerformanceMetrics() {
        const cacheHitRate = this.cache.performanceMetrics.authChecks > 0 
            ? (this.cache.performanceMetrics.cacheHits / this.cache.performanceMetrics.authChecks * 100).toFixed(2)
            : 0;
            
        return {
            ...this.cache.performanceMetrics,
            sessionRedirectCount: this.sessionRedirectCount,
            cacheHitRate: `${cacheHitRate}%`,
            activePrevention: this.cache.redirectPrevention.size,
            msalConfigured: this.cache.msalConfigured,
            lastAuthCheck: new Date(this.cache.authStatusExpiry - 30000).toISOString()
        };
    }

    /**
     * Log performance summary for debugging
     */
    logPerformanceSummary() {
        const metrics = this.getPerformanceMetrics();
        console.log('AuthManager Performance Summary:', metrics);
        
        if (metrics.redirectsPrevented > 0) {
            console.log(`[OK] Prevented ${metrics.redirectsPrevented} potential redirect loops`);
        }
        
        if (parseFloat(metrics.cacheHitRate) > 50) {
            console.log(`[PERF] Cache efficiency: ${metrics.cacheHitRate} hit rate`);
        }
    }

    /**
     * Reset performance metrics (useful for testing)
     */
    resetMetrics() {
        this.cache.performanceMetrics = {
            authChecks: 0,
            redirectsPrevented: 0,
            cacheHits: 0
        };
        this.cache.redirectPrevention.clear();
        this.sessionRedirectCount = 0;
        sessionStorage.removeItem('authRedirectCount');
        sessionStorage.removeItem('authRecentRedirects');
        console.log('AuthManager: Performance metrics reset');
    }
}

// Create global instance
window.AuthManager = new AuthManager();

console.log('SecurityConstants: Loaded and ready');
console.log('ConfigChecker: Loaded and ready');
console.log('AuthManager: Loaded and ready');

// Use existing MSAL instance if available, or create a new one.
if (typeof window.myMSALObj === 'undefined') {
    console.log('Creating MSAL instance in auth.js');
    window.myMSALObj = new msal.PublicClientApplication(msalConfig);
} else {
    console.log('Using existing MSAL instance from page');
}

// Flag to track if authentication is in progress
window.authInProgress = false;

// Handle the authentication response
function handleResponse(resp) {
    // Reset auth in progress flag
    window.authInProgress = false;
    
    // Debug: Log current URL to help diagnose redirect issues
    console.log("Current URL in handleResponse:", window.location.href);
    
    // Check for MSAL authentication fragments that might cause loops
    if (window.location.hash) {
        const hash = window.location.hash;
        const hasAuthFragments = hash.indexOf('id_token') !== -1 || 
                                hash.indexOf('code=') !== -1 || 
                                hash.indexOf('error=') !== -1 ||
                                hash.indexOf('access_token') !== -1;
        
        if (hasAuthFragments) {
            console.log("Detected MSAL auth fragments, cleaning URL to prevent refresh loop");
            if (window.history && window.history.replaceState) {
                // Remove auth fragments but preserve application routes
                const cleanUrl = window.location.pathname + window.location.search;
                window.history.replaceState(null, document.title, cleanUrl);
            }
            // Set a flag in session storage to indicate we've just handled a redirect
            sessionStorage.setItem("justAuthenticated", "true");
        }
    }
    
    if (resp !== null) {
        // Successfully signed in
        const account = resp.account;
        // Set active account
        myMSALObj.setActiveAccount(account);
        
        // Update user profile
        window.UserProfile = {
            account: account.username,
            userName: account.name,
            email: account.username
        };
        
        // Update UI if needed
        if ($("#ubar_userName_span").length) {
            $("#ubar_userName_span").text(account.name);
        }
        
        // Store token for API calls
        sessionStorage.setItem("msal.idToken", resp.idToken);
        sessionStorage.setItem("msal.accessToken", resp.accessToken);
        localStorage.setItem("msal.idToken", resp.idToken);
        localStorage.setItem("msal.accessToken", resp.accessToken);        
        console.log("Authentication successful");
        
        // Notify React app about authentication completion
        try {
            // Create user profile for notification
            var userProfile = {
                account: account.username,
                userName: account.name,
                email: account.username,
                version: '1.0'
            };
            
            // Send message to React app (if it exists in a child frame/window)
            if (window.frames && window.frames.length > 0) {
                for (var i = 0; i < window.frames.length; i++) {
                    try {
                        window.frames[i].postMessage({
                            type: 'MSAL_LOGIN_SUCCESS',
                            userProfile: userProfile,
                            timestamp: new Date().toISOString()
                        }, '*');
                    } catch (e) {
                        // Ignore cross-origin errors
                    }
                }
            }
            
            // Also try to send to any child windows that might contain React app
            if (window.document && window.document.getElementById('root')) {
                // React app is in the same window, trigger custom event
                var event = new CustomEvent('authenticationComplete', {
                    detail: {
                        type: 'MSAL_LOGIN_SUCCESS',
                        userProfile: userProfile,
                        timestamp: new Date().toISOString()
                    }
                });
                window.dispatchEvent(event);
                console.log('auth.js: Sent MSAL login success event to React app');
            }
        } catch (e) {
            console.warn('auth.js: Could not notify React app about authentication:', e);
        }
        
        // Validate token and create session
        tokenValidator.validateTokenAndCreateSession()
            .then(valid => {
                console.log("Token validation result:", valid ? "Valid" : "Invalid");
                if (!valid) {
                    console.warn("Token validation failed, user may need to re-authenticate");
                }
                
                // Check if we need to redirect back to the example page
                const returnToExamplePage = sessionStorage.getItem('returnToExamplePage');
                if (returnToExamplePage) {
                    console.log("Redirecting back to example page:", returnToExamplePage);
                    // Remove the return URL from session storage
                    sessionStorage.removeItem('returnToExamplePage');
                    // Redirect to the example page
                    window.location.href = returnToExamplePage;
                    return; // Exit early to prevent further processing
                }
            })
            .catch(error => {
                console.error("Error during token validation:", error);
            });
        
        // Set a flag in session storage to indicate we've just authenticated
        sessionStorage.setItem("justAuthenticated", "true");
    } else {
        // Check if user is already signed in
        const currentAccounts = myMSALObj.getAllAccounts();
        if (currentAccounts && currentAccounts.length > 0) {
            const account = currentAccounts[0];
            // Set active account
            myMSALObj.setActiveAccount(account);
            
            window.UserProfile = {
                account: account.username,
                userName: account.name,
                email: account.username
            };
            
            if ($("#ubar_userName_span").length) {
                $("#ubar_userName_span").text(account.name);
            }
            console.log("User already signed in");
        } else {
            // Not signed in - initiate sign in if not already in progress and not just authenticated
            const justAuthenticated = sessionStorage.getItem("justAuthenticated") === "true";
            
            if (!window.authInProgress && !justAuthenticated) {
                console.log("No signed-in user found, initiating sign-in");
                signIn();
            } else if (justAuthenticated) {
                console.log("Just authenticated, skipping automatic sign-in to prevent loops");
                // Clear the flag after using it
                sessionStorage.removeItem("justAuthenticated");
            }
        }
    }
}

// Sign in function
async function signIn() {
    // Set flag to prevent multiple sign-in attempts
    if (window.authInProgress) {
        console.log("Authentication already in progress, skipping new request");
        return;
    }
    
    // Check MSAL configuration validity before attempting sign-in
    try {
        const configValid = await window.AuthManager.checkMSALConfiguration();
        if (!configValid) {
            console.error("MSAL Configuration Error: Authentication cannot proceed due to invalid configuration.");
            console.error("\nPlease check your entraConfig.json file and ensure the following:");
            console.error("   * clientId: Must be a valid UUID (e.g., '123e4567-e89b-12d3-a456-************')");
            console.error("   * authority: Must be a valid Microsoft login URL with a valid tenant ID");
            console.error("\nCommon issues found:");
            console.error("   * Placeholder values detected (e.g., 'Enter_the_Application_Id_Here' or 'Enter_the_Tenant_ID_Here')");
            console.error("   * Invalid UUID format in clientId or tenant ID");
            console.error("   * Malformed authority URL");
            console.error("\nYou can find these values in your Azure AD app registration:");
            console.error("   - Application (client) ID: Use as clientId");
            console.error("   - Directory (tenant) ID: Use in authority URL");
            console.error("   - Authority format: 'https://login.microsoftonline.com/your-tenant-id'");
            return; // Exit early to prevent redirect to login page
        }
        console.log("MSAL configuration validated successfully");
    } catch (error) {
        console.error("Error validating MSAL configuration:", error);
        console.error("\nConfiguration validation failed. Please verify the following in entraConfig.json:");
        console.error("   [ ] clientId: Must be a valid UUID (36 characters with hyphens)");
        console.error("   [ ] authority: Must be a valid Microsoft login URL with tenant ID");
        console.error("   [ ] Remove any placeholder text or example values");
        console.error("\nExample of valid configuration:");
        console.error('  {\n    "clientId": "123e4567-e89b-12d3-a456-************",\n    "authority": "https://login.microsoftonline.com/123e4567-e89b-12d3-a456-************"\n  }');
        return; // Exit early to prevent redirect to login page
    }

    // Debug: Log current URL to help diagnose redirect issues
    console.log("Current URL in signIn:", window.location.href);
    
    // Check for potential redirect loop by looking at session storage with time-aware logic (signIn function)
    const signInCount = parseInt(sessionStorage.getItem("msalSignInCount") || "0");
    const lastSignInTime = parseInt(sessionStorage.getItem("msalSignInLastTime") || "0");
    const currentTime = Date.now();
    
    // Reset counter if enough time has passed (15 seconds) to allow for normal user refreshes
    if (currentTime - lastSignInTime > 15000) {
        sessionStorage.setItem("msalSignInCount", "1");
        sessionStorage.setItem("msalSignInLastTime", currentTime.toString());
    } else {
        sessionStorage.setItem("msalSignInCount", (signInCount + 1).toString());
        sessionStorage.setItem("msalSignInLastTime", currentTime.toString());
    }
    
    const actualSignInCount = parseInt(sessionStorage.getItem("msalSignInCount"));
    
    // If we've attempted sign-in more than 10 times in quick succession (within 15 seconds), we might be in a loop
    if (actualSignInCount > 10) {
        console.warn("Detected potential sign-in redirect loop - resetting state");
        // Clear all MSAL-related items from session storage
        Object.keys(sessionStorage).forEach(key => {
            if (key.startsWith('msal.')) {
                sessionStorage.removeItem(key);
            }
        });
        sessionStorage.removeItem("msalSignInCount");
        sessionStorage.removeItem("msalSignInLastTime");
        
        // Only clean authentication-related hashes, preserve application routes
        if (window.location.hash && window.history && window.history.replaceState) {
            const hash = window.location.hash;
            // Only remove hash if it contains MSAL authentication fragments
            const isAuthHash = hash.indexOf('id_token') !== -1 || 
                              hash.indexOf('code=') !== -1 || 
                              hash.indexOf('error=') !== -1 ||
                              hash.indexOf('access_token') !== -1;
            
            if (isAuthHash) {
                window.history.replaceState(null, document.title, window.location.pathname);
                console.log("Sign-in loop detection: Cleaned up authentication-related URL hash");
            } else {
                console.log("Sign-in loop detection: Preserving application route hash:", hash);
            }
        }
        
        // Set a flag to prevent immediate re-authentication
        sessionStorage.setItem("justAuthenticated", "true");
        return;
    }
    
    try {
        // First ensure MSAL is initialized
        await myMSALObj.initialize();
        console.log("MSAL initialized successfully");
        
        // Clear any pending interactions
        myMSALObj.clearCache();
        
        // Get current accounts
        const currentAccounts = myMSALObj.getAllAccounts();
        
        if (currentAccounts && currentAccounts.length > 0) {
            // User is already logged in, try silent token acquisition
            console.log("User already has an account, attempting silent token acquisition");
            const silentRequest = {
                scopes: loginRequest.scopes,
                account: currentAccounts[0],
                forceRefresh: false // Set to true if you need a fresh token
            };
            
            try {
                const response = await myMSALObj.acquireTokenSilent(silentRequest);
                handleResponse(response);
                return; // Exit after successful silent token acquisition
            } catch (error) {
                console.error("Silent token acquisition failed, falling back to redirect", error);
                // Continue to interactive login
            }
        }
        
        // If we get here, we need to do an interactive login
        console.log("No valid session found, starting interactive login");
        
        // Reset the interaction status
        sessionStorage.removeItem('msal.interaction.status');
        
        // Wait a short time to ensure any previous interactions are cleared
        await new Promise(resolve => setTimeout(resolve, 100));
        
        try {
            // Use a timeout to ensure we don't get stuck in a pending state
            const loginPromise = myMSALObj.loginRedirect(loginRequest);
            
            // Set a timeout to reset authInProgress if the promise doesn't resolve/reject
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error('Login redirect timeout'));
                }, 5000); // 5 second timeout
            });
            
            await Promise.race([loginPromise, timeoutPromise]);
        } catch (redirectError) {
            console.error("Redirect error:", redirectError);
            // Clear any partial state that might have been created
            myMSALObj.clearCache();
            window.authInProgress = false;
            
            // If this was a timeout or interaction_in_progress error, clear state and retry
            if (redirectError.message && 
                (redirectError.message.includes('interaction_in_progress') || 
                 redirectError.message.includes('timeout'))) {
                console.warn("Clearing state and retrying login");
                // Clear MSAL state and retry after a short delay
                Object.keys(sessionStorage).forEach(key => {
                    if (key.startsWith('msal.') || key === 'msal.interaction.status') {
                        sessionStorage.removeItem(key);
                    }
                });
                
                // Wait a bit before retrying
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Reset the flag and retry
                window.authInProgress = false;
                return signIn();
            }
            
            throw redirectError;
        }
    } catch (error) {
        console.error("Error during sign-in process:", error);
        // Ensure we always reset the auth in progress flag
        window.authInProgress = false;
        
        // Rethrow the error so it can be handled by the caller if needed
        throw error;
    }
}

// Get access token for API calls
async function getAccessToken() {
    try {
        // Ensure MSAL is initialized
        await myMSALObj.initialize();
        
        const currentAccounts = myMSALObj.getAllAccounts();
        if (!currentAccounts || currentAccounts.length === 0) {
            // No user signed in
            throw new Error("No accounts found");
        }
        
        const silentRequest = {
            scopes: loginRequest.scopes,
            account: currentAccounts[0]
        };
        
        const response = await myMSALObj.acquireTokenSilent(silentRequest);
        return response.accessToken;
    } catch (error) {
        console.error("Silent token acquisition failed", error);
        throw error;
    }
}

// Logout function using direct Azure AD endpoint
async function logout() {
    // Reset auth flag
    window.authInProgress = false;
    
    try {
        // Clear session storage first to ensure a clean logout state
        sessionStorage.clear();
        console.log("Session storage cleared");
        
        // Get the client ID from the MSAL config
        const clientId = msalConfig.auth.clientId;
        
        // Get the tenant from the authority
        const tenantId = msalConfig.auth.authority.split('/').pop();
        
        // Build the logout URL using the v1.0 endpoint which is known to show the account selection UI
        const logoutUrl = `https://login.microsoftonline.com/common/oauth2/logout`
            + `?client_id=${clientId}`
            + `&post_logout_redirect_uri=${encodeURIComponent(window.location.origin + window.location.pathname)}`;
        
        console.log("Redirecting to Azure AD v1.0 logout URL:", logoutUrl);
        
        // Redirect to Azure AD logout page which will show the UI
        window.location.href = logoutUrl;
    } catch (error) {
        console.error("Logout error:", error);
        
        // Fallback: just clear storage and reload the page
        try {
            sessionStorage.clear();
            localStorage.removeItem('msal.token.keys');
            localStorage.removeItem('msal.browser.session.id');
            console.log("Storage cleared as fallback");
        } catch (e) {
            console.error("Error clearing storage:", e);
        }
        
        // Reload the page to reset the application state
        window.location.reload();
    }
}

// Explicitly expose logout function to global scope
window.msal_logout = logout;

// Initialize authentication on page load
async function initializeAuth() {
    console.log("Initializing MSAL authentication");
    
    // Check if auto-auth should be prevented (for example pages)
    const preventAutoAuth = sessionStorage.getItem("preventAutoAuth") === "true";
    if (preventAutoAuth) {
        console.log("Automatic authentication prevented by preventAutoAuth flag");
        return;
    }
    
    // Debug: Log current URL to help diagnose redirect issues
    console.log("Current URL in initializeAuth:", window.location.href);
    
    // Check for potential redirect loop by looking at session storage with time-aware logic (initializeAuth function)
    const initCount = parseInt(sessionStorage.getItem("msalInitCount") || "0");
    const lastInitTime = parseInt(sessionStorage.getItem("msalInitLastTime") || "0");
    const currentTime = Date.now();
    
    // Reset counter if enough time has passed (15 seconds) to allow for normal user refreshes
    if (currentTime - lastInitTime > 15000) {
        sessionStorage.setItem("msalInitCount", "1");
        sessionStorage.setItem("msalInitLastTime", currentTime.toString());
    } else {
        sessionStorage.setItem("msalInitCount", (initCount + 1).toString());
        sessionStorage.setItem("msalInitLastTime", currentTime.toString());
    }
    
    const actualInitCount = parseInt(sessionStorage.getItem("msalInitCount"));
    
    // If we've attempted initialization more than 10 times in quick succession (within 15 seconds), we might be in a loop
    if (actualInitCount > 10) {
        console.warn("Detected potential initialization redirect loop - resetting state");
        // Clear all MSAL-related items from session storage
        Object.keys(sessionStorage).forEach(key => {
            if (key.startsWith('msal.')) {
                sessionStorage.removeItem(key);
            }
        });
        sessionStorage.removeItem("msalInitCount");
        sessionStorage.removeItem("msalInitLastTime");
        
        // Only clean authentication-related hashes, preserve application routes
        if (window.location.hash && window.history && window.history.replaceState) {
            const hash = window.location.hash;
            // Only remove hash if it contains MSAL authentication fragments
            const isAuthHash = hash.indexOf('id_token') !== -1 || 
                              hash.indexOf('code=') !== -1 || 
                              hash.indexOf('error=') !== -1 ||
                              hash.indexOf('access_token') !== -1;
            
            if (isAuthHash) {
                window.history.replaceState(null, document.title, window.location.pathname);
                console.log("Init loop detection: Cleaned up authentication-related URL hash");
            } else {
                console.log("Init loop detection: Preserving application route hash:", hash);
            }
        }
        
        // Set a flag to prevent immediate re-authentication
        sessionStorage.setItem("justAuthenticated", "true");
        return;
    }
    
    try {
        // First initialize MSAL - this is required in v4.12.0 before calling any other methods
        await myMSALObj.initialize();
        console.log("MSAL initialized successfully");
        
        // Check if we're in a redirect callback (has hash fragment)
        const isCallback = window.location.hash && 
                          (window.location.hash.indexOf('id_token') !== -1 || 
                           window.location.hash.indexOf('code=') !== -1 ||
                           window.location.hash.indexOf('error=') !== -1);
        
        if (isCallback) {
            console.log("Detected authentication callback in URL");
        }
        
        // Handle any hash in the URL that might be causing loops
        if (window.location.hash) {
            console.log("URL contains hash fragment:", window.location.hash);
        }
        
        // Then handle the redirect promise
        const response = await myMSALObj.handleRedirectPromise();
        
        // Only clean up authentication-related hashes, not application routes
        if (window.location.hash && window.history && window.history.replaceState) {
            const hash = window.location.hash;
            // Only remove hash if it contains MSAL authentication fragments
            const isAuthHash = hash.indexOf('id_token') !== -1 || 
                              hash.indexOf('code=') !== -1 || 
                              hash.indexOf('error=') !== -1 ||
                              hash.indexOf('access_token') !== -1;
            
            if (isAuthHash) {
                window.history.replaceState(null, document.title, window.location.pathname);
                console.log("Cleaned up authentication-related URL hash to prevent refresh loops");
            } else {
                console.log("Preserving application route hash:", hash);
            }
        }
        
        // Reset the init counter after successful handling
        if (response) {
            sessionStorage.removeItem("msalInitCount");
        }
        
        handleResponse(response);
    } catch (error) {
        console.error("Error during MSAL initialization or redirect handling:", error);
        authInProgress = false;
    }
}

// Check if functions from tokenValidation.js are available in global scope
const ensureAuthenticatedFn = window.ensureAuthenticated || function() {
    console.log('ensureAuthenticated function not found. Make sure tokenValidation.js is loaded before auth.js');
    return Promise.resolve(false);
};

const getCurrentUserFn = window.getCurrentUser || function() {
    console.log('getCurrentUser function not found. Make sure tokenValidation.js is loaded before auth.js');
    return null;
};

const authenticatedFetchFn = window.authenticatedFetch || function(url, options = {}) {
    console.log('authenticatedFetch function not found. Make sure tokenValidation.js is loaded before auth.js');
    return fetch(url, options);
};

// Expose authentication check function to global scope
window.isAuthenticated = ensureAuthenticatedFn;
window.getCurrentUser = getCurrentUserFn;
window.authenticatedFetch = authenticatedFetchFn;

// Simple Token Validator
class SimpleTokenValidator {
    constructor() {
        this.sessionKey = "processExplorer_session";
    }

    async validateTokenAndCreateSession() {
        try {
            console.log("TokenValidator: Starting token validation and session creation");
            
            // Check for stored tokens
            const accessToken = sessionStorage.getItem('msal.accessToken') || localStorage.getItem('msal.accessToken');
            const idToken = sessionStorage.getItem('msal.idToken') || localStorage.getItem('msal.idToken');
            const accountData = sessionStorage.getItem('msal.account') || localStorage.getItem('msal.account');
            
            if (!accessToken || !idToken) {
                console.log("TokenValidator: No tokens found");
                return false;
            }
            
            // Basic token structure validation
            if (!this.isValidJWT(accessToken) || !this.isValidJWT(idToken)) {
                console.log("TokenValidator: Invalid token format");
                return false;
            }
            
            // Check token expiration
            if (this.isTokenExpired(accessToken) || this.isTokenExpired(idToken)) {
                console.log("TokenValidator: Token has expired");
                return false;
            }
            
            // Create session
            let account = null;
            if (accountData) {
                try {
                    account = JSON.parse(accountData);
                } catch (e) {
                    console.warn("TokenValidator: Failed to parse account data");
                }
            }
            
            const session = {
                hasValidTokens: true,
                accountId: account?.homeAccountId || 'unknown',
                username: account?.username || 'unknown',
                name: account?.name || 'User',
                tokenExpiry: this.getTokenExpiry(accessToken),
                createdAt: new Date().toISOString(),
                lastValidated: new Date().toISOString()
            };
            
            // Store the session
            sessionStorage.setItem(this.sessionKey, JSON.stringify(session));
            
            console.log("TokenValidator: Token validation successful, session created");
            return true;
        } catch (error) {
            console.error("TokenValidator: Error during validation:", error);
            return false;
        }
    }
    
    isValidJWT(token) {
        if (!token || typeof token !== 'string') return false;
        const parts = token.split('.');
        return parts.length === 3;
    }
    
    isTokenExpired(token) {
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const now = Math.floor(Date.now() / 1000);
            
            if (payload.exp && payload.exp < now) {
                return true;
            }
            return false;
        } catch (e) {
            return true; // If we can't parse it, consider it expired
        }
    }
    
    getTokenExpiry(token) {
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            return payload.exp ? new Date(payload.exp * 1000).toISOString() : new Date(Date.now() + 3600000).toISOString();
        } catch (e) {
            return new Date(Date.now() + 3600000).toISOString(); // Default to 1 hour from now
        }
    }
}

// Create global tokenValidator instance
const tokenValidator = new SimpleTokenValidator();

/**
 * AuthOverlayManager - Manages the authentication overlay UI
 * Handles showing/hiding overlay, status messages, and user interactions
 */
class AuthOverlayManager {
    constructor() {
        this.elements = null;
        this.isInitialized = false;
        this.autoClickEnabled = false;
    }

    /**
     * Initialize the authentication overlay and set up event listeners
     * Moved from initializeAuthOverlay() in aspenONE.html
     */
    async initialize() {
        if (this.isInitialized) {
            console.log('AuthOverlayManager: Already initialized');
            return;
        }

        console.log('AuthOverlayManager: Initializing authentication overlay...');
        
        // Cache DOM elements
        this.elements = this.getElements();
        
        // Set up event listeners for the overlay
        if (this.elements.loginBtn) {
            this.elements.loginBtn.addEventListener('click', async () => {
                console.log('AuthOverlayManager: Login button clicked');
                try {
                    this.showStatus('Redirecting to Microsoft login...', 'info');
                    this.elements.loginBtn.disabled = true;
                    
                    // Smart token handling: Only clear tokens if they exist and are expired
                    const hasExpiredTokens = this.checkForExpiredTokens();
                    if (hasExpiredTokens) {
                        console.log('AuthOverlayManager: Detected expired tokens, clearing before re-authentication...');
                        
                        // Clear expired/invalid tokens
                        if (window.AuthManager && typeof window.AuthManager.clearTokens === 'function') {
                            window.AuthManager.clearTokens();
                        }
                        
                        // Reset redirect prevention counters for expired token scenarios only
                        if (window.AuthManager) {
                            window.AuthManager.cache.redirectPrevention.clear();
                            window.AuthManager.sessionRedirectCount = Math.max(0, window.AuthManager.sessionRedirectCount - 2);
                            sessionStorage.removeItem('authManager.lastCheck');
                            console.log('AuthOverlayManager: Reset redirect prevention for token expiration scenario');
                        }
                    } else {
                        console.log('AuthOverlayManager: First-time login or no expired tokens detected');
                    }
                    
                    // Initiate MSAL login
                    if (typeof msal !== 'undefined' && typeof msalConfig !== 'undefined') {
                        const msalInstance = new msal.PublicClientApplication(msalConfig);
                        await msalInstance.initialize();
                        
                        // For expired token scenarios, clear MSAL state
                        if (hasExpiredTokens) {
                            const accounts = msalInstance.getAllAccounts();
                            if (accounts.length > 0) {
                                msalInstance.setActiveAccount(null);
                            }
                            console.log('AuthOverlayManager: Cleared MSAL state for re-authentication');
                        }
                        
                        // Initiate login (force refresh only for expired tokens)
                        sessionStorage.setItem('authReturnUrl', window.location.href); // Save the current URL
                        await msalInstance.loginRedirect({
                            scopes: ["openid", "profile", "User.Read"],
                            prompt: 'select_account',
                            forceRefresh: hasExpiredTokens
                        });
                        return;
                    }
                    
                    // Fallback: Use AuthManager redirect
                    if (typeof window.AuthManager !== 'undefined' && 
                        typeof window.AuthManager.redirectToLogin === 'function') {
                        const context = hasExpiredTokens ? 'token_expiration' : 'default';
                        window.AuthManager.redirectToLogin(window.location.href, context);
                    } else {
                        this.showStatus('Authentication system not configured', 'error');
                        this.elements.loginBtn.disabled = false;
                    }
                    
                } catch (error) {
                    console.error('AuthOverlayManager: Error during login:', error);
                    this.showStatus('Login failed. Please try again.', 'error');
                    this.elements.loginBtn.disabled = false;
                }
            });
        }
        
        if (this.elements.continueBtn) {
            this.elements.continueBtn.addEventListener('click', () => {
                console.log('AuthOverlayManager: Continue button clicked');
                this.hide();
                
                // Redirect to landing page if no specific route is set
                if (!window.location.hash || window.location.hash === '#' || window.location.hash === '') {
                    window.location.hash = '#/landing';
                }
            });
        }
        
        if (this.elements.logoutBtn) {
            this.elements.logoutBtn.addEventListener('click', async () => {
                console.log('AuthOverlayManager: Logout button clicked from overlay');
                try {
                    if (typeof window.AuthManager !== 'undefined' && 
                        typeof window.AuthManager.logout === 'function') {
                        await window.AuthManager.logout();
                    } else {
                        // Fallback: clear storage and reload
                        sessionStorage.clear();
                        localStorage.clear();
                        window.location.reload();
                    }
                } catch (error) {
                    console.error('AuthOverlayManager: Error during logout:', error);
                    // Fallback
                    sessionStorage.clear();
                    localStorage.clear();
                    window.location.reload();
                }
            });
        }
        
        this.isInitialized = true;
        console.log('AuthOverlayManager: Authentication overlay initialized');
    }

    /**
     * Get authentication overlay elements
     * Moved from getAuthElements() in aspenONE.html
     */
    getElements() {
        return {
            overlay: document.getElementById('authOverlay'),
            loginSection: document.getElementById('authLoginSection'),
            authenticatedSection: document.getElementById('authAuthenticatedSection'),
            loadingSection: document.getElementById('authLoadingSection'),
            statusMessage: document.getElementById('authStatusMessage'),
            loginBtn: document.getElementById('authLoginBtn'),
            continueBtn: document.getElementById('authContinueBtn'),
            logoutBtn: document.getElementById('authLogoutBtn'),
            userName: document.getElementById('authUserName')
        };
    }

    /**
     * Show the authentication overlay
     * Moved from showAuthOverlay() in aspenONE.html
     */
    show(context = 'default') {
        // Context-based flag checking
        switch(context) {
            case 'initial_load':
                if (!window.showOverlayOnInitialLoad) {
                    console.log('AuthOverlayManager: Overlay blocked by showOverlayOnInitialLoad flag');
                    return;
                }
                break;
            case 'no_token':
                if (!window.showOverlayOnNoToken) {
                    console.log('AuthOverlayManager: Overlay blocked by showOverlayOnNoToken flag');
                    return;
                }
                break;
            case 'token_expiration':
                if (!window.showOverlayOnTokenExpiration) {
                    console.log('AuthOverlayManager: Overlay blocked by showOverlayOnTokenExpiration flag');
                    return;
                }
                break;
            case '401_error':
                if (!window.showOverlayOn401Error) {
                    console.log('AuthOverlayManager: Overlay blocked by showOverlayOn401Error flag');
                    return;
                }
                break;
            case '511_error':
                if (!window.showOverlayOn511Error) {
                    console.log('AuthOverlayManager: Overlay blocked by showOverlayOn511Error flag');
                    return;
                }
                break;
            default:
                // Fallback to original behavior for backward compatibility
                if (!window.showMsalLoginBtn) {
                    console.log('AuthOverlayManager: Overlay blocked by showMsalLoginBtn flag (default behavior)');
                    return;
                }
                break;
        }
        
        console.log(`AuthOverlayManager: Showing overlay for context: ${context}`);
        const elements = this.elements || this.getElements();
        if (elements.overlay) {
            elements.overlay.style.display = 'flex';
        }
    }

    /**
     * Hide the authentication overlay
     * Moved from hideAuthOverlay() in aspenONE.html
     */
    hide() {
        console.log('AuthOverlayManager: Hiding authentication overlay');
        const elements = this.elements || this.getElements();
        if (elements.overlay) {
            elements.overlay.style.display = 'none';
        }
    }

    /**
     * Show authentication status message
     * Moved from showAuthStatus() in aspenONE.html
     */
    showStatus(message, type = 'info') {
        console.log(`AuthOverlayManager: Auth status (${type}):`, message);
        const elements = this.elements || this.getElements();
        if (elements.statusMessage) {
            elements.statusMessage.innerHTML = `<p>${message}</p>`;
            elements.statusMessage.className = `auth-status-message auth-status-${type}`;
            elements.statusMessage.style.display = 'block';
        }
    }

    /**
     * Hide authentication status message
     * Moved from hideAuthStatus() in aspenONE.html
     */
    hideStatus() {
        const elements = this.elements || this.getElements();
        if (elements.statusMessage) {
            elements.statusMessage.style.display = 'none';
        }
    }

    /**
     * Update the authentication overlay UI based on current state
     * Moved from updateAuthOverlayUI() in aspenONE.html
     */
    async updateUI() {
        console.log('AuthOverlayManager: Updating authentication overlay UI');
        const elements = this.elements || this.getElements();
        
        try {
            // Check if user is authenticated
            let authState = { isAuthenticated: false };
            if (typeof window.AuthManager !== 'undefined' && 
                typeof window.AuthManager.isUserAuthenticated === 'function') {
                authState = await window.AuthManager.isUserAuthenticated();
            }
            
            if (authState && authState.isAuthenticated) {
                // Show authenticated section
                if (elements.loginSection) elements.loginSection.style.display = 'none';
                if (elements.loadingSection) elements.loadingSection.style.display = 'none';
                if (elements.authenticatedSection) elements.authenticatedSection.style.display = 'block';
                
                // Update user name if available
                const userAccount = localStorage.getItem('msal.account');
                if (userAccount && elements.userName) {
                    try {
                        const account = JSON.parse(userAccount);
                        elements.userName.textContent = account.name || account.username || 'User';
                    } catch (e) {
                        elements.userName.textContent = 'User';
                    }
                }
                
                this.showStatus('Successfully authenticated!', 'success');
            } else {
                // Show login section
                if (elements.authenticatedSection) elements.authenticatedSection.style.display = 'none';
                if (elements.loadingSection) elements.loadingSection.style.display = 'none';
                if (elements.loginSection) elements.loginSection.style.display = 'block';
                
                this.hideStatus();
            }
        } catch (error) {
            console.error('AuthOverlayManager: Error updating auth overlay UI:', error);
            // Default to showing login section
            if (elements.authenticatedSection) elements.authenticatedSection.style.display = 'none';
            if (elements.loadingSection) elements.loadingSection.style.display = 'none';
            if (elements.loginSection) elements.loginSection.style.display = 'block';
        }
    }

    /**
     * Check and auto-click login button if flag is enabled
     * Moved from checkAndAutoClick() in aspenONE.html
     */
    async checkAndAutoClick() {
        if (window.showMsalLoginBtn === true) {
            console.log('AuthOverlayManager: showMsalLoginBtn flag enabled, auto-clicking login button...');
            
            // Small delay to ensure overlay is fully rendered
            setTimeout(() => {
                const elements = this.elements || this.getElements();
                if (elements.loginBtn) {
                    console.log('AuthOverlayManager: Simulating login button click');
                    elements.loginBtn.click();
                } else {
                    console.warn('AuthOverlayManager: Login button not found for auto-click');
                }
            }, 50);
        }
    }

    /**
     * Set auto-click mode for login button
     */
    setAutoClickEnabled(enabled) {
        this.autoClickEnabled = enabled;
        window.showMsalLoginBtn = enabled;
    }

    /**
     * Check if there are expired tokens that need to be cleared
     * @returns {boolean} True if expired tokens are found
     */
    checkForExpiredTokens() {
        const accessToken = sessionStorage.getItem('msal.accessToken') || localStorage.getItem('msal.accessToken');
        const idToken = sessionStorage.getItem('msal.idToken') || localStorage.getItem('msal.idToken');
        
        // No tokens = first-time login, not expired tokens
        if (!accessToken || !idToken) {
            return false;
        }
        
        try {
            // Check if either token is expired
            const now = Math.floor(Date.now() / 1000);
            
            // Check access token
            const accessPayload = JSON.parse(atob(accessToken.split('.')[1]));
            const accessExpired = accessPayload.exp && accessPayload.exp < now;
            
            // Check ID token
            const idPayload = JSON.parse(atob(idToken.split('.')[1]));
            const idExpired = idPayload.exp && idPayload.exp < now;
            
            const hasExpired = accessExpired || idExpired;
            
            if (hasExpired) {
                console.log('AuthOverlayManager: Found expired tokens - Access:', accessExpired, 'ID:', idExpired);
            }
            
            return hasExpired;
        } catch (error) {
            console.warn('AuthOverlayManager: Error checking token expiration, treating as expired:', error);
            return true; // If we can't parse tokens, treat as expired
        }
    }
}

// Create global instance
window.AuthOverlayManager = new AuthOverlayManager();

/**
 * AuthFlowManager - Manages authentication flow and initialization
 * Handles waiting for AuthManager, authentication initialization, and periodic checks
 */
class AuthFlowManager {
    constructor(authManager, overlayManager) {
        this.authManager = authManager;
        this.overlayManager = overlayManager;
        this.periodicCheckInterval = null;
        this.isInitialized = false;
        this.periodicCheckIntervalId = null;
    }

    /**
     * Wait for AuthManager to be available
     * Moved from waitForAuthManager() in aspenONE.html
     */
    async waitForAuthManager() {
        return new Promise((resolve, reject) => {
            let retryCount = 0;
            const maxRetries = 50; // 5 seconds max wait
            
            const checkAuthManager = () => {
                // Check if AuthManager is loaded as a global object with required methods
                if (typeof window.AuthManager !== 'undefined' && 
                    window.AuthManager !== null &&
                    typeof window.AuthManager.requiresAuthentication === 'function' &&
                    typeof window.AuthManager.isUserAuthenticated === 'function' &&
                    typeof window.AuthManager.redirectToLogin === 'function') {
                    console.log('AuthFlowManager: AuthManager loaded and ready with all required methods');
                    resolve();
                } else if (retryCount >= maxRetries) {
                    console.warn('AuthFlowManager: AuthManager not available after maximum retries. Available methods:', 
                        window.AuthManager ? Object.getOwnPropertyNames(window.AuthManager.__proto__) : 'AuthManager not defined');
                    reject(new Error('AuthManager not available'));
                } else {
                    retryCount++;
                    console.log(`AuthFlowManager: Waiting for AuthManager... attempt ${retryCount}/${maxRetries}`);
                    setTimeout(checkAuthManager, 100);
                }
            };
            
            checkAuthManager();
        });
    }

    /**
     * Initialize authentication with overlay support
     * Moved from initializeAuthenticationWithOverlay() in aspenONE.html
     */
    async initializeWithOverlay() {
        console.log('AuthFlowManager: Initializing authentication flow with overlay...');

        await this.waitForAuthManager();

        // NEW: Use SharedAuth 8-scenario logic instead of custom authentication check
        console.log('AuthFlowManager: Using SharedAuth 8-scenario authentication flow...');

        // Wait for SharedAuth to be available
        if (typeof window.SharedAuth === 'undefined') {
            console.warn('AuthFlowManager: SharedAuth not available, falling back to original logic');
            // Fall back to original logic if SharedAuth is not loaded
            const authStatus = await this.authManager.isUserAuthenticated();
            console.log('AuthFlowManager: Fallback authentication check - authStatus:', authStatus);

            if (authStatus && authStatus.isAuthenticated) {
                console.log('AuthFlowManager: User already authenticated (fallback), hiding overlay and starting periodic checks');
                this.overlayManager.hide();
                this.startPeriodicChecks();
                return true;
            }
        } else {
            // Use SharedAuth 8-scenario logic
            const authFlow = window.SharedAuth.determineAuthFlow();
            console.log('AuthFlowManager: SharedAuth flow determination:', authFlow);

            if (authFlow.clearData) {
                console.log('AuthFlowManager: SharedAuth determined data should be cleared (invalid JSON)');
                window.SharedAuth.clearAllAuthData();
                this.overlayManager.hide();
                return true; // Continue without authentication
            }

            if (!authFlow.showOverlay) {
                console.log('AuthFlowManager: SharedAuth determined no overlay needed (valid localStorage)');
                this.overlayManager.hide();
                this.startPeriodicChecks();
                return true; // Continue with existing authentication
            }

            if (authFlow.showOverlay && authFlow.showLoginPanel) {
                console.log('AuthFlowManager: SharedAuth determined overlay and login needed');
                this.overlayManager.show('initial_load');
                // Continue with authentication flow below
            }
        }

        // SharedAuth has already determined the authentication flow above
        // If we reach here, it means we need to proceed with authentication
        console.log('AuthFlowManager: Proceeding with authentication flow as determined by SharedAuth');

        try {
            console.log('AuthFlowManager: Initializing authentication system with overlay...');
            
            await this.authManager.initialize();
            
            // Check for bypass parameter
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('bypassAuth') === 'true') {
                console.log('AuthFlowManager: Authentication bypassed via URL parameter');
                this.overlayManager.hide();
                return true;
            }
            
            // Wait for AuthManager to be loaded
            await this.waitForAuthManager();
            
            // Debug: Check current authentication status first
            const currentAuthStatus = await window.AuthManager.isUserAuthenticated();
            console.log('AuthFlowManager: Current authentication status before requiresAuthentication:', currentAuthStatus);
            
            // Check for stored tokens for debugging
            const accessToken = sessionStorage.getItem('msal.accessToken') || localStorage.getItem('msal.accessToken');
            const idToken = sessionStorage.getItem('msal.idToken') || localStorage.getItem('msal.idToken');
            console.log('AuthFlowManager: Stored tokens - Access:', !!accessToken, 'ID:', !!idToken);
            
            // Check for authentication response in URL
            const urlHash = window.location.hash;
            const hasAuthCode = urlHash && (urlHash.indexOf('code=') !== -1 || urlHash.indexOf('error=') !== -1);
            
            if (hasAuthCode) {
                console.log('AuthFlowManager: Processing authentication response in overlay...');
                this.overlayManager.show('initial_load');
                
                const elements = this.overlayManager.getElements();
                if (elements.loadingSection) elements.loadingSection.style.display = 'block';
                if (elements.loginSection) elements.loginSection.style.display = 'none';
                if (elements.authenticatedSection) elements.authenticatedSection.style.display = 'none';
                this.overlayManager.showStatus('Processing authentication response...', 'info');
                
                try {
                    // Process MSAL authentication response
                    if (typeof msal !== 'undefined' && typeof msalConfig !== 'undefined') {
                        const msalInstance = new msal.PublicClientApplication(msalConfig);
                        await msalInstance.initialize();
                        
                        const response = await msalInstance.handleRedirectPromise();
                        if (response) {
                            console.log('AuthFlowManager: MSAL authentication successful in overlay:', response);
                            
                            // Store tokens
                            if (response.accessToken) {
                                sessionStorage.setItem('msal.accessToken', response.accessToken);
                                localStorage.setItem('msal.accessToken', response.accessToken);
                            }
                            if (response.idToken) {
                                sessionStorage.setItem('msal.idToken', response.idToken);
                                localStorage.setItem('msal.idToken', response.idToken);
                            }
                            if (response.account) {
                                msalInstance.setActiveAccount(response.account);
                                sessionStorage.setItem('msal.account', JSON.stringify(response.account));
                                localStorage.setItem('msal.account', JSON.stringify(response.account));
                            }
                            
                            // Force AuthManager cache refresh
                            if (window.AuthManager) {
                                window.AuthManager.cache.authStatus = null;
                                window.AuthManager.cache.authStatusExpiry = 0;
                            }
                            
                            // Check for a stored return URL and redirect
                            const returnUrl = sessionStorage.getItem('authReturnUrl');
                            if (returnUrl) {
                                console.log('AuthFlowManager: Authentication successful. Redirecting to stored URL:', returnUrl);
                                sessionStorage.removeItem('authReturnUrl');
                                window.location.href = returnUrl;
                            } else {
                                // Redirect to the landing page via hash and then reload the page.
                                // This ensures the next page load has the correct auth state and the correct URL for React routing.
                                console.log('AuthFlowManager: Authentication successful. Redirecting to landing page...');
                                window.location.hash = '#/landing';
                                window.location.reload();
                            }
                            
                            // The code below this point will not execute due to the reload.
                            return true;
                        }
                    }
                } catch (authError) {
                    console.error('AuthFlowManager: Error handling authentication response in overlay:', authError);
                    
                    // Handle specific CORS errors
                    if (authError.name === 'BrowserAuthError' && authError.errorCode === 'post_request_failed') {
                        console.error('AuthFlowManager: CORS error detected - redirect URI configuration issue');
                        this.overlayManager.showStatus(`
                            <strong>Configuration Error:</strong><br>
                            The redirect URI is not properly registered in Azure AD.<br>
                            <small>Current redirect URI: ${msalConfig?.auth?.redirectUri || 'unknown'}</small><br>
                            <small>Please register this URL as a Single Page Application (SPA) in Azure AD.</small>
                        `, 'error');
                        
                        // Show debug info
                        if (typeof debugMSALConfig === 'function') {
                            console.warn('[DEBUG] Running MSAL configuration debug...');
                            debugMSALConfig();
                        }
                    } else {
                        // General authentication error
                        this.overlayManager.showStatus('Authentication failed. Please try again.', 'error');
                    }
                    
                    // Re-enable login button
                    const elements = this.overlayManager.getElements();
                    if (elements.loginBtn) elements.loginBtn.disabled = false;
                }
            }
            
            // Check if authentication is required for this page
            const authResult = await window.AuthManager.requiresAuthentication(window.location.href);
            console.log('AuthFlowManager: requiresAuthentication result:', authResult);
            
            if (authResult.requiresRedirect) {
                console.log('AuthFlowManager: Authentication required...');
                
                // Determine if this is token expiration or no token exists
                const hasAnyToken = sessionStorage.getItem('msal.accessToken') || 
                                   localStorage.getItem('msal.accessToken') ||
                                   sessionStorage.getItem('msal.idToken') || 
                                   localStorage.getItem('msal.idToken');
                
                // Check if this is a deep-linked URL (not just the landing page)
                const isDeepLink = window.location.hash && 
                                  window.location.hash !== '#' && 
                                  window.location.hash !== '#/' && 
                                  window.location.hash !== '#/landing';
                const hasQueryParams = window.location.search && window.location.search.length > 0;
                const isDeepLinkedUrl = isDeepLink || hasQueryParams;
                
                if (!hasAnyToken && isDeepLinkedUrl) {
                    // First-time user accessing deep-linked content - auto-redirect without overlay
                    console.log('AuthFlowManager: Deep-linked URL detected, auto-redirecting to login...');
                    
                    // Save the return URL before redirecting
                    sessionStorage.setItem('authReturnUrl', window.location.href);
                    
                    // Automatically initiate MSAL login redirect
                    if (typeof msal !== 'undefined' && typeof msalConfig !== 'undefined') {
                        try {
                            const msalInstance = new msal.PublicClientApplication(msalConfig);
                            await msalInstance.initialize();
                            
                            await msalInstance.loginRedirect({
                                scopes: ["openid", "profile", "User.Read"],
                                prompt: 'select_account'
                            });
                            return false;
                        } catch (error) {
                            console.error('AuthFlowManager: Error during auto-redirect:', error);
                            // Fall back to showing overlay
                        }
                    }
                }
                
                // Handle landing page and other scenarios
                if (hasAnyToken) {
                    // User had tokens before, but they're expired/invalid
                    console.log('AuthFlowManager: Tokens exist but invalid - using token_expiration context');
                    this.overlayManager.show('token_expiration');
                } else {
                    // Check if this is the landing page - auto-redirect without overlay
                    const isLandingPage = !window.location.hash || 
                                         window.location.hash === '#' || 
                                         window.location.hash === '#/' || 
                                         window.location.hash === '#/landing';
                    
                    if (isLandingPage && !hasQueryParams) {
                        // First-time user on landing page - auto-redirect without overlay
                        console.log('AuthFlowManager: Landing page detected, auto-redirecting to login...');
                        
                        // Save the return URL before redirecting
                        sessionStorage.setItem('authReturnUrl', window.location.href);
                        
                        // Automatically initiate MSAL login redirect
                        if (typeof msal !== 'undefined' && typeof msalConfig !== 'undefined') {
                            try {
                                const msalInstance = new msal.PublicClientApplication(msalConfig);
                                await msalInstance.initialize();
                                
                                await msalInstance.loginRedirect({
                                    scopes: ["openid", "profile", "User.Read"],
                                    prompt: 'select_account'
                                });
                                return false;
                            } catch (error) {
                                console.error('AuthFlowManager: Error during auto-redirect for landing page:', error);
                                // Fall back to showing overlay
                            }
                        }
                    }
                    
                    // Fallback: show login overlay for other cases
                    console.log('AuthFlowManager: Showing login overlay');
                    this.overlayManager.show('no_token');
                }
                                
                await this.overlayManager.updateUI();
                
                // Auto-click login button if flag is enabled
                await this.overlayManager.checkAndAutoClick();
                
                return false; // Don't continue with normal initialization yet
            } else {
                console.log('AuthFlowManager: Authentication check passed:', authResult.reason);
                this.overlayManager.hide();
                
                // If user is authenticated but no React route hash, redirect to landing page
                if (!window.location.hash || window.location.hash === '#' || window.location.hash === '') {
                    console.log('AuthFlowManager: User authenticated, redirecting to landing page');
                    window.location.hash = '#/landing';
                }
                
                if (!accessToken || !idToken) {
                    return false;
                } else {
                    console.log('AuthFlowManager: Authentication check passed:', authResult.reason);
                    return true; // Continue with normal initialization
                }
            }
        } catch (error) {
            console.error('AuthFlowManager: Error during authentication initialization with overlay:', error);
            this.overlayManager.hide();
            return true; // Continue with normal initialization on error
        }
    }

    /**
     * Handle token validation errors
     * Moved from handleTokenError() in aspenONE.html
     */
    async handleTokenError(error) {
        console.error('AuthFlowManager: Token validation error:', error);
        
        try {
            // Check if AuthManager is available
            if (typeof window.AuthManager === 'undefined' || 
                typeof window.AuthManager.checkMSALConfiguration !== 'function') {
                console.log('AuthFlowManager: AuthManager not available for token error handling, using fallback');
                window.location.href = 'login.html';
                return;
            }
            
            const msalConfigured = await window.AuthManager.checkMSALConfiguration();
            if (msalConfigured) {
                window.AuthManager.redirectToLogin(window.location.href);
            } else {
                console.log('AuthFlowManager: MSAL not configured, token error handled as warning');
            }
        } catch (e) {
            console.error('AuthFlowManager: Error in handleTokenError:', e);
            // Fallback: show auth overlay instead of redirecting
            if (this.overlayManager) {
                this.overlayManager.show('token_expiration');
            } else {
                window.location.reload();
            }
        }
    }

    /**
     * Periodic authentication check
     * Moved from periodicAuthCheck() in aspenONE.html
     */
    async performPeriodicCheck() {
        try {
            if (typeof window.AuthManager !== 'undefined' && 
                typeof window.AuthManager.isUserAuthenticated === 'function') {
                
                const authState = await window.AuthManager.isUserAuthenticated();
                
                if (!authState.isAuthenticated) {
                    console.log(`AuthFlowManager: Periodic check detected unauthenticated state (Status: ${authState.status})`);
                    
                    if (authState.status === 'expired') {
                        console.log('AuthFlowManager: Token expired, clearing tokens and showing overlay.');
                        window.AuthManager.clearTokens();
                        this.overlayManager.show('token_expiration');
                        await this.overlayManager.updateUI();
                    }
                }
            }
        } catch (error) {
            console.error('AuthFlowManager: Error in periodic auth check:', error);
        }
    }

    /**
     * Start periodic authentication checks
     */
    startPeriodicChecks(intervalMs = 30000) {
        if (this.periodicCheckInterval) {
            console.log('AuthFlowManager: Periodic checks already running');
            return;
        }
        
        console.log('AuthFlowManager: Starting periodic authentication checks every', intervalMs, 'ms');
        this.periodicCheckInterval = setInterval(() => {
            this.performPeriodicCheck();
        }, intervalMs);
    }

    /**
     * Stop periodic authentication checks
     */
    stopPeriodicChecks() {
        if (this.periodicCheckInterval) {
            console.log('AuthFlowManager: Stopping periodic authentication checks');
            clearInterval(this.periodicCheckInterval);
            this.periodicCheckInterval = null;
        }
    }

    /**
     * Enhanced XMLHttpRequest override with AuthManager integration
     * Moved from XMLHttpRequest override in aspenONE.html
     */
    setupXHRInterceptor() {
        const originalOpen = XMLHttpRequest.prototype.open;
        const self = this;
        
        XMLHttpRequest.prototype.open = function() {
            this.addEventListener('load', function() {
                if (this.status === 401 || this.status === 501) { // Unauthorized
                    console.error('AuthFlowManager: Received 401/Unauthorized, checking authentication...');
                    
                    // Use AuthManager to handle the authentication error
                    (async () => {
                        try {
                            // Check if AuthManager is available
                            if (typeof window.AuthManager === 'undefined' || 
                                typeof window.AuthManager.checkMSALConfiguration !== 'function') {
                                console.log('AuthFlowManager: AuthManager not available for 401 handling, using fallback');
                                if (self.overlayManager) {
                                    self.overlayManager.show('401_error');
                                } else {
                                    self.redirectToLogin();
                                }
                                return;
                            }
                            
                            const msalConfigured = await window.AuthManager.checkMSALConfiguration();
                            if (msalConfigured) {
                                console.log('AuthFlowManager: MSAL configured, redirecting to login for re-authentication');
                                window.AuthManager.redirectToLogin(window.location.href);
                            } else {
                                console.log('AuthFlowManager: MSAL not configured, handling 401 as application error');
                                if (self.overlayManager) {
                                    self.overlayManager.show('401_error');
                                }
                            }
                        } catch (error) {
                            console.error('AuthFlowManager: Error handling 401 response:', error);
                            // Fallback to original behavior
                            if (self.overlayManager) {
                                self.overlayManager.show('401_error');
                            } else {
                                self.redirectToLogin();
                            }
                        }
                    })();
                } else if (this.status === 511) { // Network Authentication Required
                    console.error('AuthFlowManager: Received 511/Network Authentication Required, showing authentication overlay...');
                    
                    // Handle 511 errors with immediate overlay display
                    if (self.overlayManager) {
                        self.overlayManager.show('511_error');
                    } else {
                        console.error('AuthFlowManager: Overlay manager not available for 511 handling');
                        self.redirectToLogin();
                    }
                }
            });
            return originalOpen.apply(this, arguments);
        };
    }

    /**
     * Legacy redirect to login function for backward compatibility
     * Moved from redirectToLogin() in aspenONE.html
     */
    redirectToLogin() {
        console.warn('AuthFlowManager: Using legacy redirectToLogin function, consider using AuthManager.redirectToLogin');
        
        // Check if AuthManager is available
        if (typeof window.AuthManager !== 'undefined' && 
            typeof window.AuthManager.redirectToLogin === 'function') {
            window.AuthManager.redirectToLogin(window.location.href);
        } else {
            console.warn('AuthFlowManager: AuthManager not available, showing auth overlay fallback');
            if (this.overlayManager) {
                this.overlayManager.show('token_expiration');
            } else {
                console.error('AuthFlowManager: Auth overlay not available, reloading page');
                window.location.reload();
            }
        }
    }
}

// Global authentication flow manager will be created after initialization
window.AuthFlowManager = AuthFlowManager;

/**
 * ProcessExplorerAuth - Global coordinator for all authentication components
 * Provides centralized initialization and coordination between AuthManager, AuthOverlayManager, and AuthFlowManager
 */
window.ProcessExplorerAuth = {
    overlayManager: window.AuthOverlayManager,
    flowManager: null, // Will be created after AuthManager is ready
    isInitialized: false,

    /**
     * Initialize the complete authentication system
     * Replaces the DOMContentLoaded handler from aspenONE.html
     */
    async initialize() {
        if (this.isInitialized) {
            console.log('ProcessExplorerAuth: Already initialized');
            return true;
        }

        try {
            console.log('ProcessExplorerAuth: Starting authentication system initialization...');

            // Step 1: Initialize the overlay manager first (for UI)
            await this.overlayManager.initialize();
            console.log('ProcessExplorerAuth: Overlay manager initialized');

            // Step 2: Create flow manager with AuthManager and overlay manager
            this.flowManager = new AuthFlowManager(window.AuthManager, this.overlayManager);
            console.log('ProcessExplorerAuth: Flow manager created');

            // Step 3: Setup XMLHttpRequest interceptor for 401 handling
            this.flowManager.setupXHRInterceptor();
            console.log('ProcessExplorerAuth: XHR interceptor setup');

            // Step 4: Perform initial authentication check with overlay
            const canContinue = await this.flowManager.initializeWithOverlay();
            console.log('ProcessExplorerAuth: Initial authentication check completed, canContinue:', canContinue);

            // Step 5: Set up periodic authentication checks if MSAL is configured
            if (canContinue) {
                try {
                    // Check if AuthManager is available
                    if (typeof window.AuthManager !== 'undefined' && 
                        typeof window.AuthManager.checkMSALConfiguration === 'function') {
                        const msalConfigured = await window.AuthManager.checkMSALConfiguration();
                        if (msalConfigured) {
                            console.log('ProcessExplorerAuth: Setting up periodic authentication checks');
                            this.flowManager.startPeriodicChecks(30000); // 30 seconds
                        } else {
                            console.log('ProcessExplorerAuth: MSAL not configured, no periodic auth checks needed');
                        }
                    } else {
                        console.log('ProcessExplorerAuth: AuthManager not available, no periodic auth checks');
                    }
                } catch (error) {
                    console.error('ProcessExplorerAuth: Error setting up periodic checks:', error);
                }
            }

            // Step 6: Expose global error handler
            window.handleTokenError = (error) => this.flowManager.handleTokenError(error);

            // Step 7: Expose legacy functions for backward compatibility
            this.setupLegacyCompatibility();

            this.isInitialized = true;
            console.log('ProcessExplorerAuth: Authentication system fully initialized');
            return canContinue;

        } catch (error) {
            console.error('ProcessExplorerAuth: Error during authentication initialization:', error);
            // On error, still mark as initialized to prevent loops
            this.isInitialized = true;
            return true; // Continue with normal initialization on error
        }
    },

    /**
     * Setup legacy compatibility functions
     * Ensures existing code continues to work during transition
     */
    setupLegacyCompatibility() {
        // Expose overlay functions to global scope for backward compatibility
        window.showAuthOverlay = () => this.overlayManager.show();
        window.hideAuthOverlay = () => this.overlayManager.hide();
        window.showAuthStatus = (message, type) => this.overlayManager.showStatus(message, type);
        window.updateAuthOverlayUI = () => this.overlayManager.updateUI();
        window.getAuthElements = () => this.overlayManager.getElements();
        window.checkAndAutoClick = () => this.overlayManager.checkAndAutoClick();

        // Expose flow functions
        window.redirectToLogin = () => this.flowManager.redirectToLogin();
        window.periodicAuthCheck = () => this.flowManager.performPeriodicCheck();

        // Legacy function aliases
        window.initializeAuthOverlay = () => this.overlayManager.initialize();
        window.initializeAuthenticationWithOverlay = () => this.flowManager.initializeWithOverlay();
        window.waitForAuthManager = () => this.flowManager.waitForAuthManager();

        console.log('ProcessExplorerAuth: Legacy compatibility functions exposed');
    },

    /**
     * Cleanup function for testing or reset scenarios
     */
    cleanup() {
        if (this.flowManager) {
            this.flowManager.stopPeriodicChecks();
        }
        this.isInitialized = false;
        console.log('ProcessExplorerAuth: Cleanup completed');
    },

    /**
     * Get current authentication status summary
     */
    async getStatus() {
        const status = {
            initialized: this.isInitialized,
            overlayManagerReady: this.overlayManager?.isInitialized || false,
            flowManagerReady: this.flowManager !== null,
            authManagerReady: typeof window.AuthManager !== 'undefined'
        };

        if (window.AuthManager && typeof window.AuthManager.isUserAuthenticated === 'function') {
            try {
                status.userAuthenticated = await window.AuthManager.isUserAuthenticated();
                status.msalConfigured = await window.AuthManager.checkMSALConfiguration();
            } catch (error) {
                status.authError = error.message;
            }
        }

        return status;
    }
};

console.log('ProcessExplorerAuth: Global authentication coordinator loaded and ready');

// Auto-initialize when DOM is ready (replaces DOMContentLoaded handler from aspenONE.html)
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', async () => {
        try {
            await window.ProcessExplorerAuth.initialize();
        } catch (error) {
            console.error('ProcessExplorerAuth: Automatic initialization failed:', error);
        }
    });
} else {
    // DOM already loaded, initialize immediately
    setTimeout(async () => {
        try {
            await window.ProcessExplorerAuth.initialize();
        } catch (error) {
            console.error('ProcessExplorerAuth: Immediate initialization failed:', error);
        }
    }, 0);
}

/**
 * TokenExpirationTester - Developer tools for testing token expiration scenarios
 * Provides methods to simulate expiration without waiting 65 minutes
 */
class TokenExpirationTester {
    constructor() {
        this.originalTokens = null;
        this.testMode = false;
    }

    /**
     * Simulate token expiration by modifying the exp claim in stored tokens
     * @param {number} expireInSeconds - Make tokens expire in X seconds (default: 5 seconds)
     */
    simulateExpiration(expireInSeconds = 5) {
        console.log(`🧪 TokenExpirationTester: Simulating token expiration in ${expireInSeconds} seconds...`);
        
        // Store original tokens for restoration
        this.originalTokens = {
            accessToken: sessionStorage.getItem('msal.accessToken') || localStorage.getItem('msal.accessToken'),
            idToken: sessionStorage.getItem('msal.idToken') || localStorage.getItem('msal.idToken')
        };
        
        if (!this.originalTokens.accessToken || !this.originalTokens.idToken) {
            console.error('[ERROR] No tokens found to modify. Please login first.');
            return false;
        }
        
        try {
            // Calculate new expiration time (now + expireInSeconds)
            const newExp = Math.floor(Date.now() / 1000) + expireInSeconds;
            
            // Modify access token
            const modifiedAccessToken = this.modifyTokenExpiration(this.originalTokens.accessToken, newExp);
            const modifiedIdToken = this.modifyTokenExpiration(this.originalTokens.idToken, newExp);
            
            // Store modified tokens
            sessionStorage.setItem('msal.accessToken', modifiedAccessToken);
            localStorage.setItem('msal.accessToken', modifiedAccessToken);
            sessionStorage.setItem('msal.idToken', modifiedIdToken);
            localStorage.setItem('msal.idToken', modifiedIdToken);
            
            // Clear AuthManager cache to force re-check
            if (window.AuthManager) {
                window.AuthManager.cache.authStatus = null;
                window.AuthManager.cache.authStatusExpiry = 0;
            }
            
            this.testMode = true;
            
            console.log(`[OK] Tokens modified to expire at: ${new Date(newExp * 1000).toLocaleString()}`);
            console.log(`[INFO] Tokens will be considered expired in ${expireInSeconds} seconds`);
            console.log('[INFO] Use TokenExpirationTester.checkStatus() to monitor expiration status');
            
            return true;
        } catch (error) {
            console.error('[ERROR] Failed to modify tokens:', error);
            return false;
        }
    }
    
    /**
     * Modify the exp claim in a JWT token
     * @param {string} token - Original JWT token
     * @param {number} newExp - New expiration timestamp (Unix timestamp)
     * @returns {string} Modified token
     */
    modifyTokenExpiration(token, newExp) {
        const parts = token.split('.');
        if (parts.length !== 3) {
            throw new Error('Invalid JWT format');
        }
        
        // Decode payload
        const payload = JSON.parse(atob(parts[1]));
        
        // Modify expiration
        payload.exp = newExp;
        
        // Re-encode payload
        const modifiedPayload = btoa(JSON.stringify(payload));
        
        // Return modified token
        return `${parts[0]}.${modifiedPayload}.${parts[2]}`;
    }
    
    /**
     * Force immediate token expiration (expire now)
     */
    forceExpireNow() {
        return this.simulateExpiration(0);
    }
    
    /**
     * Check current token expiration status
     */
    checkStatus() {
        const accessToken = sessionStorage.getItem('msal.accessToken') || localStorage.getItem('msal.accessToken');
        const idToken = sessionStorage.getItem('msal.idToken') || localStorage.getItem('msal.idToken');
        
        if (!accessToken || !idToken) {
            console.log('[ERROR] No tokens found');
            return { hasTokens: false };
        }
        
        try {
            const accessPayload = JSON.parse(atob(accessToken.split('.')[1]));
            const idPayload = JSON.parse(atob(idToken.split('.')[1]));
            const now = Math.floor(Date.now() / 1000);
            
            const accessExpired = accessPayload.exp && accessPayload.exp < now;
            const idExpired = idPayload.exp && idPayload.exp < now;
            
            const status = {
                hasTokens: true,
                accessToken: {
                    expired: accessExpired,
                    expiresAt: new Date(accessPayload.exp * 1000).toLocaleString(),
                    secondsUntilExpiry: accessPayload.exp - now
                },
                idToken: {
                    expired: idExpired,
                    expiresAt: new Date(idPayload.exp * 1000).toLocaleString(),
                    secondsUntilExpiry: idPayload.exp - now
                },
                anyExpired: accessExpired || idExpired
            };
            
            console.log('[INFO] Token Status:', status);
            return status;
        } catch (error) {
            console.error('[ERROR] Error checking token status:', error);
            return { hasTokens: true, error: error.message };
        }
    }
    
    /**
     * Restore original tokens (undo simulation)
     */
    restoreOriginalTokens() {
        if (!this.originalTokens) {
            console.log('[ERROR] No original tokens to restore');
            return false;
        }
        
        sessionStorage.setItem('msal.accessToken', this.originalTokens.accessToken);
        localStorage.setItem('msal.accessToken', this.originalTokens.accessToken);
        sessionStorage.setItem('msal.idToken', this.originalTokens.idToken);
        localStorage.setItem('msal.idToken', this.originalTokens.idToken);
        
        // Clear AuthManager cache
        if (window.AuthManager) {
            window.AuthManager.cache.authStatus = null;
            window.AuthManager.cache.authStatusExpiry = 0;
        }
        
        this.testMode = false;
        this.originalTokens = null;
        
        console.log('[OK] Original tokens restored');
        return true;
    }
    
    /**
     * Test the complete expiration flow
     * 1. Simulate expiration
     * 2. Trigger periodic check
     * 3. Monitor overlay behavior
     */
    async testExpirationFlow(expireInSeconds = 10) {
        console.log('🧪 Starting complete expiration flow test...');
        
        // Step 1: Simulate expiration
        if (!this.simulateExpiration(expireInSeconds)) {
            return false;
        }
        
        // Step 2: Wait for expiration + 1 second
        console.log(`⏳ Waiting ${expireInSeconds + 1} seconds for expiration...`);
        await new Promise(resolve => setTimeout(resolve, (expireInSeconds + 1) * 1000));
        
        // Step 3: Trigger manual auth check
        console.log('[INFO] Triggering manual authentication check...');
        if (window.AuthManager && typeof window.AuthManager.isUserAuthenticated === 'function') {
            const isAuth = await window.AuthManager.isUserAuthenticated();
            console.log('Authentication status:', isAuth);
        }
        
        // Step 4: Trigger periodic check if available
        if (window.ProcessExplorerAuth && 
            window.ProcessExplorerAuth.flowManager && 
            typeof window.ProcessExplorerAuth.flowManager.performPeriodicCheck === 'function') {
            console.log('[INFO] Triggering periodic check...');
            await window.ProcessExplorerAuth.flowManager.performPeriodicCheck();
        }
        
        // Step 5: Check if overlay is shown
        const overlay = document.getElementById('authOverlay');
        if (overlay && overlay.style.display !== 'none') {
            console.log('[OK] Auth overlay is showing - expiration detected correctly');
        } else {
            console.log('[ERROR] Auth overlay not showing - check periodic check logic');
        }
        
        return true;
    }
    
    /**
     * Show help/usage information
     */
    showHelp() {
        console.log(`
🧪 TokenExpirationTester Usage:

// Simulate tokens expiring in 10 seconds
TokenExpirationTester.simulateExpiration(10)

// Force immediate expiration
TokenExpirationTester.forceExpireNow()

// Check current token status
TokenExpirationTester.checkStatus()

// Test complete expiration flow
await TokenExpirationTester.testExpirationFlow(15)

// Restore original tokens
TokenExpirationTester.restoreOriginalTokens()

// Show this help
TokenExpirationTester.showHelp()
        `);
    }
}

// Create global instance
window.TokenExpirationTester = new TokenExpirationTester();

// Add convenient global functions
window.testTokenExpiration = (seconds = 10) => window.TokenExpirationTester.simulateExpiration(seconds);
window.forceTokenExpiration = () => window.TokenExpirationTester.forceExpireNow();
window.checkTokenStatus = () => window.TokenExpirationTester.checkStatus();

console.log('🧪 TokenExpirationTester loaded! Use window.TokenExpirationTester or global functions like testTokenExpiration(10)');
