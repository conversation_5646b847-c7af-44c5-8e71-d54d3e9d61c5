/**
 * Test Configuration Manager for Enhanced Authentication Flow
 * 
 * This script provides utilities to easily switch between valid and invalid
 * MSAL configurations for testing the 8 authentication scenarios.
 */

(function(window) {
    'use strict';

    const TestConfigs = {
        
        // Valid configuration for testing
        validConfig: {
            clientId: "12345678-1234-1234-1234-123456789abc",
            authority: "https://login.microsoftonline.com/your-tenant-id",
            redirectUri: window.location.origin + window.location.pathname
        },
        
        // Invalid configuration (current placeholder values)
        invalidConfig: {
            clientId: "Enter_the_Application_Id_Here",
            authority: "https://login.microsoftonline.com/Enter_the_Tenant_ID_Here",
            redirectUri: window.location.origin + window.location.pathname
        },
        
        /**
         * Set valid MSAL configuration for testing
         */
        setValidConfig: function() {
            if (typeof window.msalConfig === 'undefined') {
                window.msalConfig = { cache: { cacheLocation: 'sessionStorage', storeAuthStateInCookie: false } };
            }
            window.msalConfig.auth = { ...this.validConfig };
            console.log('TestConfigs: Set valid configuration');
            this.logCurrentConfig();
        },
        
        /**
         * Set invalid MSAL configuration for testing
         */
        setInvalidConfig: function() {
            if (typeof window.msalConfig === 'undefined') {
                window.msalConfig = { cache: { cacheLocation: 'sessionStorage', storeAuthStateInCookie: false } };
            }
            window.msalConfig.auth = { ...this.invalidConfig };
            console.log('TestConfigs: Set invalid configuration');
            this.logCurrentConfig();
        },
        
        /**
         * Log current configuration state
         */
        logCurrentConfig: function() {
            if (window.msalConfig && window.msalConfig.auth) {
                console.log('TestConfigs: Current config:', {
                    clientId: window.msalConfig.auth.clientId,
                    authority: window.msalConfig.auth.authority,
                    isValid: this.isValidConfig()
                });
            } else {
                console.log('TestConfigs: No MSAL configuration found');
            }
        },
        
        /**
         * Check if current configuration is valid
         */
        isValidConfig: function() {
            if (!window.msalConfig || !window.msalConfig.auth) {
                return false;
            }
            
            const auth = window.msalConfig.auth;
            return auth.clientId !== "Enter_the_Application_Id_Here" && 
                   auth.authority !== "https://login.microsoftonline.com/Enter_the_Tenant_ID_Here" &&
                   /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(auth.clientId);
        },
        
        /**
         * Set mock localStorage data for testing
         */
        setMockLocalStorage: function() {
            const mockProfile = {
                account: "CORP\\testuser",
                userName: "Test User",
                email: "<EMAIL>",
                version: "1.0"
            };
            
            localStorage.setItem('UserProfile', JSON.stringify(mockProfile));
            localStorage.setItem('msal.username', 'CORP\\testuser');
            localStorage.setItem('msal.accessToken', 'mock-access-token-' + Date.now());
            localStorage.setItem('msal.idToken', 'mock-id-token-' + Date.now());
            
            console.log('TestConfigs: Set mock localStorage data');
        },
        
        /**
         * Clear all authentication localStorage data
         */
        clearAuthStorage: function() {
            const keys = ['UserProfile', 'msal.username', 'msal.accessToken', 'msal.idToken', 'msal.account'];
            keys.forEach(key => {
                localStorage.removeItem(key);
                sessionStorage.removeItem(key);
            });
            
            if (window.UserProfile) {
                window.UserProfile = {};
            }
            
            console.log('TestConfigs: Cleared authentication storage');
        },
        
        /**
         * Get current authentication state
         */
        getAuthState: function() {
            const hasConfig = !!window.msalConfig;
            const isValidConfig = this.isValidConfig();
            const hasUserProfile = localStorage.getItem('UserProfile') && localStorage.getItem('UserProfile') !== 'null';
            const hasUsername = localStorage.getItem('msal.username') && localStorage.getItem('msal.username') !== 'null';
            const hasToken = localStorage.getItem('msal.accessToken') && localStorage.getItem('msal.accessToken') !== 'null';
            
            return {
                hasConfig,
                isValidConfig,
                hasUserProfile,
                hasUsername,
                hasToken,
                hasAnyAuth: hasUserProfile || hasUsername
            };
        },
        
        /**
         * Run a specific test scenario
         */
        runScenario: function(scenarioNumber) {
            console.log(`TestConfigs: ========== RUNNING SCENARIO ${scenarioNumber} ==========`);
            
            const scenarios = {
                1: () => {
                    // First visit, valid JSON, no localStorage → see overlay then login panel
                    console.log('Scenario 1: First visit with valid JSON, no localStorage');
                    this.setValidConfig();
                    this.clearAuthStorage();
                    console.log('Expected: Show overlay + login panel');
                },
                2: () => {
                    // Revisit, has localStorage → no overlay, no login panel
                    console.log('Scenario 2: Revisit with valid JSON and localStorage');
                    this.setValidConfig();
                    this.setMockLocalStorage();
                    console.log('Expected: No overlay');
                },
                3: () => {
                    // Change to invalid, refresh → should not see overlay and clear localStorage
                    console.log('Scenario 3: Change JSON to invalid, refresh');
                    this.setInvalidConfig();
                    console.log('Expected: Clear localStorage and no overlay');
                },
                4: () => {
                    // Refresh with invalid → should not see overlay and clear localStorage
                    console.log('Scenario 4: Refresh with invalid JSON');
                    this.setInvalidConfig();
                    console.log('Expected: No overlay and clear localStorage');
                },
                5: () => {
                    // Change to valid, refresh → should see overlay and login panel
                    console.log('Scenario 5: Change JSON to valid, refresh');
                    this.setValidConfig();
                    this.clearAuthStorage();
                    console.log('Expected: Show overlay and login panel');
                },
                6: () => {
                    // Refresh with valid + localStorage → should not see overlay
                    console.log('Scenario 6: Refresh with valid JSON and localStorage');
                    this.setValidConfig();
                    this.setMockLocalStorage();
                    console.log('Expected: No overlay');
                },
                7: () => {
                    // Change back to invalid → clear localStorage, no overlay
                    console.log('Scenario 7: Change JSON back to invalid');
                    this.setInvalidConfig();
                    console.log('Expected: Clear localStorage and no overlay');
                },
                8: () => {
                    // Refresh with invalid → clear localStorage, no overlay
                    console.log('Scenario 8: Refresh with invalid JSON');
                    this.setInvalidConfig();
                    console.log('Expected: Clear localStorage and no overlay');
                }
            };
            
            if (scenarios[scenarioNumber]) {
                scenarios[scenarioNumber]();
                
                // Trigger authentication check if available
                setTimeout(() => {
                    if (window.EnhancedAuthFlow && typeof window.EnhancedAuthFlow.checkAuthenticationScenarios === 'function') {
                        console.log('TestConfigs: Triggering EnhancedAuthFlow check...');
                        const result = window.EnhancedAuthFlow.checkAuthenticationScenarios();
                        console.log('TestConfigs: Result:', result);
                    } else if (window.checkAuthenticationScenarios && typeof window.checkAuthenticationScenarios === 'function') {
                        console.log('TestConfigs: Triggering local authentication check...');
                        const result = window.checkAuthenticationScenarios();
                        console.log('TestConfigs: Result:', result);
                    } else {
                        console.log('TestConfigs: No authentication flow available to trigger');
                    }
                    
                    // Log final state
                    const state = this.getAuthState();
                    console.log('TestConfigs: Final state:', state);
                }, 100);
                
            } else {
                console.error('TestConfigs: Unknown scenario:', scenarioNumber);
            }
        },
        
        /**
         * Test all scenarios in sequence
         */
        runAllScenarios: function() {
            console.log('TestConfigs: Running all 8 scenarios...');
            for (let i = 1; i <= 8; i++) {
                setTimeout(() => {
                    this.runScenario(i);
                }, i * 2000); // 2 second delay between scenarios
            }
        }
    };

    // Expose to global scope
    window.TestConfigs = TestConfigs;
    
    // Add helper functions to console for easy testing
    console.log('TestConfigs: Available commands:');
    console.log('  TestConfigs.runScenario(1-8) - Run specific scenario');
    console.log('  TestConfigs.runAllScenarios() - Run all scenarios');
    console.log('  TestConfigs.setValidConfig() - Set valid configuration');
    console.log('  TestConfigs.setInvalidConfig() - Set invalid configuration');
    console.log('  TestConfigs.setMockLocalStorage() - Add mock auth data');
    console.log('  TestConfigs.clearAuthStorage() - Clear auth data');
    console.log('  TestConfigs.getAuthState() - Get current state');

})(window);